using System.Collections.Generic;
using System.Globalization;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التوطين
    /// Localization service interface
    /// </summary>
    public interface ILocalizationService
    {
        /// <summary>
        /// الحصول على النص المترجم
        /// Get localized text
        /// </summary>
        string GetLocalizedString(string key, string? language = null);

        /// <summary>
        /// الحصول على النص المترجم مع معاملات
        /// Get localized text with parameters
        /// </summary>
        string GetLocalizedString(string key, object[] args, string? language = null);

        /// <summary>
        /// تعيين اللغة الحالية
        /// Set current language
        /// </summary>
        void SetCurrentLanguage(string language);

        /// <summary>
        /// الحصول على اللغة الحالية
        /// Get current language
        /// </summary>
        string GetCurrentLanguage();

        /// <summary>
        /// الحصول على قائمة اللغات المدعومة
        /// Get supported languages
        /// </summary>
        IEnumerable<CultureInfo> GetSupportedLanguages();

        /// <summary>
        /// التحقق من دعم اللغة
        /// Check if language is supported
        /// </summary>
        bool IsLanguageSupported(string language);

        /// <summary>
        /// الحصول على اتجاه النص
        /// Get text direction
        /// </summary>
        string GetTextDirection(string? language = null);

        /// <summary>
        /// التحقق من كون اللغة من اليمين لليسار
        /// Check if language is right-to-left
        /// </summary>
        bool IsRightToLeft(string? language = null);

        /// <summary>
        /// تنسيق التاريخ حسب اللغة
        /// Format date by language
        /// </summary>
        string FormatDate(DateTime date, string? language = null);

        /// <summary>
        /// تنسيق الوقت حسب اللغة
        /// Format time by language
        /// </summary>
        string FormatTime(DateTime time, string? language = null);

        /// <summary>
        /// تنسيق التاريخ والوقت حسب اللغة
        /// Format datetime by language
        /// </summary>
        string FormatDateTime(DateTime dateTime, string? language = null);

        /// <summary>
        /// تنسيق الرقم حسب اللغة
        /// Format number by language
        /// </summary>
        string FormatNumber(decimal number, string? language = null);

        /// <summary>
        /// تنسيق العملة حسب اللغة
        /// Format currency by language
        /// </summary>
        string FormatCurrency(decimal amount, string? language = null);

        /// <summary>
        /// تحميل ملفات الترجمة
        /// Load translation files
        /// </summary>
        Task LoadTranslationsAsync();

        /// <summary>
        /// إعادة تحميل ملفات الترجمة
        /// Reload translation files
        /// </summary>
        Task ReloadTranslationsAsync();
    }
}
