using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان تاريخ الاتصال مع العميل
    /// Client contact history entity
    /// </summary>
    [Table("ClientContacts")]
    public class ClientContact : BaseEntity
    {
        [Required]
        [Column("ClientId")]
        public int ClientId { get; set; }

        [Column("CaseId")]
        public int? CaseId { get; set; }

        [Required]
        [Column("ContactDate")]
        public DateTime ContactDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        [Column("ContactType")]
        public string ContactType { get; set; } = "مكالمة"; // مكالمة، بريد إلكتروني، رسالة، اجتماع، زيارة

        [StringLength(50)]
        [Column("ContactTypeFr")]
        public string? ContactTypeFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Direction")]
        public string Direction { get; set; } = "صادر"; // صادر، وارد

        [StringLength(50)]
        [Column("DirectionFr")]
        public string? DirectionFr { get; set; }

        [StringLength(200)]
        [Column("Subject")]
        public string? Subject { get; set; }

        [StringLength(200)]
        [Column("SubjectFr")]
        public string? SubjectFr { get; set; }

        [StringLength(2000)]
        [Column("Summary")]
        public string? Summary { get; set; }

        [StringLength(2000)]
        [Column("SummaryFr")]
        public string? SummaryFr { get; set; }

        [Column("Details")]
        public string? Details { get; set; }

        [Column("DetailsFr")]
        public string? DetailsFr { get; set; }

        [Column("Duration")]
        public int? Duration { get; set; } // بالدقائق

        [StringLength(50)]
        [Column("ContactMethod")]
        public string? ContactMethod { get; set; } // هاتف، جوال، بريد إلكتروني، واتساب

        [StringLength(50)]
        [Column("ContactMethodFr")]
        public string? ContactMethodFr { get; set; }

        [StringLength(100)]
        [Column("ContactNumber")]
        public string? ContactNumber { get; set; }

        [StringLength(100)]
        [Column("ContactEmail")]
        public string? ContactEmail { get; set; }

        [StringLength(200)]
        [Column("Location")]
        public string? Location { get; set; }

        [StringLength(200)]
        [Column("LocationFr")]
        public string? LocationFr { get; set; }

        [Column("ContactedBy")]
        public int? ContactedBy { get; set; }

        [StringLength(100)]
        [Column("ContactedByName")]
        public string? ContactedByName { get; set; }

        [StringLength(100)]
        [Column("ContactedByNameFr")]
        public string? ContactedByNameFr { get; set; }

        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مكتمل"; // مجدول، جاري، مكتمل، ملغي

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [StringLength(2000)]
        [Column("Outcome")]
        public string? Outcome { get; set; }

        [StringLength(2000)]
        [Column("OutcomeFr")]
        public string? OutcomeFr { get; set; }

        [StringLength(2000)]
        [Column("ActionItems")]
        public string? ActionItems { get; set; }

        [StringLength(2000)]
        [Column("ActionItemsFr")]
        public string? ActionItemsFr { get; set; }

        [Column("FollowUpRequired")]
        public bool FollowUpRequired { get; set; } = false;

        [Column("FollowUpDate")]
        public DateTime? FollowUpDate { get; set; }

        [StringLength(500)]
        [Column("FollowUpNotes")]
        public string? FollowUpNotes { get; set; }

        [StringLength(500)]
        [Column("FollowUpNotesFr")]
        public string? FollowUpNotesFr { get; set; }

        [Column("FollowUpCompleted")]
        public bool FollowUpCompleted { get; set; } = false;

        [Column("FollowUpCompletedDate")]
        public DateTime? FollowUpCompletedDate { get; set; }

        [Column("IsConfidential")]
        public bool IsConfidential { get; set; } = false;

        [Column("IsBillable")]
        public bool IsBillable { get; set; } = false;

        [Column("BillingRate")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? BillingRate { get; set; }

        [Column("BillableAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? BillableAmount { get; set; }

        [Column("IsInvoiced")]
        public bool IsInvoiced { get; set; } = false;

        [Column("InvoiceId")]
        public int? InvoiceId { get; set; }

        [StringLength(500)]
        [Column("AttachmentPaths")]
        public string? AttachmentPaths { get; set; } // JSON array

        [Column("HasAttachments")]
        public bool HasAttachments { get; set; } = false;

        [Column("AttachmentCount")]
        public int AttachmentCount { get; set; } = 0;

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [Column("IsTemplate")]
        public bool IsTemplate { get; set; } = false;

        [StringLength(100)]
        [Column("TemplateName")]
        public string? TemplateName { get; set; }

        [StringLength(100)]
        [Column("TemplateNameFr")]
        public string? TemplateNameFr { get; set; }

        [Column("TemplateId")]
        public int? TemplateId { get; set; }

        [Column("IsSystemGenerated")]
        public bool IsSystemGenerated { get; set; } = false;

        [StringLength(100)]
        [Column("SystemSource")]
        public string? SystemSource { get; set; }

        [Column("RelatedEntityType")]
        public string? RelatedEntityType { get; set; }

        [Column("RelatedEntityId")]
        public int? RelatedEntityId { get; set; }

        [StringLength(200)]
        [Column("RelatedEntityName")]
        public string? RelatedEntityName { get; set; }

        [Column("IsArchived")]
        public bool IsArchived { get; set; } = false;

        [Column("ArchiveDate")]
        public DateTime? ArchiveDate { get; set; }

        [Column("ReminderDate")]
        public DateTime? ReminderDate { get; set; }

        [Column("ReminderSent")]
        public bool ReminderSent { get; set; } = false;

        [Column("ReminderCount")]
        public int ReminderCount { get; set; } = 0;

        [Column("LastReminderDate")]
        public DateTime? LastReminderDate { get; set; }

        // Navigation Properties
        [ForeignKey("ClientId")]
        public virtual Client Client { get; set; } = null!;

        [ForeignKey("CaseId")]
        public virtual LegalCase? Case { get; set; }

        [ForeignKey("ContactedBy")]
        public virtual User? ContactedByUser { get; set; }

        [ForeignKey("InvoiceId")]
        public virtual Invoice? Invoice { get; set; }

        [ForeignKey("TemplateId")]
        public virtual ClientContact? Template { get; set; }

        /// <summary>
        /// الحصول على نوع الاتصال حسب اللغة
        /// Get contact type by language
        /// </summary>
        public string GetContactType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ContactTypeFr) 
                ? ContactTypeFr 
                : ContactType;
        }

        /// <summary>
        /// الحصول على اتجاه الاتصال حسب اللغة
        /// Get contact direction by language
        /// </summary>
        public string GetDirection(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DirectionFr) 
                ? DirectionFr 
                : Direction;
        }

        /// <summary>
        /// الحصول على الموضوع حسب اللغة
        /// Get subject by language
        /// </summary>
        public string GetSubject(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(SubjectFr) 
                ? SubjectFr 
                : Subject ?? string.Empty;
        }

        /// <summary>
        /// الحصول على الملخص حسب اللغة
        /// Get summary by language
        /// </summary>
        public string GetSummary(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(SummaryFr) 
                ? SummaryFr 
                : Summary ?? string.Empty;
        }

        /// <summary>
        /// الحصول على التفاصيل حسب اللغة
        /// Get details by language
        /// </summary>
        public string GetDetails(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DetailsFr) 
                ? DetailsFr 
                : Details ?? string.Empty;
        }

        /// <summary>
        /// الحصول على حالة الاتصال حسب اللغة
        /// Get contact status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// التحقق من كون الاتصال صادر
        /// Check if contact is outgoing
        /// </summary>
        public bool IsOutgoing()
        {
            return Direction == "صادر" || Direction == "Outgoing";
        }

        /// <summary>
        /// التحقق من كون الاتصال وارد
        /// Check if contact is incoming
        /// </summary>
        public bool IsIncoming()
        {
            return Direction == "وارد" || Direction == "Incoming";
        }

        /// <summary>
        /// التحقق من اكتمال الاتصال
        /// Check if contact is completed
        /// </summary>
        public bool IsCompleted()
        {
            return Status == "مكتمل" || Status == "Completed";
        }

        /// <summary>
        /// التحقق من إلغاء الاتصال
        /// Check if contact is cancelled
        /// </summary>
        public bool IsCancelled()
        {
            return Status == "ملغي" || Status == "Cancelled";
        }

        /// <summary>
        /// التحقق من تأخر المتابعة
        /// Check if follow-up is overdue
        /// </summary>
        public bool IsFollowUpOverdue()
        {
            return FollowUpRequired && 
                   FollowUpDate.HasValue && 
                   FollowUpDate.Value < DateTime.Now && 
                   !FollowUpCompleted;
        }

        /// <summary>
        /// الحصول على مدة الاتصال بصيغة قابلة للقراءة
        /// Get duration in readable format
        /// </summary>
        public string GetDurationFormatted(string language = "ar")
        {
            if (!Duration.HasValue) return language == "fr" ? "Non spécifié" : "غير محدد";
            
            var minutes = Duration.Value;
            if (minutes < 60)
            {
                return language == "fr" ? $"{minutes} minutes" : $"{minutes} دقيقة";
            }
            else
            {
                var hours = minutes / 60;
                var remainingMinutes = minutes % 60;
                if (remainingMinutes == 0)
                {
                    return language == "fr" ? $"{hours} heures" : $"{hours} ساعة";
                }
                else
                {
                    return language == "fr" 
                        ? $"{hours} heures {remainingMinutes} minutes"
                        : $"{hours} ساعة {remainingMinutes} دقيقة";
                }
            }
        }

        /// <summary>
        /// إكمال المتابعة
        /// Complete follow-up
        /// </summary>
        public void CompleteFollowUp()
        {
            FollowUpCompleted = true;
            FollowUpCompletedDate = DateTime.Now;
        }

        /// <summary>
        /// حساب المبلغ القابل للفوترة
        /// Calculate billable amount
        /// </summary>
        public void CalculateBillableAmount()
        {
            if (IsBillable && Duration.HasValue && BillingRate.HasValue)
            {
                var hours = Duration.Value / 60.0m;
                BillableAmount = hours * BillingRate.Value;
            }
            else
            {
                BillableAmount = null;
            }
        }

        /// <summary>
        /// إرسال تذكير
        /// Send reminder
        /// </summary>
        public void SendReminder()
        {
            ReminderSent = true;
            ReminderCount++;
            LastReminderDate = DateTime.Now;
        }

        /// <summary>
        /// أرشفة الاتصال
        /// Archive contact
        /// </summary>
        public void ArchiveContact()
        {
            IsArchived = true;
            ArchiveDate = DateTime.Now;
        }

        /// <summary>
        /// استعادة الاتصال من الأرشيف
        /// Restore contact from archive
        /// </summary>
        public void RestoreContact()
        {
            IsArchived = false;
            ArchiveDate = null;
        }

        /// <summary>
        /// إنشاء اتصال من قالب
        /// Create contact from template
        /// </summary>
        public static ClientContact CreateFromTemplate(ClientContact template, int clientId, int? caseId = null)
        {
            return new ClientContact
            {
                ClientId = clientId,
                CaseId = caseId,
                ContactType = template.ContactType,
                ContactTypeFr = template.ContactTypeFr,
                Subject = template.Subject,
                SubjectFr = template.SubjectFr,
                ContactMethod = template.ContactMethod,
                ContactMethodFr = template.ContactMethodFr,
                Duration = template.Duration,
                IsBillable = template.IsBillable,
                BillingRate = template.BillingRate,
                TemplateId = template.Id,
                IsTemplate = false
            };
        }
    }
}
