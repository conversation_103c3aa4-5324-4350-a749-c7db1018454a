@echo off
echo ========================================
echo نشر نظام إدارة مكتب المحاماة
echo Publishing Law Firm Management System
echo ========================================
echo.

echo اختر نوع النشر:
echo Choose publish type:
echo 1. نشر للتوزيع (Self-contained)
echo 2. نشر يتطلب .NET Runtime
echo 3. نشر محمول (Portable)
echo 4. إنشاء حزمة التثبيت (Installer)
echo.

set /p CHOICE="اختر رقم (1-4) / Choose number (1-4): "

if "%CHOICE%"=="1" goto self_contained
if "%CHOICE%"=="2" goto framework_dependent
if "%CHOICE%"=="3" goto portable
if "%CHOICE%"=="4" goto installer
goto invalid_choice

:self_contained
echo نشر النسخة المستقلة...
echo Publishing self-contained version...
echo.

set OUTPUT_DIR=publish\self-contained
set RUNTIME=win-x64

echo تنظيف المجلد السابق...
echo Cleaning previous output...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo بناء ونشر التطبيق...
echo Building and publishing application...
dotnet publish -c Release -r %RUNTIME% --self-contained true -o "%OUTPUT_DIR%" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true

if %errorlevel% neq 0 (
    echo خطأ في النشر
    echo Error during publishing
    goto end
)

echo نسخ الملفات الإضافية...
echo Copying additional files...
copy "appsettings.json" "%OUTPUT_DIR%\"
copy "README.md" "%OUTPUT_DIR%\"
copy "LICENSE" "%OUTPUT_DIR%\"
copy "INSTALLATION.md" "%OUTPUT_DIR%\"

echo إنشاء ملف التشغيل...
echo Creating run script...
echo @echo off > "%OUTPUT_DIR%\run.bat"
echo echo Starting Law Firm Management System... >> "%OUTPUT_DIR%\run.bat"
echo LawFirmManagementSystem.exe >> "%OUTPUT_DIR%\run.bat"
echo pause >> "%OUTPUT_DIR%\run.bat"

goto publish_complete

:framework_dependent
echo نشر النسخة التي تتطلب .NET Runtime...
echo Publishing framework-dependent version...
echo.

set OUTPUT_DIR=publish\framework-dependent

echo تنظيف المجلد السابق...
echo Cleaning previous output...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo بناء ونشر التطبيق...
echo Building and publishing application...
dotnet publish -c Release -o "%OUTPUT_DIR%"

if %errorlevel% neq 0 (
    echo خطأ في النشر
    echo Error during publishing
    goto end
)

echo نسخ الملفات الإضافية...
echo Copying additional files...
copy "appsettings.json" "%OUTPUT_DIR%\"
copy "README.md" "%OUTPUT_DIR%\"
copy "LICENSE" "%OUTPUT_DIR%\"
copy "INSTALLATION.md" "%OUTPUT_DIR%\"

echo إنشاء ملف التشغيل...
echo Creating run script...
echo @echo off > "%OUTPUT_DIR%\run.bat"
echo echo Starting Law Firm Management System... >> "%OUTPUT_DIR%\run.bat"
echo dotnet LawFirmManagementSystem.dll >> "%OUTPUT_DIR%\run.bat"
echo pause >> "%OUTPUT_DIR%\run.bat"

goto publish_complete

:portable
echo نشر النسخة المحمولة...
echo Publishing portable version...
echo.

set OUTPUT_DIR=publish\portable

echo تنظيف المجلد السابق...
echo Cleaning previous output...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo بناء ونشر التطبيق...
echo Building and publishing application...
dotnet publish -c Release -o "%OUTPUT_DIR%" --no-self-contained

if %errorlevel% neq 0 (
    echo خطأ في النشر
    echo Error during publishing
    goto end
)

echo نسخ الملفات الإضافية...
echo Copying additional files...
copy "appsettings.json" "%OUTPUT_DIR%\"
copy "README.md" "%OUTPUT_DIR%\"
copy "LICENSE" "%OUTPUT_DIR%\"
copy "INSTALLATION.md" "%OUTPUT_DIR%\"

echo إنشاء ملف التشغيل...
echo Creating run script...
echo @echo off > "%OUTPUT_DIR%\run.bat"
echo echo Starting Law Firm Management System... >> "%OUTPUT_DIR%\run.bat"
echo echo Checking .NET Runtime... >> "%OUTPUT_DIR%\run.bat"
echo dotnet --version ^>nul 2^>^&1 >> "%OUTPUT_DIR%\run.bat"
echo if %%errorlevel%% neq 0 ( >> "%OUTPUT_DIR%\run.bat"
echo     echo .NET Runtime not found! >> "%OUTPUT_DIR%\run.bat"
echo     echo Please install .NET 8.0 Runtime >> "%OUTPUT_DIR%\run.bat"
echo     pause >> "%OUTPUT_DIR%\run.bat"
echo     exit /b 1 >> "%OUTPUT_DIR%\run.bat"
echo ^) >> "%OUTPUT_DIR%\run.bat"
echo dotnet LawFirmManagementSystem.dll >> "%OUTPUT_DIR%\run.bat"
echo pause >> "%OUTPUT_DIR%\run.bat"

goto publish_complete

:installer
echo إنشاء حزمة التثبيت...
echo Creating installer package...
echo.

:: First create self-contained version
set OUTPUT_DIR=publish\installer-source
set RUNTIME=win-x64

echo تنظيف المجلد السابق...
echo Cleaning previous output...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo بناء ونشر التطبيق...
echo Building and publishing application...
dotnet publish -c Release -r %RUNTIME% --self-contained true -o "%OUTPUT_DIR%"

if %errorlevel% neq 0 (
    echo خطأ في النشر
    echo Error during publishing
    goto end
)

echo نسخ الملفات الإضافية...
echo Copying additional files...
copy "appsettings.json" "%OUTPUT_DIR%\"
copy "README.md" "%OUTPUT_DIR%\"
copy "LICENSE" "%OUTPUT_DIR%\"
copy "INSTALLATION.md" "%OUTPUT_DIR%\"

echo إنشاء ملف الإعداد...
echo Creating setup script...
echo ; Law Firm Management System Installer > "%OUTPUT_DIR%\setup.iss"
echo [Setup] >> "%OUTPUT_DIR%\setup.iss"
echo AppName=Law Firm Management System >> "%OUTPUT_DIR%\setup.iss"
echo AppVersion=1.0.0 >> "%OUTPUT_DIR%\setup.iss"
echo DefaultDirName={pf}\LawFirmManagementSystem >> "%OUTPUT_DIR%\setup.iss"
echo DefaultGroupName=Law Firm Management System >> "%OUTPUT_DIR%\setup.iss"
echo OutputDir=..\installer >> "%OUTPUT_DIR%\setup.iss"
echo OutputBaseFilename=LawFirmManagementSystem-Setup >> "%OUTPUT_DIR%\setup.iss"
echo [Files] >> "%OUTPUT_DIR%\setup.iss"
echo Source: "*"; DestDir: "{app}"; Flags: recursesubdirs >> "%OUTPUT_DIR%\setup.iss"
echo [Icons] >> "%OUTPUT_DIR%\setup.iss"
echo Name: "{group}\Law Firm Management System"; Filename: "{app}\LawFirmManagementSystem.exe" >> "%OUTPUT_DIR%\setup.iss"

echo ملاحظة: لإنشاء ملف التثبيت، تحتاج إلى Inno Setup
echo Note: To create installer file, you need Inno Setup
echo يمكن تحميله من: https://jrsoftware.org/isinfo.php
echo Download from: https://jrsoftware.org/isinfo.php

goto publish_complete

:publish_complete
echo ========================================
echo تم النشر بنجاح!
echo Publishing completed successfully!
echo ========================================
echo.

echo مجلد الإخراج: %OUTPUT_DIR%
echo Output directory: %OUTPUT_DIR%
echo.

echo حجم الملفات:
echo File sizes:
dir "%OUTPUT_DIR%" /s /-c | find "bytes"
echo.

echo الملفات المنشورة:
echo Published files:
dir "%OUTPUT_DIR%" /b
echo.

echo للاختبار، قم بتشغيل:
echo To test, run:
echo %OUTPUT_DIR%\run.bat
echo.

echo لإنشاء أرشيف ZIP:
echo To create ZIP archive:
set /p CREATE_ZIP="هل تريد إنشاء ملف ZIP؟ (y/n) / Create ZIP file? (y/n): "
if /i "%CREATE_ZIP%"=="y" (
    echo إنشاء ملف ZIP...
    echo Creating ZIP file...
    powershell -command "Compress-Archive -Path '%OUTPUT_DIR%\*' -DestinationPath 'LawFirmManagementSystem-v1.0.0.zip' -Force"
    echo تم إنشاء: LawFirmManagementSystem-v1.0.0.zip
    echo Created: LawFirmManagementSystem-v1.0.0.zip
)

goto end

:invalid_choice
echo اختيار غير صحيح
echo Invalid choice

:end
echo.
pause
