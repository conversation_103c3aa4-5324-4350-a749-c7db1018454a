using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان المحكمة
    /// Court entity
    /// </summary>
    [Table("Courts")]
    public class Court : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("CourtName")]
        public string CourtName { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("CourtNameFr")]
        public string? CourtNameFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("CourtType")]
        public string CourtType { get; set; } = string.Empty; // ابتدائية، استئناف، نقض، تجارية، إدارية

        [StringLength(50)]
        [Column("CourtTypeFr")]
        public string? CourtTypeFr { get; set; }

        [Required]
        [StringLength(100)]
        [Column("Jurisdiction")]
        public string Jurisdiction { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("JurisdictionFr")]
        public string? JurisdictionFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("City")]
        public string City { get; set; } = string.Empty;

        [StringLength(50)]
        [Column("CityFr")]
        public string? CityFr { get; set; }

        [StringLength(50)]
        [Column("Province")]
        public string? Province { get; set; }

        [StringLength(50)]
        [Column("ProvinceFr")]
        public string? ProvinceFr { get; set; }

        [StringLength(200)]
        [Column("Address")]
        public string? Address { get; set; }

        [StringLength(200)]
        [Column("AddressFr")]
        public string? AddressFr { get; set; }

        [StringLength(10)]
        [Column("PostalCode")]
        public string? PostalCode { get; set; }

        [StringLength(20)]
        [Column("Phone")]
        public string? Phone { get; set; }

        [StringLength(20)]
        [Column("Fax")]
        public string? Fax { get; set; }

        [StringLength(100)]
        [EmailAddress]
        [Column("Email")]
        public string? Email { get; set; }

        [StringLength(200)]
        [Column("Website")]
        public string? Website { get; set; }

        [StringLength(100)]
        [Column("ChiefJudge")]
        public string? ChiefJudge { get; set; }

        [StringLength(100)]
        [Column("ChiefJudgeFr")]
        public string? ChiefJudgeFr { get; set; }

        [StringLength(100)]
        [Column("Registrar")]
        public string? Registrar { get; set; }

        [StringLength(100)]
        [Column("RegistrarFr")]
        public string? RegistrarFr { get; set; }

        [StringLength(50)]
        [Column("WorkingHours")]
        public string? WorkingHours { get; set; }

        [StringLength(50)]
        [Column("WorkingHoursFr")]
        public string? WorkingHoursFr { get; set; }

        [StringLength(200)]
        [Column("WorkingDays")]
        public string? WorkingDays { get; set; }

        [StringLength(200)]
        [Column("WorkingDaysFr")]
        public string? WorkingDaysFr { get; set; }

        [Column("IsElectronic")]
        public bool IsElectronic { get; set; } = false;

        [StringLength(200)]
        [Column("ElectronicFilingUrl")]
        public string? ElectronicFilingUrl { get; set; }

        [StringLength(100)]
        [Column("ElectronicFilingUsername")]
        public string? ElectronicFilingUsername { get; set; }

        [StringLength(255)]
        [Column("ElectronicFilingPassword")]
        public string? ElectronicFilingPassword { get; set; }

        [Column("FilingFee")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? FilingFee { get; set; }

        [Column("ServiceFee")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ServiceFee { get; set; }

        [Column("CopyFee")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CopyFee { get; set; }

        [StringLength(500)]
        [Column("SpecialInstructions")]
        public string? SpecialInstructions { get; set; }

        [StringLength(500)]
        [Column("SpecialInstructionsFr")]
        public string? SpecialInstructionsFr { get; set; }

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; } = "#6c757d";

        [StringLength(50)]
        [Column("Icon")]
        public string? Icon { get; set; }

        // Navigation Properties
        public virtual ICollection<LegalCase> Cases { get; set; } = new List<LegalCase>();
        public virtual ICollection<Hearing> Hearings { get; set; } = new List<Hearing>();

        /// <summary>
        /// الحصول على اسم المحكمة حسب اللغة
        /// Get court name by language
        /// </summary>
        public string GetCourtName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(CourtNameFr) 
                ? CourtNameFr 
                : CourtName;
        }

        /// <summary>
        /// الحصول على نوع المحكمة حسب اللغة
        /// Get court type by language
        /// </summary>
        public string GetCourtType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(CourtTypeFr) 
                ? CourtTypeFr 
                : CourtType;
        }

        /// <summary>
        /// الحصول على الاختصاص حسب اللغة
        /// Get jurisdiction by language
        /// </summary>
        public string GetJurisdiction(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(JurisdictionFr) 
                ? JurisdictionFr 
                : Jurisdiction;
        }

        /// <summary>
        /// الحصول على العنوان حسب اللغة
        /// Get address by language
        /// </summary>
        public string GetAddress(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(AddressFr) 
                ? AddressFr 
                : Address ?? string.Empty;
        }

        /// <summary>
        /// الحصول على العنوان الكامل
        /// Get full address
        /// </summary>
        public string GetFullAddress(string language = "ar")
        {
            var address = GetAddress(language);
            var city = language.ToLower() == "fr" && !string.IsNullOrEmpty(CityFr) ? CityFr : City;
            var province = language.ToLower() == "fr" && !string.IsNullOrEmpty(ProvinceFr) ? ProvinceFr : Province;

            var parts = new List<string>();
            if (!string.IsNullOrEmpty(address)) parts.Add(address);
            if (!string.IsNullOrEmpty(city)) parts.Add(city);
            if (!string.IsNullOrEmpty(province)) parts.Add(province);
            if (!string.IsNullOrEmpty(PostalCode)) parts.Add(PostalCode);

            return string.Join(", ", parts);
        }
    }
}
