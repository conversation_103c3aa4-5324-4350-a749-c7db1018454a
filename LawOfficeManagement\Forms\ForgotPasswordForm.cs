using System;
using System.Drawing;
using System.Windows.Forms;
using LawOfficeManagement.Localization;
using LawOfficeManagement.Services;

namespace LawOfficeManagement.Forms
{
    public partial class ForgotPasswordForm : Form
    {
        private readonly LanguageManager _languageManager;
        private readonly AuthenticationService _authService;
        private string _generatedOTP = string.Empty;
        private DateTime _otpExpiry;
        
        public ForgotPasswordForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            _authService = new AuthenticationService();
            
            InitializeForm();
            ApplyTheme();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("ForgotPassword");
            this.Size = new Size(400, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void ApplyTheme()
        {
            var primaryColor = Color.FromArgb(25, 42, 86);
            var secondaryColor = Color.FromArgb(218, 165, 32);
            var backgroundColor = Color.FromArgb(248, 249, 250);
            
            this.BackColor = backgroundColor;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.BackColor = primaryColor;
                    button.ForeColor = Color.White;
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
                else if (control is TextBox textBox)
                {
                    textBox.BorderStyle = BorderStyle.FixedSingle;
                    textBox.Font = new Font("Segoe UI", 10F);
                }
            }
        }
        
        private async void btnSendOTP_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show(_languageManager.GetString("RequiredField") + ": " + _languageManager.GetString("Username"),
                    _languageManager.GetString("ValidationError"),
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            try
            {
                btnSendOTP.Enabled = false;
                btnSendOTP.Text = "جاري الإرسال...";
                
                // توليد رمز OTP
                _generatedOTP = GenerateOTP();
                _otpExpiry = DateTime.Now.AddMinutes(10); // صالح لمدة 10 دقائق
                
                // هنا يمكن إرسال الرمز عبر البريد الإلكتروني أو الواتساب
                // للتبسيط، سنعرض الرمز في رسالة
                MessageBox.Show($"رمز التحقق: {_generatedOTP}\nصالح لمدة 10 دقائق",
                    "رمز التحقق",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // إظهار حقول إدخال الرمز وكلمة المرور الجديدة
                pnlOTPVerification.Visible = true;
                txtOTP.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال رمز التحقق: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSendOTP.Enabled = true;
                btnSendOTP.Text = "إرسال رمز التحقق";
            }
        }
        
        private async void btnResetPassword_Click(object sender, EventArgs e)
        {
            if (!ValidateOTPInput()) return;
            
            try
            {
                btnResetPassword.Enabled = false;
                btnResetPassword.Text = "جاري إعادة التعيين...";
                
                // التحقق من رمز OTP
                if (txtOTP.Text != _generatedOTP || DateTime.Now > _otpExpiry)
                {
                    MessageBox.Show("رمز التحقق غير صحيح أو منتهي الصلاحية",
                        _languageManager.GetString("Error"),
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                // إعادة تعيين كلمة المرور
                var success = await _authService.ResetPasswordAsync(txtUsername.Text, txtNewPassword.Text);
                
                if (success)
                {
                    MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح",
                        _languageManager.GetString("Success"),
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في إعادة تعيين كلمة المرور",
                        _languageManager.GetString("Error"),
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnResetPassword.Enabled = true;
                btnResetPassword.Text = "إعادة تعيين كلمة المرور";
            }
        }
        
        private bool ValidateOTPInput()
        {
            if (string.IsNullOrWhiteSpace(txtOTP.Text))
            {
                MessageBox.Show("رمز التحقق مطلوب",
                    _languageManager.GetString("ValidationError"),
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtOTP.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
            {
                MessageBox.Show("كلمة المرور الجديدة مطلوبة",
                    _languageManager.GetString("ValidationError"),
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }
            
            if (txtNewPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show(_languageManager.GetString("PasswordsDoNotMatch"),
                    _languageManager.GetString("ValidationError"),
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return false;
            }
            
            if (txtNewPassword.Text.Length < 6)
            {
                MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل",
                    _languageManager.GetString("ValidationError"),
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }
            
            return true;
        }
        
        private string GenerateOTP()
        {
            var random = new Random();
            return random.Next(100000, 999999).ToString();
        }
        
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
