using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان مهمة القضية
    /// Case task entity
    /// </summary>
    [Table("CaseTasks")]
    public class CaseTask : BaseEntity
    {
        [Required]
        [Column("CaseId")]
        public int CaseId { get; set; }

        [Required]
        [StringLength(200)]
        [Column("Title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("TitleFr")]
        public string? TitleFr { get; set; }

        [StringLength(2000)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(2000)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("TaskType")]
        public string TaskType { get; set; } = "عام"; // عام، قانوني، إداري، مالي

        [StringLength(50)]
        [Column("TaskTypeFr")]
        public string? TaskTypeFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "جديد"; // جديد، قيد التنفيذ، مكتمل، ملغي، مؤجل

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [Required]
        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [Column("AssignedTo")]
        public int? AssignedTo { get; set; }

        [Column("AssignedDate")]
        public DateTime? AssignedDate { get; set; }

        [Column("DueDate")]
        public DateTime? DueDate { get; set; }

        [Column("StartDate")]
        public DateTime? StartDate { get; set; }

        [Column("CompletedDate")]
        public DateTime? CompletedDate { get; set; }

        [Column("EstimatedHours")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? EstimatedHours { get; set; }

        [Column("ActualHours")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? ActualHours { get; set; }

        [Column("ProgressPercentage")]
        public int ProgressPercentage { get; set; } = 0;

        [Column("IsRecurring")]
        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        [Column("RecurrencePattern")]
        public string? RecurrencePattern { get; set; }

        [Column("RecurrenceInterval")]
        public int? RecurrenceInterval { get; set; }

        [Column("RecurrenceEndDate")]
        public DateTime? RecurrenceEndDate { get; set; }

        [Column("ParentTaskId")]
        public int? ParentTaskId { get; set; }

        [Column("IsSubTask")]
        public bool IsSubTask { get; set; } = false;

        [Column("SubTaskCount")]
        public int SubTaskCount { get; set; } = 0;

        [Column("CompletedSubTasks")]
        public int CompletedSubTasks { get; set; } = 0;

        [Column("IsMilestone")]
        public bool IsMilestone { get; set; } = false;

        [Column("IsBlocking")]
        public bool IsBlocking { get; set; } = false;

        [StringLength(500)]
        [Column("BlockingReason")]
        public string? BlockingReason { get; set; }

        [StringLength(500)]
        [Column("BlockingReasonFr")]
        public string? BlockingReasonFr { get; set; }

        [StringLength(500)]
        [Column("Dependencies")]
        public string? Dependencies { get; set; } // JSON array of task IDs

        [Column("IsTemplate")]
        public bool IsTemplate { get; set; } = false;

        [StringLength(100)]
        [Column("TemplateName")]
        public string? TemplateName { get; set; }

        [StringLength(100)]
        [Column("TemplateNameFr")]
        public string? TemplateNameFr { get; set; }

        [Column("TemplateId")]
        public int? TemplateId { get; set; }

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [Column("RequiresApproval")]
        public bool RequiresApproval { get; set; } = false;

        [Column("IsApproved")]
        public bool IsApproved { get; set; } = true;

        [Column("ApprovedBy")]
        public int? ApprovedBy { get; set; }

        [Column("ApprovedDate")]
        public DateTime? ApprovedDate { get; set; }

        [StringLength(500)]
        [Column("ApprovalNotes")]
        public string? ApprovalNotes { get; set; }

        [StringLength(500)]
        [Column("ApprovalNotesFr")]
        public string? ApprovalNotesFr { get; set; }

        [Column("ReminderEnabled")]
        public bool ReminderEnabled { get; set; } = true;

        [Column("ReminderDate")]
        public DateTime? ReminderDate { get; set; }

        [Column("ReminderSent")]
        public bool ReminderSent { get; set; } = false;

        [Column("ReminderCount")]
        public int ReminderCount { get; set; } = 0;

        [Column("LastReminderDate")]
        public DateTime? LastReminderDate { get; set; }

        [StringLength(2000)]
        [Column("CompletionNotes")]
        public string? CompletionNotes { get; set; }

        [StringLength(2000)]
        [Column("CompletionNotesFr")]
        public string? CompletionNotesFr { get; set; }

        [StringLength(2000)]
        [Column("CancellationReason")]
        public string? CancellationReason { get; set; }

        [StringLength(2000)]
        [Column("CancellationReasonFr")]
        public string? CancellationReasonFr { get; set; }

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; }

        [StringLength(50)]
        [Column("Icon")]
        public string? Icon { get; set; }

        [Column("IsArchived")]
        public bool IsArchived { get; set; } = false;

        [Column("ArchiveDate")]
        public DateTime? ArchiveDate { get; set; }

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase Case { get; set; } = null!;

        [ForeignKey("AssignedTo")]
        public virtual User? AssignedToUser { get; set; }

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        [ForeignKey("ParentTaskId")]
        public virtual CaseTask? ParentTask { get; set; }

        [ForeignKey("TemplateId")]
        public virtual CaseTask? Template { get; set; }

        /// <summary>
        /// الحصول على العنوان حسب اللغة
        /// Get title by language
        /// </summary>
        public string GetTitle(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(TitleFr) 
                ? TitleFr 
                : Title;
        }

        /// <summary>
        /// الحصول على الوصف حسب اللغة
        /// Get description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// الحصول على نوع المهمة حسب اللغة
        /// Get task type by language
        /// </summary>
        public string GetTaskType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(TaskTypeFr) 
                ? TaskTypeFr 
                : TaskType;
        }

        /// <summary>
        /// الحصول على حالة المهمة حسب اللغة
        /// Get task status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// الحصول على الأولوية حسب اللغة
        /// Get priority by language
        /// </summary>
        public string GetPriority(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(PriorityFr) 
                ? PriorityFr 
                : Priority;
        }

        /// <summary>
        /// التحقق من اكتمال المهمة
        /// Check if task is completed
        /// </summary>
        public bool IsCompleted()
        {
            return Status == "مكتمل" || Status == "Completed";
        }

        /// <summary>
        /// التحقق من إلغاء المهمة
        /// Check if task is cancelled
        /// </summary>
        public bool IsCancelled()
        {
            return Status == "ملغي" || Status == "Cancelled";
        }

        /// <summary>
        /// التحقق من تأخر المهمة
        /// Check if task is overdue
        /// </summary>
        public bool IsOverdue()
        {
            return !IsCompleted() && DueDate.HasValue && DueDate.Value < DateTime.Now;
        }

        /// <summary>
        /// التحقق من قرب انتهاء المهمة
        /// Check if task is due soon
        /// </summary>
        public bool IsDueSoon(int hoursThreshold = 24)
        {
            return !IsCompleted() && DueDate.HasValue && 
                   DueDate.Value <= DateTime.Now.AddHours(hoursThreshold);
        }

        /// <summary>
        /// بدء المهمة
        /// Start task
        /// </summary>
        public void StartTask()
        {
            Status = "قيد التنفيذ";
            StatusFr = "In Progress";
            StartDate = DateTime.Now;
            ProgressPercentage = Math.Max(ProgressPercentage, 1);
        }

        /// <summary>
        /// إكمال المهمة
        /// Complete task
        /// </summary>
        public void CompleteTask(string? notes = null, string? notesFr = null)
        {
            Status = "مكتمل";
            StatusFr = "Completed";
            CompletedDate = DateTime.Now;
            ProgressPercentage = 100;
            CompletionNotes = notes;
            CompletionNotesFr = notesFr;
        }

        /// <summary>
        /// إلغاء المهمة
        /// Cancel task
        /// </summary>
        public void CancelTask(string reason, string? reasonFr = null)
        {
            Status = "ملغي";
            StatusFr = "Cancelled";
            CancellationReason = reason;
            CancellationReasonFr = reasonFr;
        }

        /// <summary>
        /// تأجيل المهمة
        /// Postpone task
        /// </summary>
        public void PostponeTask(DateTime newDueDate, string reason, string? reasonFr = null)
        {
            DueDate = newDueDate;
            Status = "مؤجل";
            StatusFr = "Postponed";
            Notes = reason;
            NotesFr = reasonFr;
        }

        /// <summary>
        /// تعيين المهمة
        /// Assign task
        /// </summary>
        public void AssignTask(int assignedTo)
        {
            AssignedTo = assignedTo;
            AssignedDate = DateTime.Now;
            
            if (Status == "جديد")
            {
                Status = "معين";
                StatusFr = "Assigned";
            }
        }

        /// <summary>
        /// تحديث التقدم
        /// Update progress
        /// </summary>
        public void UpdateProgress(int percentage)
        {
            ProgressPercentage = Math.Max(0, Math.Min(100, percentage));
            
            if (ProgressPercentage == 100 && !IsCompleted())
            {
                CompleteTask();
            }
            else if (ProgressPercentage > 0 && Status == "جديد")
            {
                StartTask();
            }
        }

        /// <summary>
        /// الموافقة على المهمة
        /// Approve task
        /// </summary>
        public void ApproveTask(int approvedBy, string? notes = null, string? notesFr = null)
        {
            IsApproved = true;
            ApprovedBy = approvedBy;
            ApprovedDate = DateTime.Now;
            ApprovalNotes = notes;
            ApprovalNotesFr = notesFr;
        }

        /// <summary>
        /// رفض المهمة
        /// Reject task
        /// </summary>
        public void RejectTask(string reason, string? reasonFr = null)
        {
            IsApproved = false;
            ApprovalNotes = reason;
            ApprovalNotesFr = reasonFr;
        }

        /// <summary>
        /// إرسال تذكير
        /// Send reminder
        /// </summary>
        public void SendReminder()
        {
            ReminderSent = true;
            ReminderCount++;
            LastReminderDate = DateTime.Now;
        }

        /// <summary>
        /// أرشفة المهمة
        /// Archive task
        /// </summary>
        public void ArchiveTask()
        {
            IsArchived = true;
            ArchiveDate = DateTime.Now;
        }

        /// <summary>
        /// استعادة المهمة من الأرشيف
        /// Restore task from archive
        /// </summary>
        public void RestoreTask()
        {
            IsArchived = false;
            ArchiveDate = null;
        }

        /// <summary>
        /// حساب المدة المتبقية
        /// Calculate remaining time
        /// </summary>
        public TimeSpan? GetRemainingTime()
        {
            if (!DueDate.HasValue || IsCompleted()) return null;
            
            var remaining = DueDate.Value - DateTime.Now;
            return remaining.TotalMilliseconds > 0 ? remaining : TimeSpan.Zero;
        }

        /// <summary>
        /// حساب مدة التنفيذ
        /// Calculate execution duration
        /// </summary>
        public TimeSpan? GetExecutionDuration()
        {
            if (!StartDate.HasValue) return null;
            
            var endDate = CompletedDate ?? DateTime.Now;
            return endDate - StartDate.Value;
        }
    }
}
