namespace LawOfficeManagement.Forms
{
    partial class MainForm
    {
        private System.ComponentModel.IContainer components = null;
        private MenuStrip menuStrip;
        private ToolStripMenuItem mnuFile;
        private ToolStripMenuItem mnuCases;
        private ToolStripMenuItem mnuClients;
        private ToolStripMenuItem mnuHearings;
        private ToolStripMenuItem mnuAppointments;
        private ToolStripMenuItem mnuFinancial;
        private ToolStripMenuItem mnuReports;
        private ToolStripMenuItem mnuSettings;
        private ToolStripMenuItem mnuUserManagement;
        private ToolStripMenuItem mnuSystemSettings;
        private ToolStripMenuItem mnuHelp;
        private ToolStripMenuItem mnuLogout;
        private ToolStripMenuItem mnuExit;
        private ToolStrip toolStrip;
        private ToolStripButton btnNewCase;
        private ToolStripButton btnNewClient;
        private ToolStripButton btnNewHearing;
        private ToolStripButton btnNewAppointment;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripButton btnSearch;
        private ToolStripButton btnReports;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel lblUserInfo;
        private ToolStripStatusLabel lblDateTime;
        private ToolStripStatusLabel lblStatus;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.menuStrip = new MenuStrip();
            this.mnuFile = new ToolStripMenuItem();
            this.mnuLogout = new ToolStripMenuItem();
            this.mnuExit = new ToolStripMenuItem();
            this.mnuCases = new ToolStripMenuItem();
            this.mnuClients = new ToolStripMenuItem();
            this.mnuHearings = new ToolStripMenuItem();
            this.mnuAppointments = new ToolStripMenuItem();
            this.mnuFinancial = new ToolStripMenuItem();
            this.mnuReports = new ToolStripMenuItem();
            this.mnuSettings = new ToolStripMenuItem();
            this.mnuUserManagement = new ToolStripMenuItem();
            this.mnuSystemSettings = new ToolStripMenuItem();
            this.mnuHelp = new ToolStripMenuItem();
            this.toolStrip = new ToolStrip();
            this.btnNewCase = new ToolStripButton();
            this.btnNewClient = new ToolStripButton();
            this.btnNewHearing = new ToolStripButton();
            this.btnNewAppointment = new ToolStripButton();
            this.toolStripSeparator1 = new ToolStripSeparator();
            this.btnSearch = new ToolStripButton();
            this.btnReports = new ToolStripButton();
            this.statusStrip = new StatusStrip();
            this.lblUserInfo = new ToolStripStatusLabel();
            this.lblDateTime = new ToolStripStatusLabel();
            this.lblStatus = new ToolStripStatusLabel();
            
            this.menuStrip.SuspendLayout();
            this.toolStrip.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();

            // 
            // menuStrip
            // 
            this.menuStrip.ImageScalingSize = new Size(20, 20);
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                this.mnuFile,
                this.mnuCases,
                this.mnuClients,
                this.mnuHearings,
                this.mnuAppointments,
                this.mnuFinancial,
                this.mnuReports,
                this.mnuSettings,
                this.mnuHelp});
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new Size(1200, 28);
            this.menuStrip.TabIndex = 0;
            this.menuStrip.Text = "menuStrip";

            // 
            // mnuFile
            // 
            this.mnuFile.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuLogout,
                this.mnuExit});
            this.mnuFile.Name = "mnuFile";
            this.mnuFile.Size = new Size(46, 24);
            this.mnuFile.Text = "ملف";

            // 
            // mnuLogout
            // 
            this.mnuLogout.Name = "mnuLogout";
            this.mnuLogout.Size = new Size(180, 26);
            this.mnuLogout.Text = "تسجيل الخروج";
            this.mnuLogout.Click += new EventHandler(this.mnuLogout_Click);

            // 
            // mnuExit
            // 
            this.mnuExit.Name = "mnuExit";
            this.mnuExit.Size = new Size(180, 26);
            this.mnuExit.Text = "خروج";
            this.mnuExit.Click += new EventHandler(this.mnuExit_Click);

            // 
            // mnuCases
            // 
            this.mnuCases.Name = "mnuCases";
            this.mnuCases.Size = new Size(108, 24);
            this.mnuCases.Text = "الملفات القانونية";
            this.mnuCases.Tag = "Cases";
            this.mnuCases.Click += new EventHandler(this.mnuCases_Click);

            // 
            // mnuClients
            // 
            this.mnuClients.Name = "mnuClients";
            this.mnuClients.Size = new Size(58, 24);
            this.mnuClients.Text = "العملاء";
            this.mnuClients.Tag = "Clients";
            this.mnuClients.Click += new EventHandler(this.mnuClients_Click);

            // 
            // mnuHearings
            // 
            this.mnuHearings.Name = "mnuHearings";
            this.mnuHearings.Size = new Size(66, 24);
            this.mnuHearings.Text = "الجلسات";
            this.mnuHearings.Tag = "Hearings";
            this.mnuHearings.Click += new EventHandler(this.mnuHearings_Click);

            // 
            // mnuAppointments
            // 
            this.mnuAppointments.Name = "mnuAppointments";
            this.mnuAppointments.Size = new Size(69, 24);
            this.mnuAppointments.Text = "المواعيد";
            this.mnuAppointments.Tag = "Appointments";
            this.mnuAppointments.Click += new EventHandler(this.mnuAppointments_Click);

            // 
            // mnuFinancial
            // 
            this.mnuFinancial.Name = "mnuFinancial";
            this.mnuFinancial.Size = new Size(58, 24);
            this.mnuFinancial.Text = "المالية";
            this.mnuFinancial.Tag = "Financial";
            this.mnuFinancial.Click += new EventHandler(this.mnuFinancial_Click);

            // 
            // mnuReports
            // 
            this.mnuReports.Name = "mnuReports";
            this.mnuReports.Size = new Size(66, 24);
            this.mnuReports.Text = "التقارير";
            this.mnuReports.Tag = "Reports";
            this.mnuReports.Click += new EventHandler(this.mnuReports_Click);

            // 
            // mnuSettings
            // 
            this.mnuSettings.DropDownItems.AddRange(new ToolStripItem[] {
                this.mnuUserManagement,
                this.mnuSystemSettings});
            this.mnuSettings.Name = "mnuSettings";
            this.mnuSettings.Size = new Size(75, 24);
            this.mnuSettings.Text = "الإعدادات";
            this.mnuSettings.Tag = "Settings";

            // 
            // mnuUserManagement
            // 
            this.mnuUserManagement.Name = "mnuUserManagement";
            this.mnuUserManagement.Size = new Size(180, 26);
            this.mnuUserManagement.Text = "إدارة المستخدمين";
            this.mnuUserManagement.Tag = "UserManagement";

            // 
            // mnuSystemSettings
            // 
            this.mnuSystemSettings.Name = "mnuSystemSettings";
            this.mnuSystemSettings.Size = new Size(180, 26);
            this.mnuSystemSettings.Text = "إعدادات النظام";
            this.mnuSystemSettings.Click += new EventHandler(this.mnuSettings_Click);

            // 
            // mnuHelp
            // 
            this.mnuHelp.Name = "mnuHelp";
            this.mnuHelp.Size = new Size(58, 24);
            this.mnuHelp.Text = "مساعدة";
            this.mnuHelp.Tag = "Help";

            // 
            // toolStrip
            // 
            this.toolStrip.ImageScalingSize = new Size(20, 20);
            this.toolStrip.Items.AddRange(new ToolStripItem[] {
                this.btnNewCase,
                this.btnNewClient,
                this.btnNewHearing,
                this.btnNewAppointment,
                this.toolStripSeparator1,
                this.btnSearch,
                this.btnReports});
            this.toolStrip.Location = new Point(0, 28);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.Size = new Size(1200, 27);
            this.toolStrip.TabIndex = 1;
            this.toolStrip.Text = "toolStrip";

            // 
            // btnNewCase
            // 
            this.btnNewCase.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.btnNewCase.Name = "btnNewCase";
            this.btnNewCase.Size = new Size(69, 24);
            this.btnNewCase.Text = "ملف جديد";
            this.btnNewCase.Click += new EventHandler(this.mnuCases_Click);

            // 
            // btnNewClient
            // 
            this.btnNewClient.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.btnNewClient.Name = "btnNewClient";
            this.btnNewClient.Size = new Size(73, 24);
            this.btnNewClient.Text = "عميل جديد";
            this.btnNewClient.Click += new EventHandler(this.mnuClients_Click);

            // 
            // btnNewHearing
            // 
            this.btnNewHearing.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.btnNewHearing.Name = "btnNewHearing";
            this.btnNewHearing.Size = new Size(77, 24);
            this.btnNewHearing.Text = "جلسة جديدة";
            this.btnNewHearing.Click += new EventHandler(this.mnuHearings_Click);

            // 
            // btnNewAppointment
            // 
            this.btnNewAppointment.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.btnNewAppointment.Name = "btnNewAppointment";
            this.btnNewAppointment.Size = new Size(80, 24);
            this.btnNewAppointment.Text = "موعد جديد";
            this.btnNewAppointment.Click += new EventHandler(this.mnuAppointments_Click);

            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new Size(6, 27);

            // 
            // btnSearch
            // 
            this.btnSearch.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new Size(39, 24);
            this.btnSearch.Text = "بحث";

            // 
            // btnReports
            // 
            this.btnReports.DisplayStyle = ToolStripItemDisplayStyle.Text;
            this.btnReports.Name = "btnReports";
            this.btnReports.Size = new Size(50, 24);
            this.btnReports.Text = "تقارير";
            this.btnReports.Click += new EventHandler(this.mnuReports_Click);

            // 
            // statusStrip
            // 
            this.statusStrip.ImageScalingSize = new Size(20, 20);
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.lblUserInfo,
                this.lblDateTime,
                this.lblStatus});
            this.statusStrip.Location = new Point(0, 728);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1200, 22);
            this.statusStrip.TabIndex = 2;
            this.statusStrip.Text = "statusStrip";

            // 
            // lblUserInfo
            // 
            this.lblUserInfo.Name = "lblUserInfo";
            this.lblUserInfo.Size = new Size(118, 20);
            this.lblUserInfo.Text = "معلومات المستخدم";

            // 
            // lblDateTime
            // 
            this.lblDateTime.Name = "lblDateTime";
            this.lblDateTime.Size = new Size(89, 20);
            this.lblDateTime.Text = "التاريخ والوقت";

            // 
            // lblStatus
            // 
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(978, 20);
            this.lblStatus.Spring = true;
            this.lblStatus.Text = "جاهز";
            this.lblStatus.TextAlign = ContentAlignment.MiddleLeft;

            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 750);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.toolStrip);
            this.Controls.Add(this.menuStrip);
            this.IsMdiContainer = true;
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.Text = "نظام إدارة مكتب المحاماة";
            this.WindowState = FormWindowState.Maximized;
            
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}
