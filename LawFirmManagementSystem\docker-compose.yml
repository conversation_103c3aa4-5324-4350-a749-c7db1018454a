version: '3.8'

services:
  # SQL Server service
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: lawfirm-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=LawFirm@2025!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./Database/Scripts:/docker-entrypoint-initdb.d
    networks:
      - lawfirm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P LawFirm@2025! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Law Firm Management System application
  lawfirm-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lawfirm-application
    environment:
      - DOTNET_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=LawFirmManagementDB;User Id=sa;Password=LawFirm@2025!;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=true
    volumes:
      - app_data:/app/Data
      - app_logs:/app/Logs
      - app_documents:/app/Documents
      - app_backups:/app/Backups
      - app_reports:/app/Reports
    networks:
      - lawfirm-network
    depends_on:
      sqlserver:
        condition: service_healthy
    restart: unless-stopped
    # Note: WinForms applications typically don't expose ports
    # but this is here for future web components
    # ports:
    #   - "8080:80"
    #   - "8443:443"

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: lawfirm-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lawfirm-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Elasticsearch for document search (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: lawfirm-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - lawfirm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana for log analysis (optional)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: lawfirm-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - lawfirm-network
    depends_on:
      elasticsearch:
        condition: service_healthy
    restart: unless-stopped

  # Backup service (optional)
  backup:
    image: mcr.microsoft.com/mssql-tools
    container_name: lawfirm-backup
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - SQL_SERVER=sqlserver
      - SQL_USER=sa
      - SQL_PASSWORD=LawFirm@2025!
      - DATABASE_NAME=LawFirmManagementDB
    volumes:
      - app_backups:/backups
      - ./Scripts/backup.sh:/backup.sh
    networks:
      - lawfirm-network
    depends_on:
      - sqlserver
    restart: unless-stopped
    command: ["sh", "-c", "while true; do sleep 3600; done"]  # Keep container running

networks:
  lawfirm-network:
    driver: bridge
    name: lawfirm-network

volumes:
  sqlserver_data:
    name: lawfirm-sqlserver-data
  redis_data:
    name: lawfirm-redis-data
  elasticsearch_data:
    name: lawfirm-elasticsearch-data
  app_data:
    name: lawfirm-app-data
  app_logs:
    name: lawfirm-app-logs
  app_documents:
    name: lawfirm-app-documents
  app_backups:
    name: lawfirm-app-backups
  app_reports:
    name: lawfirm-app-reports
