using System;
using System.Drawing;
using System.Windows.Forms;
using LawOfficeManagement.Localization;
using LawOfficeManagement.Services;
using LawOfficeManagement.Utils;

namespace LawOfficeManagement.Forms
{
    public partial class LoginForm : Form
    {
        private readonly AuthenticationService _authService;
        private readonly LanguageManager _languageManager;
        private bool _passwordVisible = false;
        private int _failedAttempts = 0;
        private const int MaxFailedAttempts = 5;
        
        public LoginForm()
        {
            InitializeComponent();
            _authService = new AuthenticationService();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            ApplyTheme();
            LoadSettings();
        }
        
        private void InitializeForm()
        {
            // إعدادات النافذة الأساسية
            this.Text = _languageManager.GetString("AppTitle");
            this.Size = new Size(450, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = true;
            this.ShowInTaskbar = true;
            
            // تطبيق اللغة
            _languageManager.ApplyLanguageToForm(this);
            
            // الاشتراك في تغيير اللغة
            _languageManager.LanguageChanged += OnLanguageChanged;
        }
        
        private void ApplyTheme()
        {
            // ألوان المحاماة الاحترافية
            var primaryColor = Color.FromArgb(25, 42, 86);      // كحلي داكن
            var secondaryColor = Color.FromArgb(218, 165, 32);   // ذهبي
            var backgroundColor = Color.FromArgb(248, 249, 250); // رمادي فاتح
            var textColor = Color.FromArgb(33, 37, 41);          // رمادي داكن
            
            this.BackColor = backgroundColor;
            
            // تطبيق الألوان على العناصر
            foreach (Control control in this.Controls)
            {
                ApplyThemeToControl(control, primaryColor, secondaryColor, backgroundColor, textColor);
            }
        }
        
        private void ApplyThemeToControl(Control control, Color primary, Color secondary, Color background, Color text)
        {
            if (control is Button button)
            {
                button.BackColor = primary;
                button.ForeColor = Color.White;
                button.FlatStyle = FlatStyle.Flat;
                button.FlatAppearance.BorderSize = 0;
                button.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                button.Cursor = Cursors.Hand;
            }
            else if (control is TextBox textBox)
            {
                textBox.BorderStyle = BorderStyle.FixedSingle;
                textBox.Font = new Font("Segoe UI", 11F);
                textBox.BackColor = Color.White;
                textBox.ForeColor = text;
            }
            else if (control is Label label)
            {
                label.ForeColor = text;
                label.Font = new Font("Segoe UI", 9F);
            }
            else if (control is CheckBox checkBox)
            {
                checkBox.ForeColor = text;
                checkBox.Font = new Font("Segoe UI", 9F);
            }
            else if (control is LinkLabel linkLabel)
            {
                linkLabel.LinkColor = secondary;
                linkLabel.Font = new Font("Segoe UI", 9F);
            }
            
            // تطبيق الثيم على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyThemeToControl(child, primary, secondary, background, text);
            }
        }
        
        private void LoadSettings()
        {
            try
            {
                // تحميل إعدادات تذكر المستخدم
                var savedUsername = Properties.Settings.Default.RememberedUsername;
                var rememberMe = Properties.Settings.Default.RememberMe;
                
                if (rememberMe && !string.IsNullOrEmpty(savedUsername))
                {
                    txtUsername.Text = savedUsername;
                    chkRememberMe.Checked = true;
                    txtPassword.Focus();
                }
                else
                {
                    txtUsername.Focus();
                }
                
                // تحميل اللغة المحفوظة
                var savedLanguage = Properties.Settings.Default.Language;
                if (!string.IsNullOrEmpty(savedLanguage))
                {
                    _languageManager.SetLanguage(savedLanguage);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private void SaveSettings()
        {
            try
            {
                Properties.Settings.Default.RememberMe = chkRememberMe.Checked;
                Properties.Settings.Default.RememberedUsername = chkRememberMe.Checked ? txtUsername.Text : string.Empty;
                Properties.Settings.Default.Language = _languageManager.CurrentLanguage;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
        
        private async void btnLogin_Click(object sender, EventArgs e)
        {
            if (!ValidateInput()) return;
            
            try
            {
                btnLogin.Enabled = false;
                btnLogin.Text = _languageManager.GetString("Loading") ?? "جاري التحميل...";
                
                var username = txtUsername.Text.Trim();
                var password = txtPassword.Text;
                
                var loginResult = await _authService.LoginAsync(username, password);
                
                if (loginResult.IsSuccess)
                {
                    SaveSettings();
                    
                    // إظهار رسالة نجاح
                    MessageBox.Show(_languageManager.GetString("LoginSuccessful"), 
                        _languageManager.GetString("Success"), 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // فتح النافذة الرئيسية
                    this.Hide();
                    var mainForm = new MainForm(loginResult.User!);
                    mainForm.FormClosed += (s, args) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    _failedAttempts++;
                    
                    if (_failedAttempts >= MaxFailedAttempts)
                    {
                        MessageBox.Show(_languageManager.GetString("AccountLocked"), 
                            _languageManager.GetString("Error"), 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        
                        // قفل النافذة لفترة
                        await Task.Delay(30000); // 30 ثانية
                        _failedAttempts = 0;
                    }
                    else
                    {
                        var remainingAttempts = MaxFailedAttempts - _failedAttempts;
                        MessageBox.Show($"{loginResult.ErrorMessage}\nالمحاولات المتبقية: {remainingAttempts}", 
                            _languageManager.GetString("LoginFailed"), 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", 
                    _languageManager.GetString("Error"), 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnLogin.Enabled = true;
                btnLogin.Text = _languageManager.GetString("Login");
            }
        }
        
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show(_languageManager.GetString("RequiredField") + ": " + _languageManager.GetString("Username"), 
                    _languageManager.GetString("ValidationError"), 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show(_languageManager.GetString("RequiredField") + ": " + _languageManager.GetString("Password"), 
                    _languageManager.GetString("ValidationError"), 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }
            
            return true;
        }
        
        private void btnTogglePassword_Click(object sender, EventArgs e)
        {
            _passwordVisible = !_passwordVisible;
            txtPassword.UseSystemPasswordChar = !_passwordVisible;
            btnTogglePassword.Text = _passwordVisible ? "🙈" : "👁";
        }
        
        private void btnLanguage_Click(object sender, EventArgs e)
        {
            var newLanguage = _languageManager.CurrentLanguage == "ar" ? "fr" : "ar";
            _languageManager.SetLanguage(newLanguage);
        }
        
        private void OnLanguageChanged(object? sender, LanguageChangedEventArgs e)
        {
            _languageManager.ApplyLanguageToForm(this);
            UpdateLanguageButton();
        }
        
        private void UpdateLanguageButton()
        {
            btnLanguage.Text = _languageManager.CurrentLanguage == "ar" ? "FR" : "ع";
        }
        
        private void linkForgotPassword_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var forgotPasswordForm = new ForgotPasswordForm();
            forgotPasswordForm.ShowDialog(this);
        }
        
        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }
        
        private void txtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }
        
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            SaveSettings();
            base.OnFormClosing(e);
        }
    }
}
