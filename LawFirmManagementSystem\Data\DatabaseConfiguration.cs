using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace LawFirmManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات
    /// Database configuration
    /// </summary>
    public static class DatabaseConfiguration
    {
        /// <summary>
        /// تكوين خدمات قاعدة البيانات
        /// Configure database services
        /// </summary>
        public static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("سلسلة الاتصال بقاعدة البيانات غير محددة / Database connection string is not configured");
            }

            services.AddDbContext<LawFirmDbContext>(options =>
            {
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);
                    
                    sqlOptions.CommandTimeout(30);
                    sqlOptions.MigrationsAssembly("LawFirmManagementSystem");
                });

                // Enable sensitive data logging in development
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif

                // Configure logging
                options.LogTo(Console.WriteLine, LogLevel.Information);
            });

            return services;
        }

        /// <summary>
        /// إنشاء قاعدة البيانات والجداول
        /// Create database and tables
        /// </summary>
        public static async Task<bool> EnsureDatabaseCreatedAsync(IServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                // Create database if it doesn't exist
                var created = await context.Database.EnsureCreatedAsync();
                
                if (created)
                {
                    Console.WriteLine("تم إنشاء قاعدة البيانات بنجاح / Database created successfully");
                }
                else
                {
                    Console.WriteLine("قاعدة البيانات موجودة بالفعل / Database already exists");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء قاعدة البيانات / Error creating database: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تطبيق الترحيلات المعلقة
        /// Apply pending migrations
        /// </summary>
        public static async Task<bool> ApplyMigrationsAsync(IServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
                
                if (pendingMigrations.Any())
                {
                    Console.WriteLine("تطبيق الترحيلات المعلقة / Applying pending migrations...");
                    await context.Database.MigrateAsync();
                    Console.WriteLine("تم تطبيق الترحيلات بنجاح / Migrations applied successfully");
                }
                else
                {
                    Console.WriteLine("لا توجد ترحيلات معلقة / No pending migrations");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تطبيق الترحيلات / Error applying migrations: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// Test database connection
        /// </summary>
        public static async Task<bool> TestConnectionAsync(IServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                await context.Database.OpenConnectionAsync();
                await context.Database.CloseConnectionAsync();
                
                Console.WriteLine("تم اختبار الاتصال بقاعدة البيانات بنجاح / Database connection test successful");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"فشل في اختبار الاتصال بقاعدة البيانات / Database connection test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// Create database backup
        /// </summary>
        public static async Task<bool> CreateBackupAsync(IServiceProvider serviceProvider, string backupPath = null)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                if (string.IsNullOrEmpty(backupPath))
                {
                    var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                    Directory.CreateDirectory(backupDir);
                    backupPath = Path.Combine(backupDir, $"LawFirmDB_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak");
                }

                var databaseName = context.Database.GetDbConnection().Database;
                var backupSql = $@"
                    BACKUP DATABASE [{databaseName}] 
                    TO DISK = '{backupPath}' 
                    WITH FORMAT, INIT, COMPRESSION, 
                    NAME = 'Law Firm Database Backup - {DateTime.Now:yyyy-MM-dd HH:mm:ss}'";

                await context.Database.ExecuteSqlRawAsync(backupSql);
                
                Console.WriteLine($"تم إنشاء النسخة الاحتياطية بنجاح / Backup created successfully: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء النسخة الاحتياطية / Error creating backup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// Restore database from backup
        /// </summary>
        public static async Task<bool> RestoreBackupAsync(IServiceProvider serviceProvider, string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    Console.WriteLine($"ملف النسخة الاحتياطية غير موجود / Backup file not found: {backupPath}");
                    return false;
                }

                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                var databaseName = context.Database.GetDbConnection().Database;
                
                // Set database to single user mode
                var singleUserSql = $"ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE";
                await context.Database.ExecuteSqlRawAsync(singleUserSql);

                // Restore database
                var restoreSql = $@"
                    RESTORE DATABASE [{databaseName}] 
                    FROM DISK = '{backupPath}' 
                    WITH REPLACE";
                await context.Database.ExecuteSqlRawAsync(restoreSql);

                // Set database back to multi user mode
                var multiUserSql = $"ALTER DATABASE [{databaseName}] SET MULTI_USER";
                await context.Database.ExecuteSqlRawAsync(multiUserSql);
                
                Console.WriteLine($"تم استعادة النسخة الاحتياطية بنجاح / Backup restored successfully from: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في استعادة النسخة الاحتياطية / Error restoring backup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنظيف قاعدة البيانات (حذف البيانات المحذوفة نهائياً)
        /// Clean up database (permanently delete soft-deleted records)
        /// </summary>
        public static async Task<bool> CleanupDatabaseAsync(IServiceProvider serviceProvider, int retentionDays = 30)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                // Get all entity types that inherit from BaseEntity
                var entityTypes = context.Model.GetEntityTypes()
                    .Where(t => typeof(BaseEntity).IsAssignableFrom(t.ClrType))
                    .ToList();

                var totalDeleted = 0;

                foreach (var entityType in entityTypes)
                {
                    var tableName = entityType.GetTableName();
                    var deleteSql = $@"
                        DELETE FROM [{tableName}] 
                        WHERE IsDeleted = 1 AND DeletedDate < @cutoffDate";
                    
                    var deleted = await context.Database.ExecuteSqlRawAsync(deleteSql, 
                        new Microsoft.Data.SqlClient.SqlParameter("@cutoffDate", cutoffDate));
                    
                    totalDeleted += deleted;
                    
                    if (deleted > 0)
                    {
                        Console.WriteLine($"تم حذف {deleted} سجل من جدول {tableName} / Deleted {deleted} records from {tableName}");
                    }
                }
                
                Console.WriteLine($"تم تنظيف قاعدة البيانات - إجمالي السجلات المحذوفة: {totalDeleted} / Database cleanup completed - Total records deleted: {totalDeleted}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تنظيف قاعدة البيانات / Error cleaning up database: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات قاعدة البيانات
        /// Get database statistics
        /// </summary>
        public static async Task<DatabaseStats> GetDatabaseStatsAsync(IServiceProvider serviceProvider)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<LawFirmDbContext>();
                
                var stats = new DatabaseStats
                {
                    TotalUsers = await context.Users.CountAsync(),
                    TotalClients = await context.Clients.CountAsync(),
                    TotalCases = await context.LegalCases.CountAsync(),
                    TotalInvoices = await context.Invoices.CountAsync(),
                    TotalPayments = await context.Payments.CountAsync(),
                    TotalDocuments = await context.Documents.CountAsync(),
                    TotalHearings = await context.Hearings.CountAsync(),
                    TotalAppointments = await context.Appointments.CountAsync(),
                    
                    ActiveUsers = await context.Users.CountAsync(u => u.IsActive),
                    ActiveClients = await context.Clients.CountAsync(c => c.ClientStatus == "Active"),
                    ActiveCases = await context.LegalCases.CountAsync(c => c.CaseStatus == "جاري"),
                    
                    DatabaseSizeBytes = await GetDatabaseSizeAsync(context)
                };
                
                return stats;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على إحصائيات قاعدة البيانات / Error getting database stats: {ex.Message}");
                return new DatabaseStats();
            }
        }

        private static async Task<long> GetDatabaseSizeAsync(LawFirmDbContext context)
        {
            try
            {
                var sizeSql = @"
                    SELECT SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192) as DatabaseSize
                    FROM sys.database_files 
                    WHERE type_desc = 'ROWS'";
                
                var result = await context.Database.SqlQueryRaw<long>(sizeSql).FirstOrDefaultAsync();
                return result;
            }
            catch
            {
                return 0;
            }
        }
    }

    /// <summary>
    /// إحصائيات قاعدة البيانات
    /// Database statistics
    /// </summary>
    public class DatabaseStats
    {
        public int TotalUsers { get; set; }
        public int TotalClients { get; set; }
        public int TotalCases { get; set; }
        public int TotalInvoices { get; set; }
        public int TotalPayments { get; set; }
        public int TotalDocuments { get; set; }
        public int TotalHearings { get; set; }
        public int TotalAppointments { get; set; }
        
        public int ActiveUsers { get; set; }
        public int ActiveClients { get; set; }
        public int ActiveCases { get; set; }
        
        public long DatabaseSizeBytes { get; set; }
        
        public string DatabaseSizeFormatted => FormatBytes(DatabaseSizeBytes);
        
        private static string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
