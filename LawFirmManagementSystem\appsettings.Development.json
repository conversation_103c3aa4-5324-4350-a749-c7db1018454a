{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=LawFirmManagementDB_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information"}, "Console": {"IncludeScopes": true}, "File": {"Path": "Logs/development-{Date}.log", "MinLevel": "Debug", "RollingInterval": "Day", "RetainedFileCountLimit": 7}}, "Application": {"Name": "نظام إدارة مكتب المحاماة - بيئة التطوير", "NameFr": "Système de Gestion de Cabinet d'Avocat - Développement", "Version": "1.0.0-dev", "Environment": "Development", "DebugMode": true, "SessionTimeoutMinutes": 120, "MaxLoginAttempts": 10, "LockoutDurationMinutes": 5}, "Security": {"PasswordPolicy": {"MinLength": 6, "RequireUppercase": false, "RequireLowercase": false, "RequireDigit": false, "RequireSpecialCharacter": false, "MaxAge": 365, "PreventReuse": 3}, "TwoFactorAuthentication": {"Enabled": false}, "Encryption": {"Algorithm": "AES256", "KeySize": 256, "DebugMode": true}}, "Database": {"CommandTimeout": 60, "EnableRetryOnFailure": true, "MaxRetryCount": 5, "MaxRetryDelay": "00:01:00", "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "BackupRetentionDays": 7, "AutoBackupEnabled": false, "CleanupRetentionDays": 30}, "Email": {"SmtpServer": "smtp.mailtrap.io", "SmtpPort": 2525, "EnableSsl": true, "Username": "your-mailtrap-username", "Password": "your-mailtrap-password", "FromEmail": "<EMAIL>", "FromName": "نظام إدارة مكتب المحاماة - تطوير", "FromNameFr": "Système de Gestion - Développement", "TestMode": true}, "SMS": {"Provider": "<PERSON><PERSON>", "Enabled": false, "TestMode": true}, "WhatsApp": {"Provider": "<PERSON><PERSON>", "Enabled": false, "TestMode": true}, "FileStorage": {"BasePath": "Documents_Dev", "MaxFileSize": 10485760, "AllowedExtensions": [".pdf", ".doc", ".docx", ".txt", ".jpg", ".png"], "EnableEncryption": false, "EnableVersioning": true, "EnableThumbnails": false}, "Reports": {"DefaultFormat": "PDF", "TemplatePath": "Templates/Reports_Dev", "OutputPath": "Reports/Generated_Dev", "RetentionDays": 7}, "Backup": {"Enabled": false, "BackupPath": "Backups_Dev", "RetentionDays": 7, "CompressBackups": false, "IncludeDocuments": false}, "Audit": {"Enabled": true, "LogLevel": "Debug", "RetentionDays": 30, "LogSensitiveData": true, "LogUserActions": true, "LogSystemActions": true, "LogDataChanges": true}, "Performance": {"CacheEnabled": false, "CacheExpirationMinutes": 5, "DatabasePoolSize": 10, "MaxConcurrentUsers": 5, "EnableCompression": false}, "UI": {"Themes": {"Development": {"PrimaryColor": "#ff6b35", "SecondaryColor": "#6c757d", "SuccessColor": "#28a745", "DangerColor": "#dc3545", "WarningColor": "#ffc107", "InfoColor": "#17a2b8", "LightColor": "#f8f9fa", "DarkColor": "#343a40"}}, "DefaultTheme": "Development", "ShowDebugInfo": true, "EnableHotReload": true}, "Integration": {"CourtSystems": {"Enabled": false, "ApiEndpoint": "https://api-dev.courts.ma", "TestMode": true}, "BankingSystems": {"Enabled": false, "TestMode": true}}, "Notifications": {"Email": {"Enabled": true, "TestMode": true, "TestRecipient": "<EMAIL>"}, "SMS": {"Enabled": false, "TestMode": true}, "InApp": {"Enabled": true, "RetentionDays": 7}}, "Development": {"EnableSwagger": true, "EnableDetailedErrors": true, "EnableSensitiveDataLogging": true, "SeedTestData": true, "MockExternalServices": true, "BypassAuthentication": false, "BypassAuthorization": false, "EnableProfiler": true, "ShowSqlQueries": true}, "TestData": {"CreateTestUsers": true, "CreateTestClients": true, "CreateTestCases": true, "TestUserPassword": "Test@123"}}