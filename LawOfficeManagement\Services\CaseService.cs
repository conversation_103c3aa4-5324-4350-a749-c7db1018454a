using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Services
{
    public class CaseService
    {
        private readonly DatabaseService _databaseService;
        private readonly ErrorLogService _errorLogService;
        
        public CaseService()
        {
            _databaseService = DatabaseService.Instance;
            _errorLogService = new ErrorLogService();
        }
        
        public async Task<List<LegalCase>> GetAllCasesAsync()
        {
            try
            {
                var query = @"
                    SELECT 
                        lc.CaseId, lc.CaseReference, lc.OfficeReference, lc.CaseTitle, lc.CaseTitleFr,
                        lc.CaseTypeId, lc.ClientId, lc.CourtId, lc.CourtCaseNumber, lc.CaseStatus,
                        lc.Priority, lc.OpenDate, lc.CloseDate, lc.Description, lc.InternalNotes,
                        lc.AssignedLawyer, lc.<PERSON>, lc.Created<PERSON>ate, lc.CreatedBy,
                        ct.TypeName as CaseTypeName, ct.TypeNameFr as CaseTypeNameFr,
                        c.FullName as ClientName, c.ClientType,
                        co.CourtName, co.CourtNameFr,
                        u.FullName as LawyerName
                    FROM LegalCases lc
                    LEFT JOIN CaseTypes ct ON lc.CaseTypeId = ct.CaseTypeId
                    LEFT JOIN Clients c ON lc.ClientId = c.ClientId
                    LEFT JOIN Courts co ON lc.CourtId = co.CourtId
                    LEFT JOIN Users u ON lc.AssignedLawyer = u.UserId
                    WHERE lc.IsActive = 1
                    ORDER BY lc.CreatedDate DESC";
                
                var dataTable = await _databaseService.ExecuteQueryAsync(query);
                var cases = new List<LegalCase>();
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var legalCase = MapDataRowToLegalCase(row);
                    cases.Add(legalCase);
                }
                
                return cases;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.GetAllCases", ex);
                throw;
            }
        }
        
        public async Task<LegalCase?> GetCaseByIdAsync(int caseId)
        {
            try
            {
                var query = @"
                    SELECT 
                        lc.CaseId, lc.CaseReference, lc.OfficeReference, lc.CaseTitle, lc.CaseTitleFr,
                        lc.CaseTypeId, lc.ClientId, lc.CourtId, lc.CourtCaseNumber, lc.CaseStatus,
                        lc.Priority, lc.OpenDate, lc.CloseDate, lc.Description, lc.InternalNotes,
                        lc.AssignedLawyer, lc.IsActive, lc.CreatedDate, lc.CreatedBy,
                        ct.TypeName as CaseTypeName, ct.TypeNameFr as CaseTypeNameFr,
                        c.FullName as ClientName, c.ClientType,
                        co.CourtName, co.CourtNameFr,
                        u.FullName as LawyerName
                    FROM LegalCases lc
                    LEFT JOIN CaseTypes ct ON lc.CaseTypeId = ct.CaseTypeId
                    LEFT JOIN Clients c ON lc.ClientId = c.ClientId
                    LEFT JOIN Courts co ON lc.CourtId = co.CourtId
                    LEFT JOIN Users u ON lc.AssignedLawyer = u.UserId
                    WHERE lc.CaseId = @CaseId AND lc.IsActive = 1";
                
                var parameters = new[] { new SqlParameter("@CaseId", caseId) };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                
                if (dataTable.Rows.Count == 0)
                    return null;
                
                return MapDataRowToLegalCase(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.GetCaseById", ex);
                throw;
            }
        }
        
        public async Task<int> CreateCaseAsync(LegalCase legalCase, int createdBy)
        {
            try
            {
                var query = @"
                    INSERT INTO LegalCases (
                        CaseReference, OfficeReference, CaseTitle, CaseTitleFr, CaseTypeId,
                        ClientId, CourtId, CourtCaseNumber, CaseStatus, Priority,
                        OpenDate, CloseDate, Description, InternalNotes, AssignedLawyer,
                        IsActive, CreatedDate, CreatedBy
                    ) VALUES (
                        @CaseReference, @OfficeReference, @CaseTitle, @CaseTitleFr, @CaseTypeId,
                        @ClientId, @CourtId, @CourtCaseNumber, @CaseStatus, @Priority,
                        @OpenDate, @CloseDate, @Description, @InternalNotes, @AssignedLawyer,
                        1, GETDATE(), @CreatedBy
                    );
                    SELECT SCOPE_IDENTITY();";
                
                var parameters = new[]
                {
                    new SqlParameter("@CaseReference", legalCase.CaseReference),
                    new SqlParameter("@OfficeReference", legalCase.OfficeReference),
                    new SqlParameter("@CaseTitle", legalCase.CaseTitle),
                    new SqlParameter("@CaseTitleFr", (object?)legalCase.CaseTitleFr ?? DBNull.Value),
                    new SqlParameter("@CaseTypeId", legalCase.CaseTypeId),
                    new SqlParameter("@ClientId", legalCase.ClientId),
                    new SqlParameter("@CourtId", (object?)legalCase.CourtId ?? DBNull.Value),
                    new SqlParameter("@CourtCaseNumber", (object?)legalCase.CourtCaseNumber ?? DBNull.Value),
                    new SqlParameter("@CaseStatus", legalCase.CaseStatus),
                    new SqlParameter("@Priority", legalCase.Priority),
                    new SqlParameter("@OpenDate", legalCase.OpenDate),
                    new SqlParameter("@CloseDate", (object?)legalCase.CloseDate ?? DBNull.Value),
                    new SqlParameter("@Description", (object?)legalCase.Description ?? DBNull.Value),
                    new SqlParameter("@InternalNotes", (object?)legalCase.InternalNotes ?? DBNull.Value),
                    new SqlParameter("@AssignedLawyer", (object?)legalCase.AssignedLawyer ?? DBNull.Value),
                    new SqlParameter("@CreatedBy", createdBy)
                };
                
                var result = await _databaseService.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.CreateCase", ex);
                throw;
            }
        }
        
        public async Task<bool> UpdateCaseAsync(LegalCase legalCase, int modifiedBy)
        {
            try
            {
                var query = @"
                    UPDATE LegalCases SET
                        CaseReference = @CaseReference,
                        OfficeReference = @OfficeReference,
                        CaseTitle = @CaseTitle,
                        CaseTitleFr = @CaseTitleFr,
                        CaseTypeId = @CaseTypeId,
                        ClientId = @ClientId,
                        CourtId = @CourtId,
                        CourtCaseNumber = @CourtCaseNumber,
                        CaseStatus = @CaseStatus,
                        Priority = @Priority,
                        OpenDate = @OpenDate,
                        CloseDate = @CloseDate,
                        Description = @Description,
                        InternalNotes = @InternalNotes,
                        AssignedLawyer = @AssignedLawyer,
                        ModifiedDate = GETDATE(),
                        ModifiedBy = @ModifiedBy
                    WHERE CaseId = @CaseId AND IsActive = 1";
                
                var parameters = new[]
                {
                    new SqlParameter("@CaseId", legalCase.CaseId),
                    new SqlParameter("@CaseReference", legalCase.CaseReference),
                    new SqlParameter("@OfficeReference", legalCase.OfficeReference),
                    new SqlParameter("@CaseTitle", legalCase.CaseTitle),
                    new SqlParameter("@CaseTitleFr", (object?)legalCase.CaseTitleFr ?? DBNull.Value),
                    new SqlParameter("@CaseTypeId", legalCase.CaseTypeId),
                    new SqlParameter("@ClientId", legalCase.ClientId),
                    new SqlParameter("@CourtId", (object?)legalCase.CourtId ?? DBNull.Value),
                    new SqlParameter("@CourtCaseNumber", (object?)legalCase.CourtCaseNumber ?? DBNull.Value),
                    new SqlParameter("@CaseStatus", legalCase.CaseStatus),
                    new SqlParameter("@Priority", legalCase.Priority),
                    new SqlParameter("@OpenDate", legalCase.OpenDate),
                    new SqlParameter("@CloseDate", (object?)legalCase.CloseDate ?? DBNull.Value),
                    new SqlParameter("@Description", (object?)legalCase.Description ?? DBNull.Value),
                    new SqlParameter("@InternalNotes", (object?)legalCase.InternalNotes ?? DBNull.Value),
                    new SqlParameter("@AssignedLawyer", (object?)legalCase.AssignedLawyer ?? DBNull.Value),
                    new SqlParameter("@ModifiedBy", modifiedBy)
                };
                
                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.UpdateCase", ex);
                throw;
            }
        }
        
        public async Task<bool> DeleteCaseAsync(int caseId, int deletedBy)
        {
            try
            {
                var query = @"
                    UPDATE LegalCases SET
                        IsActive = 0,
                        ModifiedDate = GETDATE(),
                        ModifiedBy = @DeletedBy
                    WHERE CaseId = @CaseId";
                
                var parameters = new[]
                {
                    new SqlParameter("@CaseId", caseId),
                    new SqlParameter("@DeletedBy", deletedBy)
                };
                
                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.DeleteCase", ex);
                throw;
            }
        }
        
        public async Task<List<LegalCase>> SearchCasesAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT 
                        lc.CaseId, lc.CaseReference, lc.OfficeReference, lc.CaseTitle, lc.CaseTitleFr,
                        lc.CaseTypeId, lc.ClientId, lc.CourtId, lc.CourtCaseNumber, lc.CaseStatus,
                        lc.Priority, lc.OpenDate, lc.CloseDate, lc.Description, lc.InternalNotes,
                        lc.AssignedLawyer, lc.IsActive, lc.CreatedDate, lc.CreatedBy,
                        ct.TypeName as CaseTypeName, ct.TypeNameFr as CaseTypeNameFr,
                        c.FullName as ClientName, c.ClientType,
                        co.CourtName, co.CourtNameFr,
                        u.FullName as LawyerName
                    FROM LegalCases lc
                    LEFT JOIN CaseTypes ct ON lc.CaseTypeId = ct.CaseTypeId
                    LEFT JOIN Clients c ON lc.ClientId = c.ClientId
                    LEFT JOIN Courts co ON lc.CourtId = co.CourtId
                    LEFT JOIN Users u ON lc.AssignedLawyer = u.UserId
                    WHERE lc.IsActive = 1 AND (
                        lc.CaseReference LIKE @SearchTerm OR
                        lc.OfficeReference LIKE @SearchTerm OR
                        lc.CaseTitle LIKE @SearchTerm OR
                        lc.CourtCaseNumber LIKE @SearchTerm OR
                        c.FullName LIKE @SearchTerm
                    )
                    ORDER BY lc.CreatedDate DESC";
                
                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                var cases = new List<LegalCase>();
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var legalCase = MapDataRowToLegalCase(row);
                    cases.Add(legalCase);
                }
                
                return cases;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.SearchCases", ex);
                throw;
            }
        }
        
        public async Task<string> GenerateNextCaseReferenceAsync()
        {
            try
            {
                var query = @"
                    SELECT COUNT(*) + 1 
                    FROM LegalCases 
                    WHERE YEAR(CreatedDate) = YEAR(GETDATE())";
                
                var result = await _databaseService.ExecuteScalarAsync(query);
                var caseNumber = Convert.ToInt32(result);
                var year = DateTime.Now.Year;
                
                return $"CASE-{year}-{caseNumber:D4}";
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.GenerateNextCaseReference", ex);
                throw;
            }
        }
        
        public async Task<string> GenerateNextOfficeReferenceAsync()
        {
            try
            {
                var query = @"
                    SELECT COUNT(*) + 1 
                    FROM LegalCases 
                    WHERE YEAR(CreatedDate) = YEAR(GETDATE())";
                
                var result = await _databaseService.ExecuteScalarAsync(query);
                var caseNumber = Convert.ToInt32(result);
                var year = DateTime.Now.Year;
                
                return $"OFF-{year}-{caseNumber:D4}";
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseService.GenerateNextOfficeReference", ex);
                throw;
            }
        }
        
        private LegalCase MapDataRowToLegalCase(DataRow row)
        {
            return new LegalCase
            {
                CaseId = (int)row["CaseId"],
                CaseReference = row["CaseReference"].ToString()!,
                OfficeReference = row["OfficeReference"].ToString()!,
                CaseTitle = row["CaseTitle"].ToString()!,
                CaseTitleFr = row["CaseTitleFr"] as string,
                CaseTypeId = (int)row["CaseTypeId"],
                ClientId = (int)row["ClientId"],
                CourtId = row["CourtId"] as int?,
                CourtCaseNumber = row["CourtCaseNumber"] as string,
                CaseStatus = row["CaseStatus"].ToString()!,
                Priority = row["Priority"].ToString()!,
                OpenDate = (DateTime)row["OpenDate"],
                CloseDate = row["CloseDate"] as DateTime?,
                Description = row["Description"] as string,
                InternalNotes = row["InternalNotes"] as string,
                AssignedLawyer = row["AssignedLawyer"] as int?,
                IsActive = (bool)row["IsActive"],
                CreatedDate = (DateTime)row["CreatedDate"],
                CreatedBy = row["CreatedBy"] as int?,
                // Additional display properties
                ClientName = row["ClientName"] as string,
                LawyerName = row["LawyerName"] as string,
                CourtName = row["CourtName"] as string
            };
        }
    }
}
