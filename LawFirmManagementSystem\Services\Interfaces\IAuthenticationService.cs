using LawFirmManagementSystem.Models.Entities;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة المصادقة
    /// Authentication service interface
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// مصادقة المستخدم
        /// Authenticate user
        /// </summary>
        Task<User?> AuthenticateAsync(string username, string password);

        /// <summary>
        /// تسجيل الخروج
        /// Logout user
        /// </summary>
        Task<bool> LogoutAsync(string sessionToken);

        /// <summary>
        /// التحقق من صحة الجلسة
        /// Validate session
        /// </summary>
        Task<bool> ValidateSessionAsync(string sessionToken);

        /// <summary>
        /// تجديد الجلسة
        /// Refresh session
        /// </summary>
        Task<bool> RefreshSessionAsync(string sessionToken);

        /// <summary>
        /// تغيير كلمة المرور
        /// Change password
        /// </summary>
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// Reset password
        /// </summary>
        Task<bool> ResetPasswordAsync(string email);

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// Validate password strength
        /// </summary>
        bool ValidatePasswordStrength(string password);

        /// <summary>
        /// تشفير كلمة المرور
        /// Hash password
        /// </summary>
        string HashPassword(string password);

        /// <summary>
        /// التحقق من كلمة المرور
        /// Verify password
        /// </summary>
        bool VerifyPassword(string password, string hashedPassword);
    }
}
