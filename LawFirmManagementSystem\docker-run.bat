@echo off
echo ========================================
echo تشغيل نظام إدارة مكتب المحاماة باستخدام Docker
echo Running Law Firm Management System with Docker
echo ========================================
echo.

:: Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Docker غير مثبت
    echo Error: Docker is not installed
    echo يرجى تثبيت Docker Desktop من: https://www.docker.com/products/docker-desktop
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo Docker مثبت بنجاح
echo Docker is installed successfully
echo.

:: Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Docker غير قيد التشغيل
    echo Error: Docker is not running
    echo يرجى تشغيل Docker Desktop
    echo Please start Docker Desktop
    pause
    exit /b 1
)

echo Docker قيد التشغيل
echo Docker is running
echo.

echo اختر بيئة التشغيل:
echo Choose environment:
echo 1. بيئة التطوير (Development)
echo 2. بيئة الإنتاج (Production)
echo 3. قاعدة البيانات فقط (Database only)
echo 4. إيقاف جميع الخدمات (Stop all services)
echo 5. عرض حالة الخدمات (Show services status)
echo.

set /p CHOICE="اختر رقم (1-5) / Choose number (1-5): "

if "%CHOICE%"=="1" goto development
if "%CHOICE%"=="2" goto production
if "%CHOICE%"=="3" goto database_only
if "%CHOICE%"=="4" goto stop_services
if "%CHOICE%"=="5" goto show_status
goto invalid_choice

:development
echo تشغيل بيئة التطوير...
echo Starting development environment...
echo.

echo إيقاف الخدمات الموجودة...
echo Stopping existing services...
docker-compose -f docker-compose.dev.yml down

echo بناء وتشغيل الخدمات...
echo Building and starting services...
docker-compose -f docker-compose.dev.yml up -d --build

if %errorlevel% neq 0 (
    echo خطأ في تشغيل بيئة التطوير
    echo Error starting development environment
    goto end
)

echo ========================================
echo تم تشغيل بيئة التطوير بنجاح!
echo Development environment started successfully!
echo ========================================
echo.

echo الخدمات المتاحة:
echo Available services:
echo.
echo - SQL Server: localhost:1434
echo   المستخدم / User: sa
echo   كلمة المرور / Password: DevPassword123!
echo.
echo - Redis: localhost:6380
echo.
echo - MailHog (Email Testing): http://localhost:8025
echo.
echo - Elasticsearch: http://localhost:9201
echo.
echo - Kibana: http://localhost:5602
echo.
echo - Seq (Logging): http://localhost:5341
echo.
echo - Jaeger (Tracing): http://localhost:16686
echo.
echo - Prometheus: http://localhost:9090
echo.
echo - Grafana: http://localhost:3000
echo   المستخدم / User: admin
echo   كلمة المرور / Password: admin
echo.
echo - MinIO: http://localhost:9001
echo   المستخدم / User: minioadmin
echo   كلمة المرور / Password: minioadmin123
echo.
echo - Adminer (DB Management): http://localhost:8080
echo.
echo - Portainer: https://localhost:9443
echo.

goto end

:production
echo تشغيل بيئة الإنتاج...
echo Starting production environment...
echo.

echo إيقاف الخدمات الموجودة...
echo Stopping existing services...
docker-compose down

echo بناء وتشغيل الخدمات...
echo Building and starting services...
docker-compose up -d --build

if %errorlevel% neq 0 (
    echo خطأ في تشغيل بيئة الإنتاج
    echo Error starting production environment
    goto end
)

echo ========================================
echo تم تشغيل بيئة الإنتاج بنجاح!
echo Production environment started successfully!
echo ========================================
echo.

echo الخدمات المتاحة:
echo Available services:
echo.
echo - SQL Server: localhost:1433
echo - Redis: localhost:6379
echo - Elasticsearch: http://localhost:9200
echo - Kibana: http://localhost:5601
echo.

goto end

:database_only
echo تشغيل قاعدة البيانات فقط...
echo Starting database only...
echo.

docker-compose -f docker-compose.dev.yml up -d sqlserver-dev

if %errorlevel% neq 0 (
    echo خطأ في تشغيل قاعدة البيانات
    echo Error starting database
    goto end
)

echo ========================================
echo تم تشغيل قاعدة البيانات بنجاح!
echo Database started successfully!
echo ========================================
echo.

echo معلومات الاتصال:
echo Connection details:
echo Server: localhost:1434
echo User: sa
echo Password: DevPassword123!
echo.

goto end

:stop_services
echo إيقاف جميع الخدمات...
echo Stopping all services...
echo.

docker-compose down
docker-compose -f docker-compose.dev.yml down

echo تنظيف الموارد غير المستخدمة...
echo Cleaning up unused resources...
docker system prune -f

echo ========================================
echo تم إيقاف جميع الخدمات بنجاح!
echo All services stopped successfully!
echo ========================================

goto end

:show_status
echo حالة الخدمات:
echo Services status:
echo.

echo خدمات الإنتاج:
echo Production services:
docker-compose ps

echo.
echo خدمات التطوير:
echo Development services:
docker-compose -f docker-compose.dev.yml ps

echo.
echo استخدام الموارد:
echo Resource usage:
docker stats --no-stream

goto end

:invalid_choice
echo اختيار غير صحيح
echo Invalid choice
goto end

:end
echo.
pause
