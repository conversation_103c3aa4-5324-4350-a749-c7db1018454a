using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان جلسة المستخدم
    /// User session entity
    /// </summary>
    [Table("UserSessions")]
    public class UserSession : BaseEntity
    {
        [Required]
        [Column("UserId")]
        public int UserId { get; set; }

        [Required]
        [StringLength(255)]
        [Column("SessionToken")]
        public string SessionToken { get; set; } = string.Empty;

        [Required]
        [Column("LoginTime")]
        public DateTime LoginTime { get; set; } = DateTime.Now;

        [Column("LogoutTime")]
        public DateTime? LogoutTime { get; set; }

        [Column("LastActivityTime")]
        public DateTime LastActivityTime { get; set; } = DateTime.Now;

        [Column("ExpiryTime")]
        public DateTime ExpiryTime { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "نشط"; // نشط، منتهي، مسجل خروج

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(100)]
        [Column("IpAddress")]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        [Column("UserAgent")]
        public string? UserAgent { get; set; }

        [StringLength(100)]
        [Column("DeviceName")]
        public string? DeviceName { get; set; }

        [StringLength(50)]
        [Column("DeviceType")]
        public string? DeviceType { get; set; } // Desktop, Mobile, Tablet

        [StringLength(50)]
        [Column("OperatingSystem")]
        public string? OperatingSystem { get; set; }

        [StringLength(50)]
        [Column("Browser")]
        public string? Browser { get; set; }

        [StringLength(100)]
        [Column("Location")]
        public string? Location { get; set; }

        [StringLength(100)]
        [Column("LocationFr")]
        public string? LocationFr { get; set; }

        [Column("IsRemembered")]
        public bool IsRemembered { get; set; } = false;

        [Column("IsTwoFactorAuthenticated")]
        public bool IsTwoFactorAuthenticated { get; set; } = false;

        [Column("TwoFactorAuthTime")]
        public DateTime? TwoFactorAuthTime { get; set; }

        [Column("IsSecure")]
        public bool IsSecure { get; set; } = false;

        [Column("SessionDuration")]
        public int SessionDuration { get; set; } = 30; // بالدقائق

        [Column("IdleTimeout")]
        public int IdleTimeout { get; set; } = 15; // بالدقائق

        [Column("WarningTime")]
        public DateTime? WarningTime { get; set; }

        [Column("WarningCount")]
        public int WarningCount { get; set; } = 0;

        [Column("IsForced")]
        public bool IsForced { get; set; } = false;

        [StringLength(500)]
        [Column("ForceReason")]
        public string? ForceReason { get; set; }

        [StringLength(500)]
        [Column("ForceReasonFr")]
        public string? ForceReasonFr { get; set; }

        [Column("ActivityCount")]
        public int ActivityCount { get; set; } = 0;

        [Column("PageViews")]
        public int PageViews { get; set; } = 0;

        [StringLength(2000)]
        [Column("LastPage")]
        public string? LastPage { get; set; }

        [StringLength(2000)]
        [Column("RefererUrl")]
        public string? RefererUrl { get; set; }

        [Column("DataTransferred")]
        public long DataTransferred { get; set; } = 0; // بالبايت

        [Column("RequestCount")]
        public int RequestCount { get; set; } = 0;

        [Column("ErrorCount")]
        public int ErrorCount { get; set; } = 0;

        [StringLength(2000)]
        [Column("LastError")]
        public string? LastError { get; set; }

        [Column("LastErrorTime")]
        public DateTime? LastErrorTime { get; set; }

        [StringLength(2000)]
        [Column("SessionData")]
        public string? SessionData { get; set; } // JSON data

        [Column("IsBackground")]
        public bool IsBackground { get; set; } = false;

        [Column("BackgroundStartTime")]
        public DateTime? BackgroundStartTime { get; set; }

        [Column("MaxConcurrentSessions")]
        public int MaxConcurrentSessions { get; set; } = 1;

        [Column("ConcurrentSessionCount")]
        public int ConcurrentSessionCount { get; set; } = 1;

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// الحصول على حالة الجلسة حسب اللغة
        /// Get session status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// التحقق من نشاط الجلسة
        /// Check if session is active
        /// </summary>
        public bool IsActive()
        {
            return Status == "نشط" && ExpiryTime > DateTime.Now;
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الجلسة
        /// Check if session is expired
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryTime <= DateTime.Now;
        }

        /// <summary>
        /// التحقق من خمول الجلسة
        /// Check if session is idle
        /// </summary>
        public bool IsIdle()
        {
            return LastActivityTime.AddMinutes(IdleTimeout) <= DateTime.Now;
        }

        /// <summary>
        /// الحصول على مدة الجلسة
        /// Get session duration
        /// </summary>
        public TimeSpan GetSessionDuration()
        {
            var endTime = LogoutTime ?? DateTime.Now;
            return endTime - LoginTime;
        }

        /// <summary>
        /// الحصول على مدة الخمول
        /// Get idle duration
        /// </summary>
        public TimeSpan GetIdleDuration()
        {
            return DateTime.Now - LastActivityTime;
        }

        /// <summary>
        /// تحديث نشاط الجلسة
        /// Update session activity
        /// </summary>
        public void UpdateActivity(string? page = null)
        {
            LastActivityTime = DateTime.Now;
            ActivityCount++;
            PageViews++;
            
            if (!string.IsNullOrEmpty(page))
            {
                LastPage = page;
            }
            
            // تمديد انتهاء الجلسة
            ExtendSession();
        }

        /// <summary>
        /// تمديد الجلسة
        /// Extend session
        /// </summary>
        public void ExtendSession()
        {
            ExpiryTime = DateTime.Now.AddMinutes(SessionDuration);
        }

        /// <summary>
        /// إنهاء الجلسة
        /// End session
        /// </summary>
        public void EndSession(string reason = "تسجيل خروج عادي", string? reasonFr = null)
        {
            Status = "مسجل خروج";
            StatusFr = "Logged Out";
            LogoutTime = DateTime.Now;
            
            if (IsForced)
            {
                ForceReason = reason;
                ForceReasonFr = reasonFr;
            }
        }

        /// <summary>
        /// انتهاء صلاحية الجلسة
        /// Expire session
        /// </summary>
        public void ExpireSession()
        {
            Status = "منتهي";
            StatusFr = "Expired";
            LogoutTime = DateTime.Now;
        }

        /// <summary>
        /// إجبار إنهاء الجلسة
        /// Force end session
        /// </summary>
        public void ForceEndSession(string reason, string? reasonFr = null)
        {
            IsForced = true;
            ForceReason = reason;
            ForceReasonFr = reasonFr;
            EndSession(reason, reasonFr);
        }

        /// <summary>
        /// تسجيل خطأ في الجلسة
        /// Log session error
        /// </summary>
        public void LogError(string error)
        {
            ErrorCount++;
            LastError = error;
            LastErrorTime = DateTime.Now;
        }

        /// <summary>
        /// إرسال تحذير انتهاء الجلسة
        /// Send session expiry warning
        /// </summary>
        public void SendExpiryWarning()
        {
            WarningTime = DateTime.Now;
            WarningCount++;
        }

        /// <summary>
        /// التحقق من الحاجة لتحذير انتهاء الجلسة
        /// Check if session expiry warning is needed
        /// </summary>
        public bool NeedsExpiryWarning(int warningMinutes = 5)
        {
            var warningTime = ExpiryTime.AddMinutes(-warningMinutes);
            return DateTime.Now >= warningTime && 
                   (WarningTime == null || WarningTime.Value < warningTime);
        }

        /// <summary>
        /// تحديث بيانات النقل
        /// Update transfer data
        /// </summary>
        public void UpdateTransferData(long bytes)
        {
            DataTransferred += bytes;
            RequestCount++;
        }

        /// <summary>
        /// تحديث معلومات الجهاز
        /// Update device information
        /// </summary>
        public void UpdateDeviceInfo(string? deviceName = null, string? deviceType = null, 
            string? os = null, string? browser = null)
        {
            if (!string.IsNullOrEmpty(deviceName)) DeviceName = deviceName;
            if (!string.IsNullOrEmpty(deviceType)) DeviceType = deviceType;
            if (!string.IsNullOrEmpty(os)) OperatingSystem = os;
            if (!string.IsNullOrEmpty(browser)) Browser = browser;
        }

        /// <summary>
        /// تحديث الموقع الجغرافي
        /// Update location
        /// </summary>
        public void UpdateLocation(string location, string? locationFr = null)
        {
            Location = location;
            LocationFr = locationFr;
        }
    }
}
