using System;
using System.ComponentModel.DataAnnotations;

namespace LawOfficeManagement.Models
{
    public class OfficeSettings
    {
        public int SettingId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string OfficeName { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? ICE { get; set; }
        
        [StringLength(50)]
        public string? CommercialRegister { get; set; }
        
        [StringLength(500)]
        public string? Address { get; set; }
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(100)]
        public string Country { get; set; } = "المغرب";
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Fax { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [StringLength(200)]
        public string? Website { get; set; }
        
        public byte[]? Logo { get; set; }
        
        [StringLength(10)]
        public string DefaultLanguage { get; set; } = "ar";
        
        [StringLength(20)]
        public string Theme { get; set; } = "Light";
        
        [StringLength(500)]
        public string? BackupPath { get; set; }
        
        public bool AutoBackupEnabled { get; set; } = true;
        
        public int BackupFrequency { get; set; } = 24; // ساعات
        
        public string? EmailSettings { get; set; } // JSON
        
        public string? WhatsAppSettings { get; set; } // JSON
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
    }
    
    public class ErrorLog
    {
        public int LogId { get; set; }
        
        [Required]
        [StringLength(20)]
        public string LogLevel { get; set; } = string.Empty; // Error, Warning, Info
        
        [Required]
        public string Message { get; set; } = string.Empty;
        
        public string? Exception { get; set; }
        
        public string? StackTrace { get; set; }
        
        [StringLength(200)]
        public string? Source { get; set; }
        
        public int? UserId { get; set; }
        
        [StringLength(100)]
        public string? UserName { get; set; }
        
        [StringLength(100)]
        public string? MachineName { get; set; }
        
        public DateTime LogDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual User? User { get; set; }
    }
    
    public class Notification
    {
        public int NotificationId { get; set; }
        
        public int? UserId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public string Message { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? NotificationType { get; set; } // Hearing, Appointment, Payment, System
        
        public int? RelatedId { get; set; } // معرف العنصر المرتبط
        
        [StringLength(50)]
        public string? RelatedType { get; set; } // نوع العنصر المرتبط
        
        public bool IsRead { get; set; } = false;
        
        public bool IsSent { get; set; } = false;
        
        [StringLength(20)]
        public string? SendMethod { get; set; } // Email, WhatsApp, Internal
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime? ReadDate { get; set; }
        
        // Navigation properties
        public virtual User? User { get; set; }
    }
    
    public class LoginHistory
    {
        public int LoginId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        public DateTime LoginDate { get; set; } = DateTime.Now;
        
        public DateTime? LogoutDate { get; set; }
        
        [StringLength(50)]
        public string? IPAddress { get; set; }
        
        [StringLength(500)]
        public string? UserAgent { get; set; }
        
        public bool IsSuccessful { get; set; } = true;
        
        [StringLength(200)]
        public string? FailureReason { get; set; }
        
        // Navigation properties
        public virtual User? User { get; set; }
    }
    
    public class BackupHistory
    {
        public int BackupId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string BackupName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(1000)]
        public string BackupPath { get; set; } = string.Empty;
        
        public long? BackupSize { get; set; }
        
        [StringLength(20)]
        public string? BackupType { get; set; } // Full, Incremental
        
        public bool IsAutomatic { get; set; } = false;
        
        public DateTime BackupDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        [StringLength(50)]
        public string Status { get; set; } = "مكتملة";
        
        public string? ErrorMessage { get; set; }
        
        // Navigation properties
        public virtual User? Creator { get; set; }
    }
    
    public class Document
    {
        public int DocumentId { get; set; }
        
        [Required]
        public int CaseId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string DocumentName { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? DocumentType { get; set; }
        
        [StringLength(1000)]
        public string? FilePath { get; set; }
        
        public long? FileSize { get; set; }
        
        [StringLength(100)]
        public string? MimeType { get; set; }
        
        public DateTime UploadDate { get; set; } = DateTime.Now;
        
        public int? UploadedBy { get; set; }
        
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        public virtual LegalCase? LegalCase { get; set; }
        public virtual User? Uploader { get; set; }
    }
}
