using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using LawFirmManagementSystem.Data;
using LawFirmManagementSystem.Services;
using LawFirmManagementSystem.Forms;
using LawFirmManagementSystem.Forms.Authentication;
using LawFirmManagementSystem.Forms.Main;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LawFirmManagementSystem;

/// <summary>
/// نقطة دخول التطبيق الرئيسية
/// Main application entry point
/// </summary>
static class Program
{
    private static IHost? _host;
    private static IServiceProvider? _serviceProvider;

    /// <summary>
    /// نقطة الدخول الرئيسية للتطبيق
    /// The main entry point for the application
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        // Configure application
        ApplicationConfiguration.Initialize();

        try
        {
            // Build and configure the host
            await BuildHostAsync();

            // Start the host
            await _host!.StartAsync();

            // Get service provider
            _serviceProvider = _host.Services;

            // Initialize database
            await InitializeDatabaseAsync();

            // Show splash screen
            ShowSplashScreen();

            // Run the main application
            RunApplication();
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"خطأ في بدء التطبيق:\n{ex.Message}\n\nError starting application:\n{ex.Message}",
                "خطأ في النظام / System Error",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
        finally
        {
            // Cleanup
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
        }
    }

    /// <summary>
    /// بناء وتكوين المضيف
    /// Build and configure the host
    /// </summary>
    private static async Task BuildHostAsync()
    {
        var builder = Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(AppDomain.CurrentDomain.BaseDirectory);
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                // Add database services
                services.AddDatabaseServices(context.Configuration);

                // Add application services
                services.AddApplicationServices();

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                });
            })
            .UseConsoleLifetime();

        _host = builder.Build();
    }

    /// <summary>
    /// تهيئة قاعدة البيانات
    /// Initialize database
    /// </summary>
    private static async Task InitializeDatabaseAsync()
    {
        try
        {
            // Test database connection
            var connectionTest = await DatabaseConfiguration.TestConnectionAsync(_serviceProvider!);
            if (!connectionTest)
            {
                throw new Exception("فشل في الاتصال بقاعدة البيانات / Failed to connect to database");
            }

            // Ensure database is created
            await DatabaseConfiguration.EnsureDatabaseCreatedAsync(_serviceProvider!);

            // Apply any pending migrations
            await DatabaseConfiguration.ApplyMigrationsAsync(_serviceProvider!);

            Console.WriteLine("تم تهيئة قاعدة البيانات بنجاح / Database initialized successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في تهيئة قاعدة البيانات / Database initialization error: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// عرض شاشة البداية
    /// Show splash screen
    /// </summary>
    private static void ShowSplashScreen()
    {
        try
        {
            var splashForm = new SplashForm();
            splashForm.ShowDialog();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في عرض شاشة البداية / Splash screen error: {ex.Message}");
        }
    }

    /// <summary>
    /// تشغيل التطبيق الرئيسي
    /// Run main application
    /// </summary>
    private static void RunApplication()
    {
        try
        {
            // Show login form
            using var loginForm = new LoginForm(_serviceProvider!);
            var loginResult = loginForm.ShowDialog();

            if (loginResult == DialogResult.OK)
            {
                // Show main form
                Application.Run(new MainForm(_serviceProvider!));
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"خطأ في تشغيل التطبيق:\n{ex.Message}\n\nError running application:\n{ex.Message}",
                "خطأ في النظام / System Error",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// الحصول على مقدم الخدمات
    /// Get service provider
    /// </summary>
    public static IServiceProvider? GetServiceProvider()
    {
        return _serviceProvider;
    }

    /// <summary>
    /// الحصول على خدمة محددة
    /// Get specific service
    /// </summary>
    public static T? GetService<T>() where T : class
    {
        return _serviceProvider?.GetService<T>();
    }

    /// <summary>
    /// الحصول على خدمة مطلوبة
    /// Get required service
    /// </summary>
    public static T GetRequiredService<T>() where T : class
    {
        if (_serviceProvider == null)
            throw new InvalidOperationException("Service provider is not initialized");

        return _serviceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// إنهاء التطبيق بأمان
    /// Shutdown application safely
    /// </summary>
    public static async Task ShutdownAsync()
    {
        try
        {
            if (_host != null)
            {
                await _host.StopAsync(TimeSpan.FromSeconds(5));
                _host.Dispose();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في إنهاء التطبيق / Application shutdown error: {ex.Message}");
        }
    }
}