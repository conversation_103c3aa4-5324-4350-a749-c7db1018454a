using LawFirmManagementSystem.Models.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة العملاء
    /// Client service interface
    /// </summary>
    public interface IClientService
    {
        // Client Management
        Task<Client?> GetClientByIdAsync(int id);
        Task<Client?> GetClientByReferenceAsync(string reference);
        Task<Client?> GetClientByCINAsync(string cin);
        Task<Client?> GetClientByEmailAsync(string email);
        Task<IEnumerable<Client>> GetAllClientsAsync();
        Task<IEnumerable<Client>> GetActiveClientsAsync();
        Task<Client> CreateClientAsync(Client client);
        Task<Client> UpdateClientAsync(Client client);
        Task<bool> DeleteClientAsync(int id);
        Task<bool> ActivateClientAsync(int id);
        Task<bool> DeactivateClientAsync(int id);

        // Client Information
        Task<bool> UpdateClientContactInfoAsync(int clientId, string? email, string? phone, string? mobile, string? address);
        Task<bool> UpdateClientPersonalInfoAsync(int clientId, string fullName, string? fullNameFr, DateTime? dateOfBirth, string? nationality);
        Task<bool> UpdateClientBusinessInfoAsync(int clientId, string? profession, string? employer, string? commercialRegister, string? ice);
        Task<bool> UpdateClientPreferencesAsync(int clientId, string preferredLanguage, string preferredContactMethod);

        // Client Financial Information
        Task<decimal> GetClientBalanceAsync(int clientId);
        Task<decimal> GetClientTotalPaidAsync(int clientId);
        Task<decimal> GetClientTotalDueAsync(int clientId);
        Task<bool> UpdateClientBalanceAsync(int clientId);
        Task<bool> SetClientCreditLimitAsync(int clientId, decimal? creditLimit);

        // Client Cases
        Task<IEnumerable<LegalCase>> GetClientCasesAsync(int clientId);
        Task<IEnumerable<LegalCase>> GetClientActiveCasesAsync(int clientId);
        Task<int> GetClientCasesCountAsync(int clientId);

        // Client Invoices and Payments
        Task<IEnumerable<Invoice>> GetClientInvoicesAsync(int clientId);
        Task<IEnumerable<Payment>> GetClientPaymentsAsync(int clientId);
        Task<IEnumerable<Invoice>> GetClientUnpaidInvoicesAsync(int clientId);
        Task<decimal> GetClientOutstandingAmountAsync(int clientId);

        // Client Documents
        Task<IEnumerable<Document>> GetClientDocumentsAsync(int clientId);
        Task<bool> AddClientDocumentAsync(int clientId, Document document);
        Task<bool> RemoveClientDocumentAsync(int clientId, int documentId);

        // Client Contact History
        Task<IEnumerable<ClientContact>> GetClientContactHistoryAsync(int clientId);
        Task<ClientContact> AddClientContactAsync(ClientContact contact);
        Task<ClientContact> UpdateClientContactAsync(ClientContact contact);
        Task<bool> DeleteClientContactAsync(int contactId);
        Task<DateTime?> GetLastContactDateAsync(int clientId);
        Task<bool> UpdateLastContactDateAsync(int clientId);

        // Client Appointments
        Task<IEnumerable<Appointment>> GetClientAppointmentsAsync(int clientId);
        Task<IEnumerable<Appointment>> GetClientUpcomingAppointmentsAsync(int clientId);
        Task<Appointment?> GetNextClientAppointmentAsync(int clientId);

        // Client Statistics
        Task<int> GetTotalClientsCountAsync();
        Task<int> GetActiveClientsCountAsync();
        Task<int> GetVIPClientsCountAsync();
        Task<IEnumerable<Client>> GetRecentClientsAsync(int count = 10);
        Task<IEnumerable<Client>> GetTopClientsByRevenueAsync(int count = 10);

        // Search and Filter
        Task<IEnumerable<Client>> SearchClientsAsync(string searchTerm);
        Task<IEnumerable<Client>> GetClientsByTypeAsync(string clientType);
        Task<IEnumerable<Client>> GetClientsByStatusAsync(string status);
        Task<IEnumerable<Client>> GetClientsByLocationAsync(string city, string? province = null);
        Task<IEnumerable<Client>> GetVIPClientsAsync();

        // Client Validation
        Task<bool> IsClientReferenceAvailableAsync(string reference, int? excludeClientId = null);
        Task<bool> IsCINAvailableAsync(string cin, int? excludeClientId = null);
        Task<bool> IsEmailAvailableAsync(string email, int? excludeClientId = null);
        Task<bool> ValidateClientDataAsync(Client client);

        // Client Reports
        Task<byte[]> GenerateClientReportAsync(int clientId, string reportType, string language = "ar");
        Task<byte[]> GenerateClientStatementAsync(int clientId, DateTime fromDate, DateTime toDate, string language = "ar");
        Task<byte[]> GenerateClientCasesReportAsync(int clientId, string language = "ar");

        // Client Communication
        Task<bool> SendClientEmailAsync(int clientId, string subject, string body, IEnumerable<string>? attachments = null);
        Task<bool> SendClientSMSAsync(int clientId, string message);
        Task<bool> SendClientWhatsAppAsync(int clientId, string message);

        // Client Import/Export
        Task<IEnumerable<Client>> ImportClientsAsync(byte[] fileData, string fileName);
        Task<byte[]> ExportClientsAsync(IEnumerable<int> clientIds, string format = "Excel");
        Task<byte[]> ExportAllClientsAsync(string format = "Excel");

        // Client Merge
        Task<bool> MergeClientsAsync(int primaryClientId, int secondaryClientId);
        Task<bool> CanMergeClientsAsync(int primaryClientId, int secondaryClientId);

        // Client Archive
        Task<bool> ArchiveClientAsync(int clientId, string reason);
        Task<bool> UnarchiveClientAsync(int clientId);
        Task<IEnumerable<Client>> GetArchivedClientsAsync();
    }
}
