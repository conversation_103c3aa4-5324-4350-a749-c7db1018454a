-- إدراج البيانات الأولية لنظام إدارة مكتب المحاماة
USE LawOfficeDB;
GO

-- إنشاء المستخدم الافتراضي (admin/admin123)
-- كلمة المرور: admin123
-- Salt: defaultSalt123
-- Hash: SHA256(admin123 + defaultSalt123)

DECLARE @DefaultPasswordHash NVARCHAR(255) = 'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg='; -- admin123 + defaultSalt123
DECLARE @DefaultSalt NVARCHAR(255) = 'defaultSalt123';

-- التحقق من عدم وجود المستخدم الافتراضي
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, Role, IsActive, CreatedDate)
    VALUES ('admin', @DefaultPasswordHash, @DefaultSalt, N'مدير النظام', '<EMAIL>', 'Admin', 1, GETDATE());
    
    PRINT 'تم إنشاء المستخدم الافتراضي بنجاح';
    PRINT 'اسم المستخدم: admin';
    PRINT 'كلمة المرور: admin123';
END
ELSE
BEGIN
    PRINT 'المستخدم الافتراضي موجود بالفعل';
END

-- إدراج أنواع القضايا الأساسية
IF NOT EXISTS (SELECT 1 FROM CaseTypes)
BEGIN
    INSERT INTO CaseTypes (TypeName, TypeNameFr, Description) VALUES
    (N'قضايا مدنية', 'Affaires civiles', N'القضايا المدنية والتجارية العامة'),
    (N'قضايا جنائية', 'Affaires pénales', N'القضايا الجنائية والجزائية'),
    (N'قضايا تجارية', 'Affaires commerciales', N'القضايا التجارية والاقتصادية'),
    (N'قضايا أسرية', 'Affaires familiales', N'قضايا الأحوال الشخصية والأسرة'),
    (N'قضايا إدارية', 'Affaires administratives', N'القضايا الإدارية والعمومية'),
    (N'قضايا عقارية', 'Affaires immobilières', N'القضايا العقارية والملكية'),
    (N'قضايا عمالية', 'Affaires du travail', N'قضايا العمل والضمان الاجتماعي'),
    (N'قضايا ضريبية', 'Affaires fiscales', N'القضايا الضريبية والجمركية'),
    (N'قضايا بنكية', 'Affaires bancaires', N'القضايا البنكية والمالية'),
    (N'قضايا تأمين', 'Affaires d''assurance', N'قضايا التأمين والتعويضات');
    
    PRINT 'تم إدراج أنواع القضايا الأساسية';
END

-- إدراج أنواع المصاريف الأساسية
IF NOT EXISTS (SELECT 1 FROM ExpenseTypes)
BEGIN
    INSERT INTO ExpenseTypes (TypeName, TypeNameFr, Description) VALUES
    (N'أتعاب المحاماة', 'Honoraires d''avocat', N'أتعاب المحامي عن الخدمات القانونية'),
    (N'رسوم المحكمة', 'Frais de tribunal', N'الرسوم القضائية والإجراءات'),
    (N'رسوم التحرير', 'Frais de rédaction', N'رسوم تحرير العقود والوثائق'),
    (N'مصاريف التنقل', 'Frais de déplacement', N'مصاريف السفر والتنقل'),
    (N'رسوم الخبرة', 'Frais d''expertise', N'رسوم الخبراء والاستشاريين'),
    (N'رسوم الترجمة', 'Frais de traduction', N'رسوم ترجمة الوثائق'),
    (N'رسوم التوثيق', 'Frais de notarisation', N'رسوم التوثيق والتصديق'),
    (N'مصاريف الطباعة', 'Frais d''impression', N'مصاريف طباعة الوثائق'),
    (N'رسوم التسجيل', 'Frais d''enregistrement', N'رسوم تسجيل العقود والوثائق'),
    (N'مصاريف أخرى', 'Autres frais', N'مصاريف متنوعة أخرى');
    
    PRINT 'تم إدراج أنواع المصاريف الأساسية';
END

-- إدراج المحاكم الأساسية في المغرب
IF NOT EXISTS (SELECT 1 FROM Courts)
BEGIN
    INSERT INTO Courts (CourtName, CourtNameFr, CourtType, Jurisdiction, City) VALUES
    (N'المحكمة الابتدائية بالرباط', 'Tribunal de première instance de Rabat', N'ابتدائية', N'مدني وجنائي', N'الرباط'),
    (N'المحكمة الابتدائية بالدار البيضاء', 'Tribunal de première instance de Casablanca', N'ابتدائية', N'مدني وجنائي', N'الدار البيضاء'),
    (N'المحكمة التجارية بالدار البيضاء', 'Tribunal de commerce de Casablanca', N'تجارية', N'تجاري', N'الدار البيضاء'),
    (N'محكمة الاستئناف بالرباط', 'Cour d''appel de Rabat', N'استئناف', N'استئنافي', N'الرباط'),
    (N'محكمة الاستئناف بالدار البيضاء', 'Cour d''appel de Casablanca', N'استئناف', N'استئنافي', N'الدار البيضاء'),
    (N'محكمة النقض', 'Cour de cassation', N'نقض', N'نقض', N'الرباط'),
    (N'المحكمة الإدارية بالرباط', 'Tribunal administratif de Rabat', N'إدارية', N'إداري', N'الرباط'),
    (N'المحكمة الابتدائية بفاس', 'Tribunal de première instance de Fès', N'ابتدائية', N'مدني وجنائي', N'فاس'),
    (N'المحكمة الابتدائية بمراكش', 'Tribunal de première instance de Marrakech', N'ابتدائية', N'مدني وجنائي', N'مراكش'),
    (N'المحكمة الابتدائية بطنجة', 'Tribunal de première instance de Tanger', N'ابتدائية', N'مدني وجنائي', N'طنجة');
    
    PRINT 'تم إدراج المحاكم الأساسية';
END

-- إدراج إعدادات المكتب الافتراضية
IF NOT EXISTS (SELECT 1 FROM OfficeSettings)
BEGIN
    INSERT INTO OfficeSettings (
        OfficeName, 
        Country, 
        DefaultLanguage, 
        Theme, 
        AutoBackupEnabled, 
        BackupFrequency,
        CreatedDate,
        ModifiedDate
    ) VALUES (
        N'مكتب المحاماة', 
        N'المغرب', 
        'ar', 
        'Light', 
        1, 
        24,
        GETDATE(),
        GETDATE()
    );
    
    PRINT 'تم إدراج إعدادات المكتب الافتراضية';
END

-- إنشاء فهارس إضافية لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Username_IsActive')
    CREATE INDEX IX_Users_Username_IsActive ON Users(Username, IsActive);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_LegalCases_Status_OpenDate')
    CREATE INDEX IX_LegalCases_Status_OpenDate ON LegalCases(CaseStatus, OpenDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Clients_FullName_IsActive')
    CREATE INDEX IX_Clients_FullName_IsActive ON Clients(FullName, IsActive);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Hearings_Date_Status')
    CREATE INDEX IX_Hearings_Date_Status ON Hearings(HearingDate, Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CaseExpenses_Date_IsPaid')
    CREATE INDEX IX_CaseExpenses_Date_IsPaid ON CaseExpenses(ExpenseDate, IsPaid);

PRINT 'تم إنشاء الفهارس الإضافية';

-- إنشاء إجراءات مخزنة أساسية
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetCasesSummary')
BEGIN
    EXEC('
    CREATE PROCEDURE sp_GetCasesSummary
    AS
    BEGIN
        SELECT 
            COUNT(*) as TotalCases,
            COUNT(CASE WHEN CaseStatus = N''جاري'' THEN 1 END) as ActiveCases,
            COUNT(CASE WHEN CaseStatus = N''مغلق'' THEN 1 END) as ClosedCases,
            COUNT(CASE WHEN DATEDIFF(day, OpenDate, GETDATE()) <= 30 THEN 1 END) as NewCases
        FROM LegalCases
        WHERE IsActive = 1;
    END');
    
    PRINT 'تم إنشاء الإجراء المخزن sp_GetCasesSummary';
END

IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetUpcomingHearings')
BEGIN
    EXEC('
    CREATE PROCEDURE sp_GetUpcomingHearings
        @Days INT = 7
    AS
    BEGIN
        SELECT 
            h.HearingId,
            h.HearingDate,
            h.HearingType,
            lc.CaseTitle,
            lc.CaseReference,
            c.FullName as ClientName,
            ct.CourtName
        FROM Hearings h
        INNER JOIN LegalCases lc ON h.CaseId = lc.CaseId
        INNER JOIN Clients c ON lc.ClientId = c.ClientId
        LEFT JOIN Courts ct ON lc.CourtId = ct.CourtId
        WHERE h.HearingDate BETWEEN GETDATE() AND DATEADD(day, @Days, GETDATE())
        AND h.Status = N''مجدولة''
        ORDER BY h.HearingDate;
    END');
    
    PRINT 'تم إنشاء الإجراء المخزن sp_GetUpcomingHearings';
END

PRINT 'تم الانتهاء من إدراج البيانات الأولية بنجاح';
GO
