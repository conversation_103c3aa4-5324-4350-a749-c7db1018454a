using System;
using System.ComponentModel.DataAnnotations;

namespace LawOfficeManagement.Models
{
    public class Client
    {
        public int ClientId { get; set; }
        
        [Required]
        public ClientType ClientType { get; set; }
        
        [Required]
        [StringLength(200)]
        public string FullName { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? FullNameFr { get; set; }
        
        [StringLength(20)]
        public string? CIN { get; set; }
        
        [StringLength(20)]
        public string? PassportNumber { get; set; }
        
        [StringLength(50)]
        public string? CompanyRegister { get; set; }
        
        [StringLength(50)]
        public string? ICE { get; set; }
        
        public DateTime? DateOfBirth { get; set; }
        
        [StringLength(10)]
        public string? Gender { get; set; }
        
        [StringLength(50)]
        public string Nationality { get; set; } = "مغربية";
        
        [StringLength(500)]
        public string? Address { get; set; }
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Mobile { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [StringLength(20)]
        public string? Fax { get; set; }
        
        [StringLength(100)]
        public string? Profession { get; set; }
        
        public string? Notes { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
        public virtual ICollection<LegalCase> LegalCases { get; set; } = new List<LegalCase>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
    }
    
    public enum ClientType
    {
        Individual,
        Company,
        Government
    }
}
