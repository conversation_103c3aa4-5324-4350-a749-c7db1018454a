{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=LawFirmManagementDB_Test;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information"}, "Console": {"IncludeScopes": true}, "File": {"Path": "Logs/testing-{Date}.log", "MinLevel": "Debug", "RollingInterval": "Day", "RetainedFileCountLimit": 3}}, "Application": {"Name": "نظام إدارة مكتب المحاماة - بيئة الاختبار", "NameFr": "Système de Gestion de Cabinet d'Avocat - Test", "Version": "1.0.0-test", "Environment": "Testing", "DebugMode": true, "SessionTimeoutMinutes": 240, "MaxLoginAttempts": 100, "LockoutDurationMinutes": 1}, "Security": {"PasswordPolicy": {"MinLength": 4, "RequireUppercase": false, "RequireLowercase": false, "RequireDigit": false, "RequireSpecialCharacter": false, "MaxAge": 3650, "PreventReuse": 1}, "TwoFactorAuthentication": {"Enabled": false}, "Encryption": {"Algorithm": "AES256", "KeySize": 256, "DebugMode": true}}, "Database": {"CommandTimeout": 120, "EnableRetryOnFailure": false, "MaxRetryCount": 1, "MaxRetryDelay": "00:00:10", "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "BackupRetentionDays": 1, "AutoBackupEnabled": false, "CleanupRetentionDays": 1}, "Email": {"SmtpServer": "localhost", "SmtpPort": 25, "EnableSsl": false, "Username": "", "Password": "", "FromEmail": "<EMAIL>", "FromName": "نظام إدارة مكتب المحاماة - اختبار", "FromNameFr": "Système de Gestion - Test", "TestMode": true}, "SMS": {"Provider": "<PERSON><PERSON>", "Enabled": false, "TestMode": true}, "WhatsApp": {"Provider": "<PERSON><PERSON>", "Enabled": false, "TestMode": true}, "FileStorage": {"BasePath": "Documents_Test", "MaxFileSize": 1048576, "AllowedExtensions": [".txt", ".pdf"], "EnableEncryption": false, "EnableVersioning": false, "EnableThumbnails": false}, "Reports": {"DefaultFormat": "PDF", "TemplatePath": "Templates/Reports_Test", "OutputPath": "Reports/Generated_Test", "RetentionDays": 1}, "Backup": {"Enabled": false, "BackupPath": "Backups_Test", "RetentionDays": 1, "CompressBackups": false, "IncludeDocuments": false}, "Audit": {"Enabled": true, "LogLevel": "Debug", "RetentionDays": 1, "LogSensitiveData": true, "LogUserActions": true, "LogSystemActions": true, "LogDataChanges": true}, "Performance": {"CacheEnabled": false, "CacheExpirationMinutes": 1, "DatabasePoolSize": 5, "MaxConcurrentUsers": 10, "EnableCompression": false}, "UI": {"DefaultTheme": "Testing", "ShowDebugInfo": true, "EnableHotReload": true}, "Integration": {"CourtSystems": {"Enabled": false, "TestMode": true}, "BankingSystems": {"Enabled": false, "TestMode": true}}, "Notifications": {"Email": {"Enabled": false, "TestMode": true}, "SMS": {"Enabled": false, "TestMode": true}, "InApp": {"Enabled": true, "RetentionDays": 1}}, "Testing": {"EnableMockServices": true, "EnableTestData": true, "EnableDetailedLogging": true, "SkipAuthentication": false, "SkipAuthorization": false, "EnablePerformanceProfiling": true, "EnableMemoryProfiling": true, "MockExternalServices": true, "UseInMemoryDatabase": false, "SeedTestData": true, "CleanupAfterTests": true}, "TestData": {"CreateTestUsers": true, "CreateTestClients": true, "CreateTestCases": true, "TestUserPassword": "test", "TestDataCount": 10}}