using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الموعد
    /// Appointment entity
    /// </summary>
    [Table("Appointments")]
    public class Appointment : BaseEntity
    {
        [Column("CaseId")]
        public int? CaseId { get; set; }

        [Column("ClientId")]
        public int? ClientId { get; set; }

        [Required]
        [StringLength(200)]
        [Column("Title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("TitleFr")]
        public string? TitleFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("AppointmentType")]
        public string AppointmentType { get; set; } = "استشارة"; // استشارة، اجتماع، مكالمة، زيارة

        [StringLength(50)]
        [Column("AppointmentTypeFr")]
        public string? AppointmentTypeFr { get; set; }

        [Required]
        [Column("AppointmentDate")]
        public DateTime AppointmentDate { get; set; }

        [Required]
        [Column("StartTime")]
        public TimeSpan StartTime { get; set; }

        [Required]
        [Column("EndTime")]
        public TimeSpan EndTime { get; set; }

        [Column("Duration")]
        public int Duration { get; set; } // بالدقائق

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مجدول"; // مجدول، مؤكد، جاري، مكتمل، ملغي، مؤجل

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(100)]
        [Column("Location")]
        public string? Location { get; set; }

        [StringLength(100)]
        [Column("LocationFr")]
        public string? LocationFr { get; set; }

        [StringLength(200)]
        [Column("Address")]
        public string? Address { get; set; }

        [StringLength(200)]
        [Column("AddressFr")]
        public string? AddressFr { get; set; }

        [StringLength(2000)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(2000)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [StringLength(2000)]
        [Column("Agenda")]
        public string? Agenda { get; set; }

        [StringLength(2000)]
        [Column("AgendaFr")]
        public string? AgendaFr { get; set; }

        [StringLength(2000)]
        [Column("PreparationNotes")]
        public string? PreparationNotes { get; set; }

        [StringLength(2000)]
        [Column("PreparationNotesFr")]
        public string? PreparationNotesFr { get; set; }

        [StringLength(2000)]
        [Column("Outcome")]
        public string? Outcome { get; set; }

        [StringLength(2000)]
        [Column("OutcomeFr")]
        public string? OutcomeFr { get; set; }

        [StringLength(2000)]
        [Column("FollowUpActions")]
        public string? FollowUpActions { get; set; }

        [StringLength(2000)]
        [Column("FollowUpActionsFr")]
        public string? FollowUpActionsFr { get; set; }

        [Column("AssignedLawyerId")]
        public int? AssignedLawyerId { get; set; }

        [StringLength(500)]
        [Column("Attendees")]
        public string? Attendees { get; set; }

        [StringLength(500)]
        [Column("AttendeesFr")]
        public string? AttendeesFr { get; set; }

        [Column("IsRecurring")]
        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        [Column("RecurrencePattern")]
        public string? RecurrencePattern { get; set; } // يومي، أسبوعي، شهري

        [StringLength(50)]
        [Column("RecurrencePatternFr")]
        public string? RecurrencePatternFr { get; set; }

        [Column("RecurrenceInterval")]
        public int? RecurrenceInterval { get; set; }

        [Column("RecurrenceEndDate")]
        public DateTime? RecurrenceEndDate { get; set; }

        [Column("IsAllDay")]
        public bool IsAllDay { get; set; } = false;

        [Column("IsPrivate")]
        public bool IsPrivate { get; set; } = false;

        [Column("IsImportant")]
        public bool IsImportant { get; set; } = false;

        [Column("RequiresPreparation")]
        public bool RequiresPreparation { get; set; } = false;

        [Column("ReminderEnabled")]
        public bool ReminderEnabled { get; set; } = true;

        [Column("ReminderMinutes")]
        public int ReminderMinutes { get; set; } = 15;

        [Column("ReminderSent")]
        public bool ReminderSent { get; set; } = false;

        [Column("ReminderSentDate")]
        public DateTime? ReminderSentDate { get; set; }

        [Column("EmailReminderSent")]
        public bool EmailReminderSent { get; set; } = false;

        [Column("SmsReminderSent")]
        public bool SmsReminderSent { get; set; } = false;

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; } = "#28a745";

        [Column("Fee")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Fee { get; set; }

        [Column("IsBillable")]
        public bool IsBillable { get; set; } = true;

        [Column("BillingRate")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? BillingRate { get; set; }

        [Column("ActualStartTime")]
        public DateTime? ActualStartTime { get; set; }

        [Column("ActualEndTime")]
        public DateTime? ActualEndTime { get; set; }

        [StringLength(100)]
        [Column("MeetingLink")]
        public string? MeetingLink { get; set; }

        [StringLength(100)]
        [Column("MeetingPassword")]
        public string? MeetingPassword { get; set; }

        [StringLength(50)]
        [Column("MeetingPlatform")]
        public string? MeetingPlatform { get; set; } // Zoom, Teams, Meet

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase? Case { get; set; }

        [ForeignKey("ClientId")]
        public virtual Client? Client { get; set; }

        [ForeignKey("AssignedLawyerId")]
        public virtual User? AssignedLawyer { get; set; }

        /// <summary>
        /// الحصول على عنوان الموعد حسب اللغة
        /// Get appointment title by language
        /// </summary>
        public string GetTitle(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(TitleFr) 
                ? TitleFr 
                : Title;
        }

        /// <summary>
        /// الحصول على نوع الموعد حسب اللغة
        /// Get appointment type by language
        /// </summary>
        public string GetAppointmentType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(AppointmentTypeFr) 
                ? AppointmentTypeFr 
                : AppointmentType;
        }

        /// <summary>
        /// الحصول على حالة الموعد حسب اللغة
        /// Get appointment status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// الحصول على الموقع حسب اللغة
        /// Get location by language
        /// </summary>
        public string GetLocation(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(LocationFr) 
                ? LocationFr 
                : Location ?? string.Empty;
        }

        /// <summary>
        /// الحصول على الوصف حسب اللغة
        /// Get description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// الحصول على التاريخ والوقت الكامل للبداية
        /// Get full start date and time
        /// </summary>
        public DateTime GetStartDateTime()
        {
            return AppointmentDate.Date + StartTime;
        }

        /// <summary>
        /// الحصول على التاريخ والوقت الكامل للنهاية
        /// Get full end date and time
        /// </summary>
        public DateTime GetEndDateTime()
        {
            return AppointmentDate.Date + EndTime;
        }

        /// <summary>
        /// حساب المدة بالدقائق
        /// Calculate duration in minutes
        /// </summary>
        public int CalculateDuration()
        {
            return (int)(EndTime - StartTime).TotalMinutes;
        }

        /// <summary>
        /// التحقق من انتهاء الموعد
        /// Check if appointment is completed
        /// </summary>
        public bool IsCompleted()
        {
            return Status == "مكتمل" || Status == "Completed";
        }

        /// <summary>
        /// التحقق من إلغاء الموعد
        /// Check if appointment is cancelled
        /// </summary>
        public bool IsCancelled()
        {
            return Status == "ملغي" || Status == "Cancelled";
        }

        /// <summary>
        /// التحقق من قرب موعد الموعد
        /// Check if appointment is upcoming
        /// </summary>
        public bool IsUpcoming(int minutesThreshold = 60)
        {
            var appointmentDateTime = GetStartDateTime();
            var now = DateTime.Now;
            return appointmentDateTime > now && appointmentDateTime <= now.AddMinutes(minutesThreshold);
        }

        /// <summary>
        /// بدء الموعد
        /// Start appointment
        /// </summary>
        public void StartAppointment()
        {
            ActualStartTime = DateTime.Now;
            Status = "جاري";
            StatusFr = "En cours";
        }

        /// <summary>
        /// إنهاء الموعد
        /// End appointment
        /// </summary>
        public void EndAppointment(string outcome, string? outcomeFr = null)
        {
            ActualEndTime = DateTime.Now;
            Status = "مكتمل";
            StatusFr = "Completed";
            Outcome = outcome;
            OutcomeFr = outcomeFr;
        }

        /// <summary>
        /// إلغاء الموعد
        /// Cancel appointment
        /// </summary>
        public void CancelAppointment(string reason)
        {
            Status = "ملغي";
            StatusFr = "Cancelled";
            Outcome = $"تم الإلغاء: {reason}";
            OutcomeFr = $"Annulé: {reason}";
        }

        /// <summary>
        /// تأجيل الموعد
        /// Postpone appointment
        /// </summary>
        public void PostponeAppointment(DateTime newDate, TimeSpan newStartTime, TimeSpan newEndTime)
        {
            AppointmentDate = newDate;
            StartTime = newStartTime;
            EndTime = newEndTime;
            Duration = CalculateDuration();
            Status = "مؤجل";
            StatusFr = "Postponed";
        }

        /// <summary>
        /// الحصول على المدة الفعلية للموعد
        /// Get actual duration of appointment
        /// </summary>
        public TimeSpan? GetActualDuration()
        {
            if (ActualStartTime.HasValue && ActualEndTime.HasValue)
            {
                return ActualEndTime.Value - ActualStartTime.Value;
            }
            return null;
        }
    }
}
