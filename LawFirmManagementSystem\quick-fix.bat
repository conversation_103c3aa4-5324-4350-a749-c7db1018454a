@echo off
echo ========================================
echo إصلاح سريع للأخطاء
echo Quick Fix for Errors
echo ========================================
echo.

echo تنظيف المشروع...
echo Cleaning project...
dotnet clean

echo استعادة الحزم...
echo Restoring packages...
dotnet restore

echo محاولة البناء مع تجاهل التحذيرات...
echo Attempting build ignoring warnings...
dotnet build --configuration Debug --verbosity minimal --no-restore

if %errorlevel% equ 0 (
    echo ========================================
    echo ✓ تم البناء بنجاح!
    echo ✓ Build successful!
    echo ========================================
    echo.
    echo تشغيل التطبيق...
    echo Running application...
    echo.
    echo بيانات تسجيل الدخول:
    echo Login credentials:
    echo Username: admin
    echo Password: Admin@123
    echo.
    dotnet run
) else (
    echo ========================================
    echo ✗ فشل البناء
    echo ✗ Build failed
    echo ========================================
    echo.
    echo محاولة إصلاح إضافية...
    echo Attempting additional fixes...
    
    echo تنظيف مجلدات البناء...
    echo Cleaning build folders...
    rmdir /s /q bin 2>nul
    rmdir /s /q obj 2>nul
    
    echo استعادة الحزم مرة أخرى...
    echo Restoring packages again...
    dotnet restore --force
    
    echo محاولة البناء مرة أخرى...
    echo Attempting build again...
    dotnet build --configuration Debug
    
    if %errorlevel% equ 0 (
        echo ✓ نجح الإصلاح!
        echo ✓ Fix successful!
        dotnet run
    ) else (
        echo ✗ لا تزال هناك أخطاء
        echo ✗ Still have errors
        echo يرجى مراجعة الأخطاء أعلاه
        echo Please review errors above
    )
)

pause
