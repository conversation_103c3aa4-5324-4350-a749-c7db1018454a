@echo off
echo ========================================
echo نظام إدارة مكتب المحاماة
echo Law Firm Management System
echo ========================================
echo.

echo المسار الحالي: %CD%
echo Current Path: %CD%
echo.

echo بيانات تسجيل الدخول:
echo Login Credentials:
echo Username: admin
echo Password: Admin@123
echo.

echo فحص المسار...
echo Checking path...

if exist "LawFirmManagementSystem.csproj" (
    echo ✓ ملف المشروع موجود
    echo ✓ Project file found
) else (
    echo ✗ ملف المشروع غير موجود
    echo ✗ Project file not found
    echo.
    echo تأكد من أنك في المجلد الصحيح:
    echo Make sure you are in the correct folder:
    echo C:\Users\<USER>\Desktop\law\LawFirmManagementSystem
    echo.
    pause
    exit /b 1
)

echo.
echo تنظيف المشروع...
echo Cleaning project...
dotnet clean

echo.
echo استعادة الحزم...
echo Restoring packages...
dotnet restore

echo.
echo محاولة البناء...
echo Attempting build...
dotnet build --configuration Debug

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✓ تم البناء بنجاح!
    echo ✓ Build successful!
    echo ========================================
    echo.
    echo تشغيل التطبيق...
    echo Running application...
    echo.
    dotnet run
) else (
    echo.
    echo ========================================
    echo ⚠ فشل البناء - محاولة النسخة المبسطة
    echo ⚠ Build failed - trying simple version
    echo ========================================
    echo.
    
    if exist "SimpleProgram.cs" (
        echo تجميع النسخة المبسطة...
        echo Compiling simple version...
        
        csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleProgram.cs
        
        if %errorlevel% equ 0 (
            echo ✓ تم تجميع النسخة المبسطة بنجاح!
            echo ✓ Simple version compiled successfully!
            echo.
            echo تشغيل النسخة المبسطة...
            echo Running simple version...
            SimpleProgram.exe
        ) else (
            echo ✗ فشل تجميع النسخة المبسطة أيضاً
            echo ✗ Simple version compilation also failed
        )
    ) else (
        echo ✗ ملف النسخة المبسطة غير موجود
        echo ✗ Simple version file not found
    )
)

echo.
echo ========================================
echo انتهى التشغيل
echo Execution completed
echo ========================================
pause
