# نظام إدارة مكتب المحاماة / Law Firm Management System

## نظرة عامة / Overview

نظام شامل لإدارة مكاتب المحاماة في المغرب، يوفر جميع الأدوات اللازمة لإدارة العملاء والقضايا والمواعيد والوثائق والأمور المالية بكفاءة عالية.

A comprehensive law firm management system for Morocco, providing all necessary tools to efficiently manage clients, cases, appointments, documents, and financial matters.

## المميزات الرئيسية / Key Features

### إدارة العملاء / Client Management
- ✅ إضافة وتعديل بيانات العملاء
- ✅ تتبع تاريخ التواصل مع العملاء
- ✅ إدارة الوثائق الخاصة بكل عميل
- ✅ تقارير شاملة عن العملاء

### إدارة القضايا / Case Management
- ✅ إنشاء وإدارة القضايا
- ✅ تتبع مراحل القضية
- ✅ إدارة الخصوم والأطراف
- ✅ ربط القضايا بالعملاء والمحاكم

### التقويم والمواعيد / Calendar & Appointments
- ✅ جدولة المواعيد والجلسات
- ✅ تذكيرات تلقائية
- ✅ عرض التقويم الشهري والأسبوعي
- ✅ إدارة الجلسات القضائية

### الإدارة المالية / Financial Management
- ✅ إنشاء الفواتير
- ✅ تتبع الدفعات
- ✅ إدارة المصروفات
- ✅ التقارير المالية

### إدارة الوثائق / Document Management
- ✅ رفع وتنظيم الوثائق
- ✅ البحث في الوثائق
- ✅ إدارة الإصدارات
- ✅ الأمان والتشفير

### التقارير / Reports
- ✅ تقارير العملاء والقضايا
- ✅ التقارير المالية
- ✅ تقارير الأداء
- ✅ تصدير بصيغ مختلفة (PDF, Excel, Word)

## المتطلبات التقنية / Technical Requirements

### متطلبات النظام / System Requirements
- Windows 10/11 أو أحدث
- .NET 8.0 Runtime
- SQL Server 2019 أو أحدث (أو SQL Server Express)
- 4 GB RAM (8 GB مُوصى به)
- 2 GB مساحة تخزين متاحة

### التقنيات المستخدمة / Technologies Used
- **Framework**: .NET 8.0 WinForms
- **Database**: SQL Server / SQL Server Express
- **ORM**: Entity Framework Core 8.0
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Logging**: Serilog
- **PDF Generation**: iTextSharp
- **Excel Export**: EPPlus
- **Email**: MailKit
- **SMS/WhatsApp**: Twilio
- **Encryption**: System.Security.Cryptography

## التثبيت والإعداد / Installation & Setup

### 1. متطلبات ما قبل التثبيت / Prerequisites
```bash
# تثبيت .NET 8.0 SDK
# Install .NET 8.0 SDK
https://dotnet.microsoft.com/download/dotnet/8.0

# تثبيت SQL Server Express (اختياري)
# Install SQL Server Express (optional)
https://www.microsoft.com/sql-server/sql-server-downloads
```

### 2. استنساخ المشروع / Clone the Project
```bash
git clone https://github.com/your-repo/LawFirmManagementSystem.git
cd LawFirmManagementSystem
```

### 3. إعداد قاعدة البيانات / Database Setup
```bash
# تحديث سلسلة الاتصال في appsettings.json
# Update connection string in appsettings.json

# تشغيل الترحيلات
# Run migrations
dotnet ef database update
```

### 4. تشغيل التطبيق / Run the Application

#### التشغيل السريع / Quick Start
```bash
# للتشغيل السريع مع الاختبار
quick-test.bat

# أو التشغيل العادي
run.bat

# أو التشغيل المباشر
dotnet run
```

#### بيانات تسجيل الدخول الافتراضية / Default Login Credentials
```
اسم المستخدم / Username: admin
كلمة المرور / Password: Admin@123
```

#### أدوات مساعدة أخرى / Other Helper Tools
```bash
# فحص صحة النظام
system-check.bat

# إعداد بيئة التطوير
dev-setup.bat

# تنظيف المشروع
clean.bat

# النسخ الاحتياطي
backup.bat
```

## الإعدادات / Configuration

### إعدادات قاعدة البيانات / Database Settings
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=LawFirmManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

### إعدادات البريد الإلكتروني / Email Settings
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "EnableSsl": true,
    "Username": "<EMAIL>",
    "Password": "your-app-password"
  }
}
```

### إعدادات الأمان / Security Settings
```json
{
  "SecuritySettings": {
    "EncryptionKey": "your-encryption-key",
    "EnableAuditLog": true,
    "EnableDataEncryption": true
  }
}
```

## الاستخدام / Usage

### تسجيل الدخول الأولي / Initial Login
- **اسم المستخدم / Username**: `admin`
- **كلمة المرور / Password**: `Admin@123`

### إضافة عميل جديد / Adding a New Client
1. انقر على "العملاء" > "عميل جديد"
2. املأ البيانات المطلوبة
3. احفظ البيانات

### إنشاء قضية جديدة / Creating a New Case
1. انقر على "القضايا" > "قضية جديدة"
2. اختر العميل
3. املأ تفاصيل القضية
4. احفظ القضية

## الدعم الفني / Technical Support

### المشاكل الشائعة / Common Issues

#### مشكلة الاتصال بقاعدة البيانات / Database Connection Issues
```bash
# تحقق من سلسلة الاتصال
# Check connection string
# تأكد من تشغيل SQL Server
# Ensure SQL Server is running
```

#### مشكلة الأذونات / Permission Issues
```bash
# تشغيل التطبيق كمدير
# Run application as administrator
# تحقق من أذونات المجلدات
# Check folder permissions
```

### الحصول على المساعدة / Getting Help
- 📧 البريد الإلكتروني / Email: <EMAIL>
- 📞 الهاتف / Phone: +212 5XX-XXXXXX
- 🌐 الموقع الإلكتروني / Website: www.lawfirmsolutions.ma

## المساهمة في التطوير / Contributing

### إرشادات المساهمة / Contribution Guidelines
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

### معايير الكود / Code Standards
- استخدام C# Coding Conventions
- إضافة تعليقات باللغتين العربية والفرنسية
- كتابة Unit Tests للميزات الجديدة
- اتباع مبادئ SOLID

## الترخيص / License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## الإصدارات / Versions

### الإصدار 1.0.0 / Version 1.0.0
- ✅ إدارة العملاء الأساسية
- ✅ إدارة القضايا
- ✅ التقويم والمواعيد
- ✅ الإدارة المالية الأساسية
- ✅ إدارة الوثائق
- ✅ التقارير الأساسية
- ✅ الدعم ثنائي اللغة (العربية/الفرنسية)

### الإصدارات القادمة / Upcoming Versions
- 🔄 تكامل مع أنظمة المحاكم الإلكترونية
- 🔄 تطبيق الهاتف المحمول
- 🔄 واجهة برمجة التطبيقات (API)
- 🔄 تحليلات متقدمة وذكاء اصطناعي

## شكر وتقدير / Acknowledgments

- شكر خاص لجميع المحامين الذين ساهموا في تحديد المتطلبات
- فريق التطوير والاختبار
- المجتمع المفتوح المصدر

---

**© 2025 Law Firm Management Solutions. جميع الحقوق محفوظة.**
