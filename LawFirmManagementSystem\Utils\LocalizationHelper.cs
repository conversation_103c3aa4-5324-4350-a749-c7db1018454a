using LawFirmManagementSystem.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Utils
{
    /// <summary>
    /// مساعد التوطين للنماذج
    /// Localization helper for forms
    /// </summary>
    public static class LocalizationHelper
    {
        private static ILocalizationService? _localizationService;

        /// <summary>
        /// تهيئة مساعد التوطين
        /// Initialize localization helper
        /// </summary>
        public static void Initialize(ILocalizationService localizationService)
        {
            _localizationService = localizationService ?? throw new ArgumentNullException(nameof(localizationService));
        }

        /// <summary>
        /// الحصول على النص المترجم
        /// Get localized text
        /// </summary>
        public static string GetText(string key, string? language = null)
        {
            return _localizationService?.GetLocalizedString(key, language) ?? key;
        }

        /// <summary>
        /// الحصول على النص المترجم مع معاملات
        /// Get localized text with parameters
        /// </summary>
        public static string GetText(string key, object[] args, string? language = null)
        {
            return _localizationService?.GetLocalizedString(key, args, language) ?? key;
        }

        /// <summary>
        /// تطبيق التوطين على النموذج
        /// Apply localization to form
        /// </summary>
        public static void LocalizeForm(Form form, string? language = null)
        {
            if (_localizationService == null) return;

            var currentLanguage = language ?? _localizationService.GetCurrentLanguage();
            var isRtl = _localizationService.IsRightToLeft(currentLanguage);

            // Set form properties
            form.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
            form.RightToLeftLayout = isRtl;

            // Apply to all controls
            LocalizeControls(form.Controls, currentLanguage);
        }

        /// <summary>
        /// تطبيق التوطين على مجموعة من العناصر
        /// Apply localization to control collection
        /// </summary>
        public static void LocalizeControls(Control.ControlCollection controls, string? language = null)
        {
            if (_localizationService == null) return;

            var currentLanguage = language ?? _localizationService.GetCurrentLanguage();
            var isRtl = _localizationService.IsRightToLeft(currentLanguage);

            foreach (Control control in controls)
            {
                LocalizeControl(control, currentLanguage, isRtl);

                // Recursively localize child controls
                if (control.HasChildren)
                {
                    LocalizeControls(control.Controls, currentLanguage);
                }
            }
        }

        /// <summary>
        /// تطبيق التوطين على عنصر واحد
        /// Apply localization to single control
        /// </summary>
        public static void LocalizeControl(Control control, string? language = null, bool? isRtl = null)
        {
            if (_localizationService == null) return;

            var currentLanguage = language ?? _localizationService.GetCurrentLanguage();
            var rightToLeft = isRtl ?? _localizationService.IsRightToLeft(currentLanguage);

            // Set RTL properties
            control.RightToLeft = rightToLeft ? RightToLeft.Yes : RightToLeft.No;

            // Apply font for Arabic text
            if (currentLanguage == "ar")
            {
                ApplyArabicFont(control);
            }

            // Localize specific control types
            switch (control)
            {
                case Button button:
                    LocalizeButton(button, currentLanguage);
                    break;
                case Label label:
                    LocalizeLabel(label, currentLanguage);
                    break;
                case TextBox textBox:
                    LocalizeTextBox(textBox, currentLanguage, rightToLeft);
                    break;
                case ComboBox comboBox:
                    LocalizeComboBox(comboBox, currentLanguage, rightToLeft);
                    break;
                case CheckBox checkBox:
                    LocalizeCheckBox(checkBox, currentLanguage);
                    break;
                case RadioButton radioButton:
                    LocalizeRadioButton(radioButton, currentLanguage);
                    break;
                case GroupBox groupBox:
                    LocalizeGroupBox(groupBox, currentLanguage);
                    break;
                case TabPage tabPage:
                    LocalizeTabPage(tabPage, currentLanguage);
                    break;
                case DataGridView dataGridView:
                    LocalizeDataGridView(dataGridView, currentLanguage, rightToLeft);
                    break;
                case MenuStrip menuStrip:
                    LocalizeMenuStrip(menuStrip, currentLanguage);
                    break;
                case ToolStrip toolStrip:
                    LocalizeToolStrip(toolStrip, currentLanguage);
                    break;
            }
        }

        /// <summary>
        /// تطبيق خط عربي مناسب
        /// Apply appropriate Arabic font
        /// </summary>
        private static void ApplyArabicFont(Control control)
        {
            try
            {
                // Use Tahoma or Segoe UI for Arabic text
                var arabicFont = new Font("Tahoma", control.Font.Size, control.Font.Style);
                control.Font = arabicFont;
            }
            catch
            {
                // Fallback to default font if Arabic font is not available
            }
        }

        /// <summary>
        /// توطين الأزرار
        /// Localize buttons
        /// </summary>
        private static void LocalizeButton(Button button, string language)
        {
            if (button.Tag is string key)
            {
                button.Text = GetText(key, language);
            }
        }

        /// <summary>
        /// توطين التسميات
        /// Localize labels
        /// </summary>
        private static void LocalizeLabel(Label label, string language)
        {
            if (label.Tag is string key)
            {
                label.Text = GetText(key, language);
            }
        }

        /// <summary>
        /// توطين صناديق النص
        /// Localize text boxes
        /// </summary>
        private static void LocalizeTextBox(TextBox textBox, string language, bool isRtl)
        {
            if (textBox.Tag is string key)
            {
                textBox.PlaceholderText = GetText(key, language);
            }

            // Set text alignment for RTL
            textBox.TextAlign = isRtl ? HorizontalAlignment.Right : HorizontalAlignment.Left;
        }

        /// <summary>
        /// توطين القوائم المنسدلة
        /// Localize combo boxes
        /// </summary>
        private static void LocalizeComboBox(ComboBox comboBox, string language, bool isRtl)
        {
            // Set dropdown alignment for RTL
            if (isRtl)
            {
                comboBox.RightToLeft = RightToLeft.Yes;
            }
        }

        /// <summary>
        /// توطين مربعات الاختيار
        /// Localize check boxes
        /// </summary>
        private static void LocalizeCheckBox(CheckBox checkBox, string language)
        {
            if (checkBox.Tag is string key)
            {
                checkBox.Text = GetText(key, language);
            }
        }

        /// <summary>
        /// توطين أزرار الراديو
        /// Localize radio buttons
        /// </summary>
        private static void LocalizeRadioButton(RadioButton radioButton, string language)
        {
            if (radioButton.Tag is string key)
            {
                radioButton.Text = GetText(key, language);
            }
        }

        /// <summary>
        /// توطين مجموعات العناصر
        /// Localize group boxes
        /// </summary>
        private static void LocalizeGroupBox(GroupBox groupBox, string language)
        {
            if (groupBox.Tag is string key)
            {
                groupBox.Text = GetText(key, language);
            }
        }

        /// <summary>
        /// توطين صفحات التبويب
        /// Localize tab pages
        /// </summary>
        private static void LocalizeTabPage(TabPage tabPage, string language)
        {
            if (tabPage.Tag is string key)
            {
                tabPage.Text = GetText(key, language);
            }
        }

        /// <summary>
        /// توطين جداول البيانات
        /// Localize data grid views
        /// </summary>
        private static void LocalizeDataGridView(DataGridView dataGridView, string language, bool isRtl)
        {
            // Set RTL properties
            dataGridView.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;

            // Localize column headers
            foreach (DataGridViewColumn column in dataGridView.Columns)
            {
                if (column.Tag is string key)
                {
                    column.HeaderText = GetText(key, language);
                }
            }
        }

        /// <summary>
        /// توطين شريط القوائم
        /// Localize menu strip
        /// </summary>
        private static void LocalizeMenuStrip(MenuStrip menuStrip, string language)
        {
            foreach (ToolStripItem item in menuStrip.Items)
            {
                LocalizeToolStripItem(item, language);
            }
        }

        /// <summary>
        /// توطين شريط الأدوات
        /// Localize tool strip
        /// </summary>
        private static void LocalizeToolStrip(ToolStrip toolStrip, string language)
        {
            foreach (ToolStripItem item in toolStrip.Items)
            {
                LocalizeToolStripItem(item, language);
            }
        }

        /// <summary>
        /// توطين عنصر شريط الأدوات
        /// Localize tool strip item
        /// </summary>
        private static void LocalizeToolStripItem(ToolStripItem item, string language)
        {
            if (item.Tag is string key)
            {
                item.Text = GetText(key, language);
            }

            // Recursively localize dropdown items
            if (item is ToolStripDropDownItem dropDownItem)
            {
                foreach (ToolStripItem subItem in dropDownItem.DropDownItems)
                {
                    LocalizeToolStripItem(subItem, language);
                }
            }
        }

        /// <summary>
        /// تنسيق التاريخ حسب اللغة
        /// Format date by language
        /// </summary>
        public static string FormatDate(DateTime date, string? language = null)
        {
            return _localizationService?.FormatDate(date, language) ?? date.ToString("d");
        }

        /// <summary>
        /// تنسيق الوقت حسب اللغة
        /// Format time by language
        /// </summary>
        public static string FormatTime(DateTime time, string? language = null)
        {
            return _localizationService?.FormatTime(time, language) ?? time.ToString("t");
        }

        /// <summary>
        /// تنسيق التاريخ والوقت حسب اللغة
        /// Format datetime by language
        /// </summary>
        public static string FormatDateTime(DateTime dateTime, string? language = null)
        {
            return _localizationService?.FormatDateTime(dateTime, language) ?? dateTime.ToString("g");
        }

        /// <summary>
        /// تنسيق الرقم حسب اللغة
        /// Format number by language
        /// </summary>
        public static string FormatNumber(decimal number, string? language = null)
        {
            return _localizationService?.FormatNumber(number, language) ?? number.ToString("N");
        }

        /// <summary>
        /// تنسيق العملة حسب اللغة
        /// Format currency by language
        /// </summary>
        public static string FormatCurrency(decimal amount, string? language = null)
        {
            return _localizationService?.FormatCurrency(amount, language) ?? amount.ToString("C");
        }

        /// <summary>
        /// الحصول على اتجاه النص
        /// Get text direction
        /// </summary>
        public static bool IsRightToLeft(string? language = null)
        {
            return _localizationService?.IsRightToLeft(language) ?? false;
        }

        /// <summary>
        /// تبديل اللغة وتحديث النموذج
        /// Switch language and update form
        /// </summary>
        public static void SwitchLanguage(Form form, string language)
        {
            if (_localizationService == null) return;

            _localizationService.SetCurrentLanguage(language);
            LocalizeForm(form, language);
        }
    }
}
