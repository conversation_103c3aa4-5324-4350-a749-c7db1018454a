@echo off
echo ========================================
echo نظام النسخ الاحتياطي
echo Backup System
echo ========================================
echo.

echo اختر نوع النسخ الاحتياطي:
echo Choose backup type:
echo 1. نسخة احتياطية سريعة (Quick Backup)
echo 2. نسخة احتياطية شاملة (Full Backup)
echo 3. نسخة احتياطية لقاعدة البيانات فقط (Database Only)
echo 4. نسخة احتياطية للوثائق فقط (Documents Only)
echo 5. نسخة احتياطية مجدولة (Scheduled Backup)
echo 6. استعادة النسخة الاحتياطية (Restore Backup)
echo 7. عرض النسخ الاحتياطية (List Backups)
echo.

set /p CHOICE="اختر رقم (1-7) / Choose number (1-7): "

if "%CHOICE%"=="1" goto quick_backup
if "%CHOICE%"=="2" goto full_backup
if "%CHOICE%"=="3" goto database_backup
if "%CHOICE%"=="4" goto documents_backup
if "%CHOICE%"=="5" goto scheduled_backup
if "%CHOICE%"=="6" goto restore_backup
if "%CHOICE%"=="7" goto list_backups
goto invalid_choice

:quick_backup
echo إنشاء نسخة احتياطية سريعة...
echo Creating quick backup...
echo.

set BACKUP_NAME=QuickBackup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=%BACKUP_NAME: =0%
set BACKUP_DIR=Backups\%BACKUP_NAME%

echo إنشاء مجلد النسخة الاحتياطية...
echo Creating backup directory...
if not exist "Backups" mkdir "Backups"
mkdir "%BACKUP_DIR%"

echo نسخ ملفات الإعدادات...
echo Copying configuration files...
copy "appsettings.json" "%BACKUP_DIR%\" >nul
if exist "appsettings.Production.json" copy "appsettings.Production.json" "%BACKUP_DIR%\" >nul

echo نسخ قاعدة البيانات...
echo Copying database...
call :backup_database "%BACKUP_DIR%"

echo إنشاء ملف معلومات النسخة الاحتياطية...
echo Creating backup info file...
echo Backup Type: Quick Backup > "%BACKUP_DIR%\backup_info.txt"
echo Created: %date% %time% >> "%BACKUP_DIR%\backup_info.txt"
echo Computer: %COMPUTERNAME% >> "%BACKUP_DIR%\backup_info.txt"
echo User: %USERNAME% >> "%BACKUP_DIR%\backup_info.txt"

goto backup_complete

:full_backup
echo إنشاء نسخة احتياطية شاملة...
echo Creating full backup...
echo.

set BACKUP_NAME=FullBackup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=%BACKUP_NAME: =0%
set BACKUP_DIR=Backups\%BACKUP_NAME%

echo إنشاء مجلد النسخة الاحتياطية...
echo Creating backup directory...
if not exist "Backups" mkdir "Backups"
mkdir "%BACKUP_DIR%"

echo نسخ ملفات الإعدادات...
echo Copying configuration files...
copy "appsettings.json" "%BACKUP_DIR%\" >nul
if exist "appsettings.Production.json" copy "appsettings.Production.json" "%BACKUP_DIR%\" >nul
if exist "appsettings.Development.json" copy "appsettings.Development.json" "%BACKUP_DIR%\" >nul

echo نسخ قاعدة البيانات...
echo Copying database...
call :backup_database "%BACKUP_DIR%"

echo نسخ الوثائق...
echo Copying documents...
if exist "Documents" (
    mkdir "%BACKUP_DIR%\Documents"
    xcopy "Documents\*.*" "%BACKUP_DIR%\Documents\" /s /e /h /y >nul
)

echo نسخ القوالب...
echo Copying templates...
if exist "Templates" (
    mkdir "%BACKUP_DIR%\Templates"
    xcopy "Templates\*.*" "%BACKUP_DIR%\Templates\" /s /e /h /y >nul
)

echo نسخ السجلات (آخر 30 يوم)...
echo Copying logs (last 30 days)...
if exist "Logs" (
    mkdir "%BACKUP_DIR%\Logs"
    forfiles /p "Logs" /m *.log /d -30 /c "cmd /c copy @path \"%BACKUP_DIR%\Logs\\" 2>nul
)

echo إنشاء ملف معلومات النسخة الاحتياطية...
echo Creating backup info file...
echo Backup Type: Full Backup > "%BACKUP_DIR%\backup_info.txt"
echo Created: %date% %time% >> "%BACKUP_DIR%\backup_info.txt"
echo Computer: %COMPUTERNAME% >> "%BACKUP_DIR%\backup_info.txt"
echo User: %USERNAME% >> "%BACKUP_DIR%\backup_info.txt"
echo Includes: Configuration, Database, Documents, Templates, Logs >> "%BACKUP_DIR%\backup_info.txt"

goto backup_complete

:database_backup
echo إنشاء نسخة احتياطية لقاعدة البيانات...
echo Creating database backup...
echo.

set BACKUP_NAME=DatabaseBackup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=%BACKUP_NAME: =0%
set BACKUP_DIR=Backups\%BACKUP_NAME%

if not exist "Backups" mkdir "Backups"
mkdir "%BACKUP_DIR%"

call :backup_database "%BACKUP_DIR%"

echo إنشاء ملف معلومات النسخة الاحتياطية...
echo Creating backup info file...
echo Backup Type: Database Only > "%BACKUP_DIR%\backup_info.txt"
echo Created: %date% %time% >> "%BACKUP_DIR%\backup_info.txt"

goto backup_complete

:documents_backup
echo إنشاء نسخة احتياطية للوثائق...
echo Creating documents backup...
echo.

set BACKUP_NAME=DocumentsBackup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=%BACKUP_NAME: =0%
set BACKUP_DIR=Backups\%BACKUP_NAME%

if not exist "Backups" mkdir "Backups"
mkdir "%BACKUP_DIR%"

if exist "Documents" (
    echo نسخ الوثائق...
    echo Copying documents...
    mkdir "%BACKUP_DIR%\Documents"
    xcopy "Documents\*.*" "%BACKUP_DIR%\Documents\" /s /e /h /y >nul
) else (
    echo لا توجد وثائق للنسخ
    echo No documents to backup
)

echo إنشاء ملف معلومات النسخة الاحتياطية...
echo Creating backup info file...
echo Backup Type: Documents Only > "%BACKUP_DIR%\backup_info.txt"
echo Created: %date% %time% >> "%BACKUP_DIR%\backup_info.txt"

goto backup_complete

:scheduled_backup
echo إعداد النسخ الاحتياطي المجدول...
echo Setting up scheduled backup...
echo.

echo إنشاء مهمة مجدولة للنسخ الاحتياطي...
echo Creating scheduled task for backup...

echo اختر تكرار النسخ الاحتياطي:
echo Choose backup frequency:
echo 1. يومياً (Daily)
echo 2. أسبوعياً (Weekly)
echo 3. شهرياً (Monthly)

set /p FREQ_CHOICE="اختر رقم (1-3) / Choose number (1-3): "

if "%FREQ_CHOICE%"=="1" set SCHEDULE=DAILY
if "%FREQ_CHOICE%"=="2" set SCHEDULE=WEEKLY
if "%FREQ_CHOICE%"=="3" set SCHEDULE=MONTHLY

set /p BACKUP_TIME="أدخل وقت النسخ الاحتياطي (HH:MM) / Enter backup time (HH:MM): "

echo إنشاء المهمة المجدولة...
echo Creating scheduled task...

schtasks /create /tn "LawFirm_AutoBackup" /tr "%~dp0backup.bat 2" /sc %SCHEDULE% /st %BACKUP_TIME% /f

if %errorlevel% equ 0 (
    echo ✓ تم إنشاء المهمة المجدولة بنجاح
    echo ✓ Scheduled task created successfully
) else (
    echo ✗ فشل في إنشاء المهمة المجدولة
    echo ✗ Failed to create scheduled task
)

goto end

:restore_backup
echo استعادة النسخة الاحتياطية...
echo Restoring backup...
echo.

if not exist "Backups" (
    echo لا توجد نسخ احتياطية
    echo No backups found
    goto end
)

echo النسخ الاحتياطية المتاحة:
echo Available backups:
dir "Backups" /b /ad

echo.
set /p BACKUP_TO_RESTORE="أدخل اسم النسخة الاحتياطية للاستعادة / Enter backup name to restore: "

if not exist "Backups\%BACKUP_TO_RESTORE%" (
    echo النسخة الاحتياطية غير موجودة
    echo Backup not found
    goto end
)

echo تحذير: سيتم استبدال البيانات الحالية
echo Warning: Current data will be replaced
set /p CONFIRM="هل أنت متأكد؟ (y/n) / Are you sure? (y/n): "
if /i not "%CONFIRM%"=="y" goto end

echo استعادة الملفات...
echo Restoring files...

if exist "Backups\%BACKUP_TO_RESTORE%\appsettings.json" (
    copy "Backups\%BACKUP_TO_RESTORE%\appsettings.json" "." >nul
)

if exist "Backups\%BACKUP_TO_RESTORE%\Documents" (
    if exist "Documents" rmdir /s /q "Documents"
    xcopy "Backups\%BACKUP_TO_RESTORE%\Documents" "Documents\" /s /e /h /y >nul
)

call :restore_database "Backups\%BACKUP_TO_RESTORE%"

echo ✓ تم استعادة النسخة الاحتياطية
echo ✓ Backup restored successfully

goto end

:list_backups
echo النسخ الاحتياطية المتاحة:
echo Available backups:
echo.

if not exist "Backups" (
    echo لا توجد نسخ احتياطية
    echo No backups found
    goto end
)

for /d %%i in (Backups\*) do (
    echo ========================================
    echo النسخة الاحتياطية: %%~ni
    echo Backup: %%~ni
    if exist "%%i\backup_info.txt" (
        type "%%i\backup_info.txt"
    )
    echo الحجم: 
    echo Size:
    dir "%%i" /s /-c | find "bytes"
    echo.
)

goto end

:backup_complete
echo ========================================
echo تم إنشاء النسخة الاحتياطية بنجاح!
echo Backup created successfully!
echo ========================================
echo.

echo اسم النسخة الاحتياطية: %BACKUP_NAME%
echo Backup name: %BACKUP_NAME%
echo المسار: %BACKUP_DIR%
echo Path: %BACKUP_DIR%
echo.

echo حجم النسخة الاحتياطية:
echo Backup size:
dir "%BACKUP_DIR%" /s /-c | find "bytes"
echo.

set /p COMPRESS="هل تريد ضغط النسخة الاحتياطية؟ (y/n) / Compress backup? (y/n): "
if /i "%COMPRESS%"=="y" (
    echo ضغط النسخة الاحتياطية...
    echo Compressing backup...
    powershell -command "Compress-Archive -Path '%BACKUP_DIR%\*' -DestinationPath '%BACKUP_DIR%.zip' -Force"
    if exist "%BACKUP_DIR%.zip" (
        echo ✓ تم ضغط النسخة الاحتياطية: %BACKUP_DIR%.zip
        echo ✓ Backup compressed: %BACKUP_DIR%.zip
        
        set /p DELETE_ORIGINAL="هل تريد حذف المجلد الأصلي؟ (y/n) / Delete original folder? (y/n): "
        if /i "!DELETE_ORIGINAL!"=="y" (
            rmdir /s /q "%BACKUP_DIR%"
            echo ✓ تم حذف المجلد الأصلي
            echo ✓ Original folder deleted
        )
    )
)

goto end

:backup_database
set DB_BACKUP_DIR=%1
echo نسخ قاعدة البيانات إلى %DB_BACKUP_DIR%...
echo Backing up database to %DB_BACKUP_DIR%...

:: Try LocalDB first
sqlcmd -S "(localdb)\MSSQLLocalDB" -Q "BACKUP DATABASE [LawFirmManagementDB] TO DISK = '%DB_BACKUP_DIR%\LawFirmManagementDB.bak'" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ تم نسخ قاعدة البيانات من LocalDB
    echo ✓ Database backed up from LocalDB
    goto :eof
)

:: Try SQL Server Express
sqlcmd -S ".\SQLEXPRESS" -Q "BACKUP DATABASE [LawFirmManagementDB] TO DISK = '%DB_BACKUP_DIR%\LawFirmManagementDB.bak'" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ تم نسخ قاعدة البيانات من SQL Server Express
    echo ✓ Database backed up from SQL Server Express
    goto :eof
)

echo ⚠ لم يتم العثور على قاعدة البيانات
echo ⚠ Database not found
goto :eof

:restore_database
set DB_RESTORE_DIR=%1
echo استعادة قاعدة البيانات من %DB_RESTORE_DIR%...
echo Restoring database from %DB_RESTORE_DIR%...

if exist "%DB_RESTORE_DIR%\LawFirmManagementDB.bak" (
    sqlcmd -S "(localdb)\MSSQLLocalDB" -Q "RESTORE DATABASE [LawFirmManagementDB] FROM DISK = '%DB_RESTORE_DIR%\LawFirmManagementDB.bak' WITH REPLACE" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ تم استعادة قاعدة البيانات
        echo ✓ Database restored successfully
    ) else (
        echo ⚠ فشل في استعادة قاعدة البيانات
        echo ⚠ Failed to restore database
    )
) else (
    echo ⚠ ملف قاعدة البيانات غير موجود في النسخة الاحتياطية
    echo ⚠ Database file not found in backup
)
goto :eof

:invalid_choice
echo اختيار غير صحيح
echo Invalid choice

:end
echo.
echo للحصول على المساعدة:
echo For help:
echo <EMAIL>
echo.
pause
