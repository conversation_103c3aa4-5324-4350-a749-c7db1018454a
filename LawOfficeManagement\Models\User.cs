using System;
using System.ComponentModel.DataAnnotations;

namespace LawOfficeManagement.Models
{
    public class User
    {
        public int UserId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;
        
        [Required]
        [StringLength(255)]
        public string Salt { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [Required]
        public UserRole Role { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime? LastLogin { get; set; }
        
        public int FailedLoginAttempts { get; set; } = 0;
        
        public bool IsLocked { get; set; } = false;
        
        public DateTime? LockoutEnd { get; set; }
        
        public bool TwoFactorEnabled { get; set; } = false;
        
        [StringLength(255)]
        public string? TwoFactorSecret { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
    }
    
    public enum UserRole
    {
        Admin,
        Lawyer,
        Secretary,
        Assistant
    }
}
