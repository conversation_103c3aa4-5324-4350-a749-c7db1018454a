using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان المستخدم
    /// User entity
    /// </summary>
    [Table("Users")]
    public class User : BaseEntity
    {
        [Required]
        [StringLength(50)]
        [Column("Username")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Column("PasswordHash")]
        public string PasswordHash { get; set; } = string.Empty;

        [StringLength(255)]
        [Column("PasswordSalt")]
        public string? PasswordSalt { get; set; }

        [Required]
        [StringLength(100)]
        [Column("FullName")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("FullNameFr")]
        public string? FullNameFr { get; set; }

        [StringLength(100)]
        [EmailAddress]
        [Column("Email")]
        public string? Email { get; set; }

        [StringLength(20)]
        [Column("Phone")]
        public string? Phone { get; set; }

        [StringLength(20)]
        [Column("Mobile")]
        public string? Mobile { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Role")]
        public string Role { get; set; } = "User";

        [Column("IsLocked")]
        public bool IsLocked { get; set; } = false;

        [Column("LockoutEnd")]
        public DateTime? LockoutEnd { get; set; }

        [Column("FailedLoginAttempts")]
        public int FailedLoginAttempts { get; set; } = 0;

        [Column("LastLogin")]
        public DateTime? LastLogin { get; set; }

        [Column("LastPasswordChange")]
        public DateTime? LastPasswordChange { get; set; }

        [Column("MustChangePassword")]
        public bool MustChangePassword { get; set; } = false;

        [Column("TwoFactorEnabled")]
        public bool TwoFactorEnabled { get; set; } = false;

        [StringLength(255)]
        [Column("TwoFactorSecret")]
        public string? TwoFactorSecret { get; set; }

        [StringLength(10)]
        [Column("PreferredLanguage")]
        public string PreferredLanguage { get; set; } = "ar";

        [StringLength(50)]
        [Column("Theme")]
        public string Theme { get; set; } = "Professional";

        [StringLength(500)]
        [Column("ProfilePicturePath")]
        public string? ProfilePicturePath { get; set; }

        [StringLength(100)]
        [Column("Department")]
        public string? Department { get; set; }

        [StringLength(100)]
        [Column("DepartmentFr")]
        public string? DepartmentFr { get; set; }

        [StringLength(100)]
        [Column("Position")]
        public string? Position { get; set; }

        [StringLength(100)]
        [Column("PositionFr")]
        public string? PositionFr { get; set; }

        [Column("HireDate")]
        public DateTime? HireDate { get; set; }

        [Column("Salary")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Salary { get; set; }

        [StringLength(50)]
        [Column("EmployeeId")]
        public string? EmployeeId { get; set; }

        [StringLength(20)]
        [Column("CIN")]
        public string? CIN { get; set; }

        [Column("DateOfBirth")]
        public DateTime? DateOfBirth { get; set; }

        [StringLength(10)]
        [Column("Gender")]
        public string? Gender { get; set; }

        [StringLength(50)]
        [Column("Nationality")]
        public string? Nationality { get; set; }

        [StringLength(50)]
        [Column("NationalityFr")]
        public string? NationalityFr { get; set; }

        [StringLength(200)]
        [Column("Address")]
        public string? Address { get; set; }

        [StringLength(200)]
        [Column("AddressFr")]
        public string? AddressFr { get; set; }

        [StringLength(50)]
        [Column("City")]
        public string? City { get; set; }

        [StringLength(50)]
        [Column("CityFr")]
        public string? CityFr { get; set; }

        [StringLength(10)]
        [Column("PostalCode")]
        public string? PostalCode { get; set; }

        [StringLength(100)]
        [Column("EmergencyContact")]
        public string? EmergencyContact { get; set; }

        [StringLength(20)]
        [Column("EmergencyPhone")]
        public string? EmergencyPhone { get; set; }

        [Column("SessionTimeout")]
        public int SessionTimeout { get; set; } = 30;

        [Column("LastActivityDate")]
        public DateTime? LastActivityDate { get; set; }

        [Column("IsOnline")]
        public bool IsOnline { get; set; } = false;

        // Navigation Properties
        public virtual ICollection<UserSession> Sessions { get; set; } = new List<UserSession>();
        public virtual ICollection<UserPermission> Permissions { get; set; } = new List<UserPermission>();
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
        public virtual ICollection<LegalCase> CreatedCases { get; set; } = new List<LegalCase>();
        public virtual ICollection<LegalCase> AssignedCases { get; set; } = new List<LegalCase>();
        public virtual ICollection<Client> CreatedClients { get; set; } = new List<Client>();
        public virtual ICollection<Hearing> CreatedHearings { get; set; } = new List<Hearing>();
        public virtual ICollection<Appointment> CreatedAppointments { get; set; } = new List<Appointment>();

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// Verify password
        /// </summary>
        public bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// Hash password
        /// </summary>
        public static string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية القفل
        /// Check if lockout has expired
        /// </summary>
        public bool IsLockoutExpired()
        {
            return LockoutEnd.HasValue && LockoutEnd.Value <= DateTime.Now;
        }

        /// <summary>
        /// إعادة تعيين محاولات تسجيل الدخول الفاشلة
        /// Reset failed login attempts
        /// </summary>
        public void ResetFailedLoginAttempts()
        {
            FailedLoginAttempts = 0;
            IsLocked = false;
            LockoutEnd = null;
        }

        /// <summary>
        /// زيادة محاولات تسجيل الدخول الفاشلة
        /// Increment failed login attempts
        /// </summary>
        public void IncrementFailedLoginAttempts(int maxAttempts = 5, int lockoutMinutes = 15)
        {
            FailedLoginAttempts++;
            if (FailedLoginAttempts >= maxAttempts)
            {
                IsLocked = true;
                LockoutEnd = DateTime.Now.AddMinutes(lockoutMinutes);
            }
        }

        /// <summary>
        /// تحديث آخر نشاط
        /// Update last activity
        /// </summary>
        public void UpdateLastActivity()
        {
            LastActivityDate = DateTime.Now;
            IsOnline = true;
        }

        /// <summary>
        /// تسجيل الخروج
        /// Logout
        /// </summary>
        public void Logout()
        {
            IsOnline = false;
            LastActivityDate = DateTime.Now;
        }
    }
}
