# نظام إدارة مكتب المحاماة
# Law Firm Management System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "نظام إدارة مكتب المحاماة" -ForegroundColor Green
Write-Host "Law Firm Management System" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "بيانات تسجيل الدخول / Login Credentials:" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: Admin@123" -ForegroundColor White
Write-Host ""

Write-Host "فحص .NET SDK..." -ForegroundColor Blue
Write-Host "Checking .NET SDK..." -ForegroundColor Blue

try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET SDK غير متاح / .NET SDK not available" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة / Press Enter to continue"
    exit
}

Write-Host ""
Write-Host "استعادة الحزم..." -ForegroundColor Blue
Write-Host "Restoring packages..." -ForegroundColor Blue

try {
    dotnet restore
    Write-Host "✓ تم استعادة الحزم بنجاح / Packages restored successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠ تحذير في استعادة الحزم / Warning in package restore" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "بناء المشروع..." -ForegroundColor Blue
Write-Host "Building project..." -ForegroundColor Blue

try {
    dotnet build --configuration Debug
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم البناء بنجاح / Build successful" -ForegroundColor Green
    } else {
        Write-Host "⚠ تحذيرات في البناء / Build warnings" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ فشل البناء / Build failed" -ForegroundColor Red
    Write-Host "محاولة تنظيف المشروع..." -ForegroundColor Yellow
    dotnet clean
    dotnet restore
    dotnet build
}

Write-Host ""
Write-Host "تشغيل التطبيق..." -ForegroundColor Blue
Write-Host "Running application..." -ForegroundColor Blue
Write-Host ""

try {
    dotnet run
} catch {
    Write-Host ""
    Write-Host "خطأ في تشغيل التطبيق / Error running application" -ForegroundColor Red
    Write-Host "تحقق من الأخطاء أعلاه / Check errors above" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "اضغط Enter للإغلاق / Press Enter to close"
