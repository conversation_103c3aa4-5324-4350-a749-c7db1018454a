using System;
using System.ComponentModel.DataAnnotations;

namespace LawOfficeManagement.Models
{
    public class LegalCase
    {
        public int CaseId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string CaseReference { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string OfficeReference { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string CaseTitle { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? CaseTitleFr { get; set; }
        
        [Required]
        public int CaseTypeId { get; set; }
        
        [Required]
        public int ClientId { get; set; }
        
        public int? CourtId { get; set; }
        
        [StringLength(100)]
        public string? CourtCaseNumber { get; set; }
        
        [StringLength(50)]
        public string CaseStatus { get; set; } = "جاري";
        
        [StringLength(20)]
        public string Priority { get; set; } = "عادية";
        
        [Required]
        public DateTime OpenDate { get; set; }
        
        public DateTime? CloseDate { get; set; }
        
        public string? Description { get; set; }
        
        public string? InternalNotes { get; set; }
        
        public int? AssignedLawyer { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual CaseType? CaseType { get; set; }
        public virtual Client? Client { get; set; }
        public virtual Court? Court { get; set; }
        public virtual User? Lawyer { get; set; }
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
        public virtual ICollection<Opponent> Opponents { get; set; } = new List<Opponent>();
        public virtual ICollection<Hearing> Hearings { get; set; } = new List<Hearing>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
        public virtual ICollection<CaseExpense> Expenses { get; set; } = new List<CaseExpense>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    }
    
    public class CaseType
    {
        public int CaseTypeId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string TypeName { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? TypeNameFr { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual ICollection<LegalCase> LegalCases { get; set; } = new List<LegalCase>();
    }
    
    public class Court
    {
        public int CourtId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string CourtName { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? CourtNameFr { get; set; }
        
        [StringLength(50)]
        public string? CourtType { get; set; }
        
        [StringLength(100)]
        public string? Jurisdiction { get; set; }
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(500)]
        public string? Address { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(20)]
        public string? Fax { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual ICollection<LegalCase> LegalCases { get; set; } = new List<LegalCase>();
    }
}
