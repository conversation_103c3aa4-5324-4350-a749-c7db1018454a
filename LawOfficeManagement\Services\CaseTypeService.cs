using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Services
{
    public class CaseTypeService
    {
        private readonly DatabaseService _databaseService;
        private readonly ErrorLogService _errorLogService;
        
        public CaseTypeService()
        {
            _databaseService = DatabaseService.Instance;
            _errorLogService = new ErrorLogService();
        }
        
        public async Task<List<CaseType>> GetAllCaseTypesAsync()
        {
            try
            {
                var query = @"
                    SELECT CaseTypeId, TypeName, TypeNameFr, Description, IsActive, CreatedDate
                    FROM CaseTypes
                    WHERE IsActive = 1
                    ORDER BY TypeName";
                
                var dataTable = await _databaseService.ExecuteQueryAsync(query);
                var caseTypes = new List<CaseType>();
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var caseType = new CaseType
                    {
                        CaseTypeId = (int)row["CaseTypeId"],
                        TypeName = row["TypeName"].ToString()!,
                        TypeNameFr = row["TypeNameFr"] as string,
                        Description = row["Description"] as string,
                        IsActive = (bool)row["IsActive"],
                        CreatedDate = (DateTime)row["CreatedDate"]
                    };
                    caseTypes.Add(caseType);
                }
                
                return caseTypes;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CaseTypeService.GetAllCaseTypes", ex);
                throw;
            }
        }
    }
}
