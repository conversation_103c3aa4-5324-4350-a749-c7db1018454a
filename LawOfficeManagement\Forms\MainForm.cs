using System;
using System.Drawing;
using System.Windows.Forms;
using LawOfficeManagement.Models;
using LawOfficeManagement.Localization;

namespace LawOfficeManagement.Forms
{
    public partial class MainForm : Form
    {
        private readonly User _currentUser;
        private readonly LanguageManager _languageManager;
        
        public MainForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            ApplyTheme();
            SetupUserInterface();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("AppTitle");
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.ShowIcon = true;
            this.ShowInTaskbar = true;
            
            // تطبيق اللغة
            _languageManager.ApplyLanguageToForm(this);
            
            // الاشتراك في تغيير اللغة
            _languageManager.LanguageChanged += OnLanguageChanged;
        }
        
        private void ApplyTheme()
        {
            // ألوان المحاماة الاحترافية
            var primaryColor = Color.FromArgb(25, 42, 86);      // كحلي داكن
            var secondaryColor = Color.FromArgb(218, 165, 32);   // ذهبي
            var backgroundColor = Color.FromArgb(248, 249, 250); // رمادي فاتح
            var sidebarColor = Color.FromArgb(52, 58, 64);       // رمادي داكن
            
            this.BackColor = backgroundColor;
            
            // تطبيق الألوان على القوائم والأشرطة
            if (menuStrip != null)
            {
                menuStrip.BackColor = primaryColor;
                menuStrip.ForeColor = Color.White;
                
                foreach (ToolStripMenuItem item in menuStrip.Items)
                {
                    item.ForeColor = Color.White;
                    item.BackColor = primaryColor;
                }
            }
            
            if (statusStrip != null)
            {
                statusStrip.BackColor = sidebarColor;
                statusStrip.ForeColor = Color.White;
            }
            
            if (toolStrip != null)
            {
                toolStrip.BackColor = backgroundColor;
            }
        }
        
        private void SetupUserInterface()
        {
            // إعداد شريط الحالة
            lblUserInfo.Text = $"مرحباً، {_currentUser.FullName} ({_currentUser.Role})";
            lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            
            // تحديث الوقت كل دقيقة
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 60000; // دقيقة واحدة
            timer.Tick += (s, e) => lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();
            
            // إعداد الصلاحيات حسب دور المستخدم
            SetupPermissions();
        }
        
        private void SetupPermissions()
        {
            switch (_currentUser.Role)
            {
                case UserRole.Admin:
                    // المدير له صلاحية كاملة
                    break;
                    
                case UserRole.Lawyer:
                    // المحامي له صلاحية محدودة
                    mnuUserManagement.Visible = false;
                    mnuSystemSettings.Visible = false;
                    break;
                    
                case UserRole.Secretary:
                    // السكرتير له صلاحية أقل
                    mnuUserManagement.Visible = false;
                    mnuSystemSettings.Visible = false;
                    mnuReports.Enabled = false;
                    break;
                    
                case UserRole.Assistant:
                    // المساعد له صلاحية قراءة فقط
                    mnuUserManagement.Visible = false;
                    mnuSystemSettings.Visible = false;
                    mnuReports.Enabled = false;
                    mnuFinancial.Enabled = false;
                    break;
            }
        }
        
        private void OnLanguageChanged(object? sender, LanguageChangedEventArgs e)
        {
            _languageManager.ApplyLanguageToForm(this);
            UpdateMenuTexts();
        }
        
        private void UpdateMenuTexts()
        {
            // تحديث نصوص القوائم
            mnuCases.Text = _languageManager.GetString("Cases");
            mnuClients.Text = _languageManager.GetString("Clients");
            mnuHearings.Text = _languageManager.GetString("Hearings");
            mnuAppointments.Text = _languageManager.GetString("Appointments");
            mnuFinancial.Text = _languageManager.GetString("Financial");
            mnuReports.Text = _languageManager.GetString("Reports");
            mnuSettings.Text = _languageManager.GetString("Settings");
        }
        
        // معالجات الأحداث للقوائم
        private void mnuCases_Click(object sender, EventArgs e)
        {
            OpenChildForm(new CaseManagementForm());
        }
        
        private void mnuClients_Click(object sender, EventArgs e)
        {
            OpenChildForm(new ClientManagementForm());
        }
        
        private void mnuHearings_Click(object sender, EventArgs e)
        {
            OpenChildForm(new HearingManagementForm());
        }
        
        private void mnuAppointments_Click(object sender, EventArgs e)
        {
            OpenChildForm(new AppointmentManagementForm());
        }
        
        private void mnuFinancial_Click(object sender, EventArgs e)
        {
            OpenChildForm(new FinancialManagementForm());
        }
        
        private void mnuReports_Click(object sender, EventArgs e)
        {
            OpenChildForm(new ReportsForm());
        }
        
        private void mnuSettings_Click(object sender, EventArgs e)
        {
            OpenChildForm(new SettingsForm());
        }
        
        private void mnuLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(_languageManager.GetString("LogoutConfirmation") ?? "هل تريد تسجيل الخروج؟",
                _languageManager.GetString("Logout"),
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                this.Hide();
                var loginForm = new LoginForm();
                loginForm.Show();
                this.Close();
            }
        }
        
        private void mnuExit_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟",
                _languageManager.GetString("Exit"),
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
        
        private void OpenChildForm(Form childForm)
        {
            // إغلاق النافذة الفرعية الحالية إن وجدت
            foreach (Form form in this.MdiChildren)
            {
                form.Close();
            }
            
            // فتح النافذة الجديدة
            childForm.MdiParent = this;
            childForm.WindowState = FormWindowState.Maximized;
            childForm.Show();
        }
        
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // حفظ الإعدادات عند الإغلاق
            Properties.Settings.Default.Save();
            base.OnFormClosing(e);
        }
    }
}
