{"dotnet.defaultSolution": "LawFirmManagementSystem.sln", "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "csharp.semanticHighlighting.enabled": true, "csharp.format.enable": true, "files.encoding": "utf8", "files.eol": "\r\n", "editor.insertSpaces": true, "editor.tabSize": 4, "editor.detectIndentation": false, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "editor.rulers": [120], "workbench.colorTheme": "Visual Studio Dark", "editor.fontFamily": "'Cascadia Code', 'Fira Code', 'Consolas', 'Courier New', monospace", "editor.fontLigatures": true, "terminal.integrated.defaultProfile.windows": "PowerShell", "git.enableSmartCommit": true, "git.confirmSync": false, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/bin": true, "**/obj": true, "**/.vs": true, "**/Logs": true, "**/Documents": true, "**/Backups": true}, "files.watcherExclude": {"**/bin/**": true, "**/obj/**": true, "**/.vs/**": true, "**/Logs/**": true, "**/Documents/**": true, "**/Backups/**": true}}