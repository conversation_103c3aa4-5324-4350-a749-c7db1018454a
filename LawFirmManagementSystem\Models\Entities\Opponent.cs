using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الخصم
    /// Opponent entity
    /// </summary>
    [Table("Opponents")]
    public class Opponent : BaseEntity
    {
        [Required]
        [Column("CaseId")]
        public int CaseId { get; set; }

        [Required]
        [StringLength(10)]
        [Column("OpponentType")]
        public string OpponentType { get; set; } = "Individual"; // Individual, Company, Government

        [Required]
        [StringLength(100)]
        [Column("FullName")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("FullNameFr")]
        public string? FullNameFr { get; set; }

        [StringLength(100)]
        [Column("FirstName")]
        public string? FirstName { get; set; }

        [StringLength(100)]
        [Column("FirstNameFr")]
        public string? FirstNameFr { get; set; }

        [StringLength(100)]
        [Column("LastName")]
        public string? LastName { get; set; }

        [StringLength(100)]
        [Column("LastNameFr")]
        public string? LastNameFr { get; set; }

        [StringLength(20)]
        [Column("CIN")]
        public string? CIN { get; set; }

        [StringLength(50)]
        [Column("PassportNumber")]
        public string? PassportNumber { get; set; }

        [StringLength(50)]
        [Column("CommercialRegister")]
        public string? CommercialRegister { get; set; }

        [StringLength(50)]
        [Column("ICE")]
        public string? ICE { get; set; }

        [StringLength(100)]
        [EmailAddress]
        [Column("Email")]
        public string? Email { get; set; }

        [StringLength(20)]
        [Column("Phone")]
        public string? Phone { get; set; }

        [StringLength(20)]
        [Column("Mobile")]
        public string? Mobile { get; set; }

        [StringLength(20)]
        [Column("Fax")]
        public string? Fax { get; set; }

        [StringLength(200)]
        [Column("Address")]
        public string? Address { get; set; }

        [StringLength(200)]
        [Column("AddressFr")]
        public string? AddressFr { get; set; }

        [StringLength(50)]
        [Column("City")]
        public string? City { get; set; }

        [StringLength(50)]
        [Column("CityFr")]
        public string? CityFr { get; set; }

        [StringLength(50)]
        [Column("Province")]
        public string? Province { get; set; }

        [StringLength(50)]
        [Column("ProvinceFr")]
        public string? ProvinceFr { get; set; }

        [StringLength(10)]
        [Column("PostalCode")]
        public string? PostalCode { get; set; }

        [StringLength(50)]
        [Column("Country")]
        public string? Country { get; set; } = "المغرب";

        [StringLength(50)]
        [Column("CountryFr")]
        public string? CountryFr { get; set; } = "Maroc";

        [StringLength(100)]
        [Column("LawyerName")]
        public string? LawyerName { get; set; }

        [StringLength(100)]
        [Column("LawyerNameFr")]
        public string? LawyerNameFr { get; set; }

        [StringLength(100)]
        [Column("LawyerFirm")]
        public string? LawyerFirm { get; set; }

        [StringLength(100)]
        [Column("LawyerFirmFr")]
        public string? LawyerFirmFr { get; set; }

        [StringLength(100)]
        [EmailAddress]
        [Column("LawyerEmail")]
        public string? LawyerEmail { get; set; }

        [StringLength(20)]
        [Column("LawyerPhone")]
        public string? LawyerPhone { get; set; }

        [StringLength(20)]
        [Column("LawyerMobile")]
        public string? LawyerMobile { get; set; }

        [StringLength(200)]
        [Column("LawyerAddress")]
        public string? LawyerAddress { get; set; }

        [StringLength(200)]
        [Column("LawyerAddressFr")]
        public string? LawyerAddressFr { get; set; }

        [StringLength(50)]
        [Column("LawyerBarNumber")]
        public string? LawyerBarNumber { get; set; }

        [StringLength(50)]
        [Column("LawyerBarAssociation")]
        public string? LawyerBarAssociation { get; set; }

        [StringLength(50)]
        [Column("LawyerBarAssociationFr")]
        public string? LawyerBarAssociationFr { get; set; }

        [StringLength(50)]
        [Column("Role")]
        public string? Role { get; set; } // المدعى عليه، المدعي المقابل، الطرف الثالث

        [StringLength(50)]
        [Column("RoleFr")]
        public string? RoleFr { get; set; }

        [StringLength(50)]
        [Column("RelationshipToCase")]
        public string? RelationshipToCase { get; set; }

        [StringLength(50)]
        [Column("RelationshipToCaseFr")]
        public string? RelationshipToCaseFr { get; set; }

        [Column("IsMainOpponent")]
        public bool IsMainOpponent { get; set; } = false;

        [Column("IsRepresented")]
        public bool IsRepresented { get; set; } = false;

        [StringLength(2000)]
        [Column("SpecialNotes")]
        public string? SpecialNotes { get; set; }

        [StringLength(2000)]
        [Column("SpecialNotesFr")]
        public string? SpecialNotesFr { get; set; }

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase Case { get; set; } = null!;

        /// <summary>
        /// الحصول على الاسم الكامل حسب اللغة
        /// Get full name by language
        /// </summary>
        public string GetFullName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(FullNameFr) 
                ? FullNameFr 
                : FullName;
        }

        /// <summary>
        /// الحصول على العنوان حسب اللغة
        /// Get address by language
        /// </summary>
        public string GetAddress(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(AddressFr) 
                ? AddressFr 
                : Address ?? string.Empty;
        }

        /// <summary>
        /// الحصول على اسم المحامي حسب اللغة
        /// Get lawyer name by language
        /// </summary>
        public string GetLawyerName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(LawyerNameFr) 
                ? LawyerNameFr 
                : LawyerName ?? string.Empty;
        }

        /// <summary>
        /// الحصول على اسم مكتب المحامي حسب اللغة
        /// Get lawyer firm name by language
        /// </summary>
        public string GetLawyerFirm(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(LawyerFirmFr) 
                ? LawyerFirmFr 
                : LawyerFirm ?? string.Empty;
        }

        /// <summary>
        /// الحصول على الدور حسب اللغة
        /// Get role by language
        /// </summary>
        public string GetRole(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(RoleFr) 
                ? RoleFr 
                : Role ?? string.Empty;
        }

        /// <summary>
        /// الحصول على العلاقة بالقضية حسب اللغة
        /// Get relationship to case by language
        /// </summary>
        public string GetRelationshipToCase(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(RelationshipToCaseFr) 
                ? RelationshipToCaseFr 
                : RelationshipToCase ?? string.Empty;
        }

        /// <summary>
        /// الحصول على العنوان الكامل
        /// Get full address
        /// </summary>
        public string GetFullAddress(string language = "ar")
        {
            var address = GetAddress(language);
            var city = language.ToLower() == "fr" && !string.IsNullOrEmpty(CityFr) ? CityFr : City;
            var province = language.ToLower() == "fr" && !string.IsNullOrEmpty(ProvinceFr) ? ProvinceFr : Province;

            var parts = new List<string>();
            if (!string.IsNullOrEmpty(address)) parts.Add(address);
            if (!string.IsNullOrEmpty(city)) parts.Add(city);
            if (!string.IsNullOrEmpty(province)) parts.Add(province);
            if (!string.IsNullOrEmpty(PostalCode)) parts.Add(PostalCode);

            return string.Join(", ", parts);
        }
    }
}
