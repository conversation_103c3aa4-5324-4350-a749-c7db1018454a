using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الدفعة
    /// Payment entity
    /// </summary>
    [Table("Payments")]
    public class Payment : BaseEntity
    {
        [Required]
        [StringLength(50)]
        [Column("PaymentNumber")]
        public string PaymentNumber { get; set; } = string.Empty;

        [Column("InvoiceId")]
        public int? InvoiceId { get; set; }

        [Column("CaseId")]
        public int? CaseId { get; set; }

        [Required]
        [Column("ClientId")]
        public int ClientId { get; set; }

        [Required]
        [Column("Amount", TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [Column("PaymentDate")]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        [Column("PaymentMethod")]
        public string PaymentMethod { get; set; } = "نقد"; // نقد، شيك، تحويل، بطاقة

        [StringLength(50)]
        [Column("PaymentMethodFr")]
        public string? PaymentMethodFr { get; set; }

        [StringLength(100)]
        [Column("PaymentReference")]
        public string? PaymentReference { get; set; }

        [StringLength(100)]
        [Column("CheckNumber")]
        public string? CheckNumber { get; set; }

        [StringLength(100)]
        [Column("BankName")]
        public string? BankName { get; set; }

        [StringLength(100)]
        [Column("BankNameFr")]
        public string? BankNameFr { get; set; }

        [Column("CheckDate")]
        public DateTime? CheckDate { get; set; }

        [StringLength(100)]
        [Column("TransactionId")]
        public string? TransactionId { get; set; }

        [StringLength(100)]
        [Column("CardLastFourDigits")]
        public string? CardLastFourDigits { get; set; }

        [StringLength(50)]
        [Column("CardType")]
        public string? CardType { get; set; } // Visa, MasterCard, etc.

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مؤكد"; // مؤكد، معلق، مرفوض، ملغي

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(50)]
        [Column("Currency")]
        public string Currency { get; set; } = "MAD";

        [Column("ExchangeRate", TypeName = "decimal(10,4)")]
        public decimal? ExchangeRate { get; set; }

        [Column("AmountInBaseCurrency", TypeName = "decimal(18,2)")]
        public decimal? AmountInBaseCurrency { get; set; }

        [StringLength(500)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [StringLength(2000)]
        [Column("Notes")]
        public string? Notes { get; set; }

        [StringLength(2000)]
        [Column("NotesFr")]
        public string? NotesFr { get; set; }

        [Column("ReceivedBy")]
        public int? ReceivedBy { get; set; }

        [Column("ProcessedBy")]
        public int? ProcessedBy { get; set; }

        [Column("ProcessedDate")]
        public DateTime? ProcessedDate { get; set; }

        [Column("DepositDate")]
        public DateTime? DepositDate { get; set; }

        [Column("ClearanceDate")]
        public DateTime? ClearanceDate { get; set; }

        [StringLength(500)]
        [Column("ReceiptPath")]
        public string? ReceiptPath { get; set; }

        [Column("ReceiptGenerated")]
        public bool ReceiptGenerated { get; set; } = false;

        [Column("ReceiptGeneratedDate")]
        public DateTime? ReceiptGeneratedDate { get; set; }

        [Column("IsRefund")]
        public bool IsRefund { get; set; } = false;

        [Column("RefundReason")]
        public string? RefundReason { get; set; }

        [Column("RefundReasonFr")]
        public string? RefundReasonFr { get; set; }

        [Column("OriginalPaymentId")]
        public int? OriginalPaymentId { get; set; }

        [Column("IsPartialPayment")]
        public bool IsPartialPayment { get; set; } = false;

        [Column("IsAdvancePayment")]
        public bool IsAdvancePayment { get; set; } = false;

        [Column("AllocationDate")]
        public DateTime? AllocationDate { get; set; }

        [Column("AllocatedAmount", TypeName = "decimal(18,2)")]
        public decimal? AllocatedAmount { get; set; }

        [Column("UnallocatedAmount", TypeName = "decimal(18,2)")]
        public decimal? UnallocatedAmount { get; set; }

        [StringLength(20)]
        [Column("PaymentSource")]
        public string? PaymentSource { get; set; } // عميل، تأمين، طرف ثالث

        [StringLength(20)]
        [Column("PaymentSourceFr")]
        public string? PaymentSourceFr { get; set; }

        [Column("TaxAmount", TypeName = "decimal(18,2)")]
        public decimal? TaxAmount { get; set; }

        [Column("NetAmount", TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }

        [Column("FeeAmount", TypeName = "decimal(18,2)")]
        public decimal? FeeAmount { get; set; }

        [StringLength(500)]
        [Column("FeeDescription")]
        public string? FeeDescription { get; set; }

        [StringLength(500)]
        [Column("FeeDescriptionFr")]
        public string? FeeDescriptionFr { get; set; }

        // Navigation Properties
        [ForeignKey("InvoiceId")]
        public virtual Invoice? Invoice { get; set; }

        [ForeignKey("CaseId")]
        public virtual LegalCase? Case { get; set; }

        [ForeignKey("ClientId")]
        public virtual Client Client { get; set; } = null!;

        [ForeignKey("ReceivedBy")]
        public virtual User? ReceivedByUser { get; set; }

        [ForeignKey("ProcessedBy")]
        public virtual User? ProcessedByUser { get; set; }

        [ForeignKey("OriginalPaymentId")]
        public virtual Payment? OriginalPayment { get; set; }

        /// <summary>
        /// الحصول على طريقة الدفع حسب اللغة
        /// Get payment method by language
        /// </summary>
        public string GetPaymentMethod(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(PaymentMethodFr) 
                ? PaymentMethodFr 
                : PaymentMethod;
        }

        /// <summary>
        /// الحصول على حالة الدفعة حسب اللغة
        /// Get payment status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// الحصول على الوصف حسب اللغة
        /// Get description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// التحقق من تأكيد الدفعة
        /// Check if payment is confirmed
        /// </summary>
        public bool IsConfirmed()
        {
            return Status == "مؤكد" || Status == "Confirmed";
        }

        /// <summary>
        /// التحقق من انتظار الدفعة
        /// Check if payment is pending
        /// </summary>
        public bool IsPending()
        {
            return Status == "معلق" || Status == "Pending";
        }

        /// <summary>
        /// التحقق من رفض الدفعة
        /// Check if payment is rejected
        /// </summary>
        public bool IsRejected()
        {
            return Status == "مرفوض" || Status == "Rejected";
        }

        /// <summary>
        /// تأكيد الدفعة
        /// Confirm payment
        /// </summary>
        public void ConfirmPayment(int processedBy)
        {
            Status = "مؤكد";
            StatusFr = "Confirmed";
            ProcessedBy = processedBy;
            ProcessedDate = DateTime.Now;
        }

        /// <summary>
        /// رفض الدفعة
        /// Reject payment
        /// </summary>
        public void RejectPayment(string reason, string? reasonFr = null)
        {
            Status = "مرفوض";
            StatusFr = "Rejected";
            Notes = reason;
            NotesFr = reasonFr;
        }

        /// <summary>
        /// إلغاء الدفعة
        /// Cancel payment
        /// </summary>
        public void CancelPayment(string reason, string? reasonFr = null)
        {
            Status = "ملغي";
            StatusFr = "Cancelled";
            Notes = reason;
            NotesFr = reasonFr;
        }

        /// <summary>
        /// معالجة الدفعة
        /// Process payment
        /// </summary>
        public void ProcessPayment(int processedBy)
        {
            ProcessedBy = processedBy;
            ProcessedDate = DateTime.Now;
            
            if (PaymentMethod == "شيك" || PaymentMethod == "Check")
            {
                Status = "معلق";
                StatusFr = "Pending";
            }
            else
            {
                ConfirmPayment(processedBy);
            }
        }

        /// <summary>
        /// تسوية الشيك
        /// Clear check
        /// </summary>
        public void ClearCheck()
        {
            if (PaymentMethod == "شيك" || PaymentMethod == "Check")
            {
                ClearanceDate = DateTime.Now;
                ConfirmPayment(ProcessedBy ?? CreatedBy ?? 0);
            }
        }

        /// <summary>
        /// حساب المبلغ الصافي
        /// Calculate net amount
        /// </summary>
        public void CalculateNetAmount()
        {
            var taxAmount = TaxAmount ?? 0;
            var feeAmount = FeeAmount ?? 0;
            NetAmount = Amount - taxAmount - feeAmount;
        }
    }
}
