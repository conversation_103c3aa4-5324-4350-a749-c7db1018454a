using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان ملاحظة القضية
    /// Case note entity
    /// </summary>
    [Table("CaseNotes")]
    public class CaseNote : BaseEntity
    {
        [Required]
        [Column("CaseId")]
        public int CaseId { get; set; }

        [Required]
        [StringLength(200)]
        [Column("Title")]
        public string Title { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("TitleFr")]
        public string? TitleFr { get; set; }

        [Required]
        [Column("Content")]
        public string Content { get; set; } = string.Empty;

        [Column("ContentFr")]
        public string? ContentFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("NoteType")]
        public string NoteType { get; set; } = "عام"; // عام، قانوني، إداري، مالي، شخصي

        [StringLength(50)]
        [Column("NoteTypeFr")]
        public string? NoteTypeFr { get; set; }

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [Column("IsPrivate")]
        public bool IsPrivate { get; set; } = false;

        [Column("IsImportant")]
        public bool IsImportant { get; set; } = false;

        [Column("IsActionRequired")]
        public bool IsActionRequired { get; set; } = false;

        [StringLength(500)]
        [Column("ActionRequired")]
        public string? ActionRequired { get; set; }

        [StringLength(500)]
        [Column("ActionRequiredFr")]
        public string? ActionRequiredFr { get; set; }

        [Column("ActionDueDate")]
        public DateTime? ActionDueDate { get; set; }

        [Column("ActionCompletedDate")]
        public DateTime? ActionCompletedDate { get; set; }

        [Column("ActionCompletedBy")]
        public int? ActionCompletedBy { get; set; }

        [Column("IsFollowUpRequired")]
        public bool IsFollowUpRequired { get; set; } = false;

        [Column("FollowUpDate")]
        public DateTime? FollowUpDate { get; set; }

        [Column("FollowUpCompleted")]
        public bool FollowUpCompleted { get; set; } = false;

        [Column("FollowUpCompletedDate")]
        public DateTime? FollowUpCompletedDate { get; set; }

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "نشط"; // نشط، مؤرشف، محذوف

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [Column("IsTemplate")]
        public bool IsTemplate { get; set; } = false;

        [StringLength(100)]
        [Column("TemplateName")]
        public string? TemplateName { get; set; }

        [StringLength(100)]
        [Column("TemplateNameFr")]
        public string? TemplateNameFr { get; set; }

        [Column("TemplateId")]
        public int? TemplateId { get; set; }

        [Column("IsSystemGenerated")]
        public bool IsSystemGenerated { get; set; } = false;

        [StringLength(100)]
        [Column("SystemSource")]
        public string? SystemSource { get; set; }

        [Column("RelatedEntityType")]
        public string? RelatedEntityType { get; set; }

        [Column("RelatedEntityId")]
        public int? RelatedEntityId { get; set; }

        [StringLength(200)]
        [Column("RelatedEntityName")]
        public string? RelatedEntityName { get; set; }

        [Column("IsShared")]
        public bool IsShared { get; set; } = false;

        [StringLength(500)]
        [Column("SharedWith")]
        public string? SharedWith { get; set; } // JSON array of user IDs

        [Column("ShareDate")]
        public DateTime? ShareDate { get; set; }

        [Column("ShareExpiryDate")]
        public DateTime? ShareExpiryDate { get; set; }

        [Column("IsEditable")]
        public bool IsEditable { get; set; } = true;

        [Column("IsDeletable")]
        public bool IsDeletable { get; set; } = true;

        [Column("IsCommentable")]
        public bool IsCommentable { get; set; } = true;

        [Column("CommentCount")]
        public int CommentCount { get; set; } = 0;

        [Column("LastCommentDate")]
        public DateTime? LastCommentDate { get; set; }

        [Column("ViewCount")]
        public int ViewCount { get; set; } = 0;

        [Column("LastViewDate")]
        public DateTime? LastViewDate { get; set; }

        [Column("LastViewBy")]
        public int? LastViewBy { get; set; }

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; }

        [StringLength(50)]
        [Column("Icon")]
        public string? Icon { get; set; }

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        [Column("IsPinned")]
        public bool IsPinned { get; set; } = false;

        [Column("PinnedDate")]
        public DateTime? PinnedDate { get; set; }

        [Column("PinnedBy")]
        public int? PinnedBy { get; set; }

        [Column("IsLocked")]
        public bool IsLocked { get; set; } = false;

        [Column("LockedDate")]
        public DateTime? LockedDate { get; set; }

        [Column("LockedBy")]
        public int? LockedBy { get; set; }

        [StringLength(500)]
        [Column("LockReason")]
        public string? LockReason { get; set; }

        [StringLength(500)]
        [Column("LockReasonFr")]
        public string? LockReasonFr { get; set; }

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase Case { get; set; } = null!;

        [ForeignKey("ActionCompletedBy")]
        public virtual User? ActionCompletedByUser { get; set; }

        [ForeignKey("PinnedBy")]
        public virtual User? PinnedByUser { get; set; }

        [ForeignKey("LockedBy")]
        public virtual User? LockedByUser { get; set; }

        [ForeignKey("TemplateId")]
        public virtual CaseNote? Template { get; set; }

        /// <summary>
        /// الحصول على العنوان حسب اللغة
        /// Get title by language
        /// </summary>
        public string GetTitle(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(TitleFr) 
                ? TitleFr 
                : Title;
        }

        /// <summary>
        /// الحصول على المحتوى حسب اللغة
        /// Get content by language
        /// </summary>
        public string GetContent(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ContentFr) 
                ? ContentFr 
                : Content;
        }

        /// <summary>
        /// الحصول على نوع الملاحظة حسب اللغة
        /// Get note type by language
        /// </summary>
        public string GetNoteType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(NoteTypeFr) 
                ? NoteTypeFr 
                : NoteType;
        }

        /// <summary>
        /// الحصول على الأولوية حسب اللغة
        /// Get priority by language
        /// </summary>
        public string GetPriority(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(PriorityFr) 
                ? PriorityFr 
                : Priority;
        }

        /// <summary>
        /// التحقق من انتهاء موعد الإجراء المطلوب
        /// Check if action is overdue
        /// </summary>
        public bool IsActionOverdue()
        {
            return IsActionRequired && 
                   ActionDueDate.HasValue && 
                   ActionDueDate.Value < DateTime.Now && 
                   !ActionCompletedDate.HasValue;
        }

        /// <summary>
        /// التحقق من انتهاء موعد المتابعة
        /// Check if follow-up is overdue
        /// </summary>
        public bool IsFollowUpOverdue()
        {
            return IsFollowUpRequired && 
                   FollowUpDate.HasValue && 
                   FollowUpDate.Value < DateTime.Now && 
                   !FollowUpCompleted;
        }

        /// <summary>
        /// إكمال الإجراء المطلوب
        /// Complete required action
        /// </summary>
        public void CompleteAction(int completedBy)
        {
            ActionCompletedDate = DateTime.Now;
            ActionCompletedBy = completedBy;
        }

        /// <summary>
        /// إكمال المتابعة
        /// Complete follow-up
        /// </summary>
        public void CompleteFollowUp()
        {
            FollowUpCompleted = true;
            FollowUpCompletedDate = DateTime.Now;
        }

        /// <summary>
        /// تثبيت الملاحظة
        /// Pin note
        /// </summary>
        public void PinNote(int pinnedBy)
        {
            IsPinned = true;
            PinnedDate = DateTime.Now;
            PinnedBy = pinnedBy;
        }

        /// <summary>
        /// إلغاء تثبيت الملاحظة
        /// Unpin note
        /// </summary>
        public void UnpinNote()
        {
            IsPinned = false;
            PinnedDate = null;
            PinnedBy = null;
        }

        /// <summary>
        /// قفل الملاحظة
        /// Lock note
        /// </summary>
        public void LockNote(int lockedBy, string reason, string? reasonFr = null)
        {
            IsLocked = true;
            LockedDate = DateTime.Now;
            LockedBy = lockedBy;
            LockReason = reason;
            LockReasonFr = reasonFr;
            IsEditable = false;
            IsDeletable = false;
        }

        /// <summary>
        /// إلغاء قفل الملاحظة
        /// Unlock note
        /// </summary>
        public void UnlockNote()
        {
            IsLocked = false;
            LockedDate = null;
            LockedBy = null;
            LockReason = null;
            LockReasonFr = null;
            IsEditable = true;
            IsDeletable = true;
        }

        /// <summary>
        /// مشاركة الملاحظة
        /// Share note
        /// </summary>
        public void ShareNote(string userIds, DateTime? expiryDate = null)
        {
            IsShared = true;
            SharedWith = userIds;
            ShareDate = DateTime.Now;
            ShareExpiryDate = expiryDate;
        }

        /// <summary>
        /// إلغاء مشاركة الملاحظة
        /// Unshare note
        /// </summary>
        public void UnshareNote()
        {
            IsShared = false;
            SharedWith = null;
            ShareDate = null;
            ShareExpiryDate = null;
        }

        /// <summary>
        /// تسجيل عرض الملاحظة
        /// Record note view
        /// </summary>
        public void RecordView(int? viewedBy = null)
        {
            ViewCount++;
            LastViewDate = DateTime.Now;
            LastViewBy = viewedBy;
        }

        /// <summary>
        /// أرشفة الملاحظة
        /// Archive note
        /// </summary>
        public void ArchiveNote()
        {
            Status = "مؤرشف";
            StatusFr = "Archived";
        }

        /// <summary>
        /// استعادة الملاحظة من الأرشيف
        /// Restore note from archive
        /// </summary>
        public void RestoreNote()
        {
            Status = "نشط";
            StatusFr = "Active";
        }
    }
}
