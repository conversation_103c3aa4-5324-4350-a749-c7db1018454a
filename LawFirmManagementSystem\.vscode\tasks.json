{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/LawFirmManagementSystem.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/LawFirmManagementSystem.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/LawFirmManagementSystem.csproj"], "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/LawFirmManagementSystem.csproj"], "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/LawFirmManagementSystem.csproj"], "problemMatcher": "$msCompile"}, {"label": "run", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/LawFirmManagementSystem.csproj"], "problemMatcher": "$msCompile", "group": {"kind": "test", "isDefault": true}}, {"label": "run-dev", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/LawFirmManagementSystem.csproj", "--environment", "Development"], "problemMatcher": "$msCompile"}, {"label": "run-prod", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/LawFirmManagementSystem.csproj", "--environment", "Production"], "problemMatcher": "$msCompile"}]}