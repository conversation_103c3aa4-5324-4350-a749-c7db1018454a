using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الجلسة
    /// Hearing entity
    /// </summary>
    [Table("Hearings")]
    public class Hearing : BaseEntity
    {
        [Required]
        [Column("CaseId")]
        public int CaseId { get; set; }

        [Column("CourtId")]
        public int? CourtId { get; set; }

        [Required]
        [StringLength(100)]
        [Column("HearingTitle")]
        public string HearingTitle { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("HearingTitleFr")]
        public string? HearingTitleFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("HearingType")]
        public string HearingType { get; set; } = string.Empty; // جلسة مرافعة، جلسة حكم، جلسة تأجيل

        [StringLength(50)]
        [Column("HearingTypeFr")]
        public string? HearingTypeFr { get; set; }

        [Required]
        [Column("HearingDate")]
        public DateTime HearingDate { get; set; }

        [Required]
        [Column("HearingTime")]
        public TimeSpan HearingTime { get; set; }

        [Column("EstimatedDuration")]
        public int? EstimatedDuration { get; set; } // بالدقائق

        [Column("ActualStartTime")]
        public DateTime? ActualStartTime { get; set; }

        [Column("ActualEndTime")]
        public DateTime? ActualEndTime { get; set; }

        [StringLength(100)]
        [Column("CourtRoom")]
        public string? CourtRoom { get; set; }

        [StringLength(100)]
        [Column("JudgeName")]
        public string? JudgeName { get; set; }

        [StringLength(100)]
        [Column("JudgeNameFr")]
        public string? JudgeNameFr { get; set; }

        [StringLength(100)]
        [Column("ClerkName")]
        public string? ClerkName { get; set; }

        [StringLength(100)]
        [Column("ClerkNameFr")]
        public string? ClerkNameFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مجدولة"; // مجدولة، جارية، مكتملة، مؤجلة، ملغية

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(2000)]
        [Column("Agenda")]
        public string? Agenda { get; set; }

        [StringLength(2000)]
        [Column("AgendaFr")]
        public string? AgendaFr { get; set; }

        [StringLength(2000)]
        [Column("PreparationNotes")]
        public string? PreparationNotes { get; set; }

        [StringLength(2000)]
        [Column("PreparationNotesFr")]
        public string? PreparationNotesFr { get; set; }

        [StringLength(2000)]
        [Column("Outcome")]
        public string? Outcome { get; set; }

        [StringLength(2000)]
        [Column("OutcomeFr")]
        public string? OutcomeFr { get; set; }

        [StringLength(2000)]
        [Column("JudgeDecision")]
        public string? JudgeDecision { get; set; }

        [StringLength(2000)]
        [Column("JudgeDecisionFr")]
        public string? JudgeDecisionFr { get; set; }

        [Column("NextHearingDate")]
        public DateTime? NextHearingDate { get; set; }

        [Column("NextHearingTime")]
        public TimeSpan? NextHearingTime { get; set; }

        [StringLength(500)]
        [Column("NextHearingPurpose")]
        public string? NextHearingPurpose { get; set; }

        [StringLength(500)]
        [Column("NextHearingPurposeFr")]
        public string? NextHearingPurposeFr { get; set; }

        [Column("AttendedByLawyer")]
        public bool AttendedByLawyer { get; set; } = true;

        [Column("AttendedByClient")]
        public bool AttendedByClient { get; set; } = false;

        [StringLength(500)]
        [Column("AttendeesNotes")]
        public string? AttendeesNotes { get; set; }

        [StringLength(500)]
        [Column("AttendeesNotesFr")]
        public string? AttendeesNotesFr { get; set; }

        [Column("IsImportant")]
        public bool IsImportant { get; set; } = false;

        [Column("IsPublic")]
        public bool IsPublic { get; set; } = true;

        [Column("RequiresPreparation")]
        public bool RequiresPreparation { get; set; } = true;

        [Column("ReminderSent")]
        public bool ReminderSent { get; set; } = false;

        [Column("ReminderSentDate")]
        public DateTime? ReminderSentDate { get; set; }

        [Column("DocumentsRequired")]
        public bool DocumentsRequired { get; set; } = false;

        [StringLength(1000)]
        [Column("RequiredDocuments")]
        public string? RequiredDocuments { get; set; }

        [StringLength(1000)]
        [Column("RequiredDocumentsFr")]
        public string? RequiredDocumentsFr { get; set; }

        [Column("CostEstimate")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CostEstimate { get; set; }

        [Column("ActualCost")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ActualCost { get; set; }

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; } = "#007bff";

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase Case { get; set; } = null!;

        [ForeignKey("CourtId")]
        public virtual Court? Court { get; set; }

        /// <summary>
        /// الحصول على عنوان الجلسة حسب اللغة
        /// Get hearing title by language
        /// </summary>
        public string GetTitle(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(HearingTitleFr) 
                ? HearingTitleFr 
                : HearingTitle;
        }

        /// <summary>
        /// الحصول على نوع الجلسة حسب اللغة
        /// Get hearing type by language
        /// </summary>
        public string GetHearingType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(HearingTypeFr) 
                ? HearingTypeFr 
                : HearingType;
        }

        /// <summary>
        /// الحصول على حالة الجلسة حسب اللغة
        /// Get hearing status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// الحصول على جدول الأعمال حسب اللغة
        /// Get agenda by language
        /// </summary>
        public string GetAgenda(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(AgendaFr) 
                ? AgendaFr 
                : Agenda ?? string.Empty;
        }

        /// <summary>
        /// الحصول على النتيجة حسب اللغة
        /// Get outcome by language
        /// </summary>
        public string GetOutcome(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(OutcomeFr) 
                ? OutcomeFr 
                : Outcome ?? string.Empty;
        }

        /// <summary>
        /// الحصول على التاريخ والوقت الكامل
        /// Get full date and time
        /// </summary>
        public DateTime GetFullDateTime()
        {
            return HearingDate.Date + HearingTime;
        }

        /// <summary>
        /// التحقق من انتهاء الجلسة
        /// Check if hearing is completed
        /// </summary>
        public bool IsCompleted()
        {
            return Status == "مكتملة" || Status == "Completed";
        }

        /// <summary>
        /// التحقق من إلغاء الجلسة
        /// Check if hearing is cancelled
        /// </summary>
        public bool IsCancelled()
        {
            return Status == "ملغية" || Status == "Cancelled";
        }

        /// <summary>
        /// التحقق من تأجيل الجلسة
        /// Check if hearing is postponed
        /// </summary>
        public bool IsPostponed()
        {
            return Status == "مؤجلة" || Status == "Postponed";
        }

        /// <summary>
        /// التحقق من قرب موعد الجلسة
        /// Check if hearing is upcoming
        /// </summary>
        public bool IsUpcoming(int hoursThreshold = 24)
        {
            var hearingDateTime = GetFullDateTime();
            var now = DateTime.Now;
            return hearingDateTime > now && hearingDateTime <= now.AddHours(hoursThreshold);
        }

        /// <summary>
        /// الحصول على المدة الفعلية للجلسة
        /// Get actual duration of hearing
        /// </summary>
        public TimeSpan? GetActualDuration()
        {
            if (ActualStartTime.HasValue && ActualEndTime.HasValue)
            {
                return ActualEndTime.Value - ActualStartTime.Value;
            }
            return null;
        }

        /// <summary>
        /// بدء الجلسة
        /// Start hearing
        /// </summary>
        public void StartHearing()
        {
            ActualStartTime = DateTime.Now;
            Status = "جارية";
            StatusFr = "En cours";
        }

        /// <summary>
        /// إنهاء الجلسة
        /// End hearing
        /// </summary>
        public void EndHearing(string outcome, string? outcomeFr = null)
        {
            ActualEndTime = DateTime.Now;
            Status = "مكتملة";
            StatusFr = "Completed";
            Outcome = outcome;
            OutcomeFr = outcomeFr;
        }

        /// <summary>
        /// تأجيل الجلسة
        /// Postpone hearing
        /// </summary>
        public void PostponeHearing(DateTime newDate, TimeSpan newTime, string reason)
        {
            NextHearingDate = newDate;
            NextHearingTime = newTime;
            Status = "مؤجلة";
            StatusFr = "Postponed";
            Outcome = $"تم التأجيل: {reason}";
            OutcomeFr = $"Reporté: {reason}";
        }

        /// <summary>
        /// إلغاء الجلسة
        /// Cancel hearing
        /// </summary>
        public void CancelHearing(string reason)
        {
            Status = "ملغية";
            StatusFr = "Cancelled";
            Outcome = $"تم الإلغاء: {reason}";
            OutcomeFr = $"Annulé: {reason}";
        }
    }
}
