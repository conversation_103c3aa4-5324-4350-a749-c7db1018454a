using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان صلاحيات المستخدم
    /// User permission entity
    /// </summary>
    [Table("UserPermissions")]
    public class UserPermission : BaseEntity
    {
        [Required]
        [Column("UserId")]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        [Column("PermissionName")]
        public string PermissionName { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("PermissionNameFr")]
        public string? PermissionNameFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Module")]
        public string Module { get; set; } = string.Empty; // Cases, Clients, Users, Reports, etc.

        [StringLength(50)]
        [Column("ModuleFr")]
        public string? ModuleFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Action")]
        public string Action { get; set; } = string.Empty; // Create, Read, Update, Delete, Export, etc.

        [StringLength(50)]
        [Column("ActionFr")]
        public string? ActionFr { get; set; }

        [Required]
        [Column("IsGranted")]
        public bool IsGranted { get; set; } = true;

        [StringLength(500)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [StringLength(50)]
        [Column("PermissionLevel")]
        public string PermissionLevel { get; set; } = "User"; // System, Admin, Manager, User

        [StringLength(50)]
        [Column("PermissionLevelFr")]
        public string? PermissionLevelFr { get; set; }

        [Column("GrantedBy")]
        public int? GrantedBy { get; set; }

        [Column("GrantedDate")]
        public DateTime? GrantedDate { get; set; }

        [Column("ExpiryDate")]
        public DateTime? ExpiryDate { get; set; }

        [Column("IsTemporary")]
        public bool IsTemporary { get; set; } = false;

        [StringLength(2000)]
        [Column("Conditions")]
        public string? Conditions { get; set; } // JSON conditions

        [StringLength(2000)]
        [Column("Restrictions")]
        public string? Restrictions { get; set; } // JSON restrictions

        [Column("MaxUsageCount")]
        public int? MaxUsageCount { get; set; }

        [Column("CurrentUsageCount")]
        public int CurrentUsageCount { get; set; } = 0;

        [Column("LastUsedDate")]
        public DateTime? LastUsedDate { get; set; }

        [Column("IsInherited")]
        public bool IsInherited { get; set; } = false;

        [StringLength(50)]
        [Column("InheritedFrom")]
        public string? InheritedFrom { get; set; } // Role, Group, etc.

        [Column("Priority")]
        public int Priority { get; set; } = 0;

        [Column("IsSystemPermission")]
        public bool IsSystemPermission { get; set; } = false;

        [Column("CanDelegate")]
        public bool CanDelegate { get; set; } = false;

        [Column("CanRevoke")]
        public bool CanRevoke { get; set; } = true;

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("GrantedBy")]
        public virtual User? GrantedByUser { get; set; }

        /// <summary>
        /// الحصول على اسم الصلاحية حسب اللغة
        /// Get permission name by language
        /// </summary>
        public string GetPermissionName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(PermissionNameFr) 
                ? PermissionNameFr 
                : PermissionName;
        }

        /// <summary>
        /// الحصول على اسم الوحدة حسب اللغة
        /// Get module name by language
        /// </summary>
        public string GetModule(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ModuleFr) 
                ? ModuleFr 
                : Module;
        }

        /// <summary>
        /// الحصول على اسم الإجراء حسب اللغة
        /// Get action name by language
        /// </summary>
        public string GetAction(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ActionFr) 
                ? ActionFr 
                : Action;
        }

        /// <summary>
        /// الحصول على وصف الصلاحية حسب اللغة
        /// Get permission description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// التحقق من صحة الصلاحية
        /// Check if permission is valid
        /// </summary>
        public bool IsValid()
        {
            if (!IsGranted) return false;
            if (!IsActive) return false;
            if (ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now) return false;
            if (MaxUsageCount.HasValue && CurrentUsageCount >= MaxUsageCount.Value) return false;
            
            return true;
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الإذن
        /// Check if permission is expired
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value <= DateTime.Now;
        }

        /// <summary>
        /// التحقق من استنفاد عدد الاستخدامات
        /// Check if usage limit is exceeded
        /// </summary>
        public bool IsUsageLimitExceeded()
        {
            return MaxUsageCount.HasValue && CurrentUsageCount >= MaxUsageCount.Value;
        }

        /// <summary>
        /// استخدام الصلاحية
        /// Use permission
        /// </summary>
        public bool UsePermission()
        {
            if (!IsValid()) return false;
            
            CurrentUsageCount++;
            LastUsedDate = DateTime.Now;
            
            return true;
        }

        /// <summary>
        /// منح الصلاحية
        /// Grant permission
        /// </summary>
        public void GrantPermission(int grantedBy, DateTime? expiryDate = null)
        {
            IsGranted = true;
            GrantedBy = grantedBy;
            GrantedDate = DateTime.Now;
            ExpiryDate = expiryDate;
        }

        /// <summary>
        /// إلغاء الصلاحية
        /// Revoke permission
        /// </summary>
        public void RevokePermission()
        {
            IsGranted = false;
            ExpiryDate = DateTime.Now;
        }

        /// <summary>
        /// تمديد الصلاحية
        /// Extend permission
        /// </summary>
        public void ExtendPermission(DateTime newExpiryDate)
        {
            if (IsValid())
            {
                ExpiryDate = newExpiryDate;
            }
        }

        /// <summary>
        /// إعادة تعيين عداد الاستخدام
        /// Reset usage counter
        /// </summary>
        public void ResetUsageCounter()
        {
            CurrentUsageCount = 0;
        }

        /// <summary>
        /// تحديث حد الاستخدام
        /// Update usage limit
        /// </summary>
        public void UpdateUsageLimit(int newLimit)
        {
            MaxUsageCount = newLimit;
        }

        /// <summary>
        /// الحصول على الصلاحية الكاملة
        /// Get full permission string
        /// </summary>
        public string GetFullPermission()
        {
            return $"{Module}.{Action}";
        }

        /// <summary>
        /// التحقق من تطابق الصلاحية
        /// Check if permission matches
        /// </summary>
        public bool Matches(string module, string action)
        {
            return Module.Equals(module, StringComparison.OrdinalIgnoreCase) &&
                   Action.Equals(action, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// التحقق من تطابق الصلاحية مع نمط
        /// Check if permission matches pattern
        /// </summary>
        public bool MatchesPattern(string pattern)
        {
            // Support wildcards like "Cases.*" or "*.Read"
            var parts = pattern.Split('.');
            if (parts.Length != 2) return false;
            
            var modulePattern = parts[0];
            var actionPattern = parts[1];
            
            var moduleMatches = modulePattern == "*" || 
                               Module.Equals(modulePattern, StringComparison.OrdinalIgnoreCase);
            var actionMatches = actionPattern == "*" || 
                               Action.Equals(actionPattern, StringComparison.OrdinalIgnoreCase);
            
            return moduleMatches && actionMatches;
        }
    }
}
