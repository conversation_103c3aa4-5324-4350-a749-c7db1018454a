using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using LawOfficeManagement.Localization;
using LawOfficeManagement.Services;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Forms
{
    public partial class CaseManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        private readonly CaseService _caseService;
        private readonly ClientService _clientService;
        private List<LegalCase> _cases;
        private List<LegalCase> _filteredCases;

        public CaseManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            _caseService = new CaseService();
            _clientService = new ClientService();
            _cases = new List<LegalCase>();
            _filteredCases = new List<LegalCase>();

            InitializeForm();
            SetupDataGridView();
            LoadDataAsync();
        }

        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("CaseManagement");
            this.WindowState = FormWindowState.Maximized;

            _languageManager.ApplyLanguageToForm(this);
            ApplyTheme();
        }

        private void ApplyTheme()
        {
            var primaryColor = Color.FromArgb(25, 42, 86);
            var secondaryColor = Color.FromArgb(218, 165, 32);
            var backgroundColor = Color.FromArgb(248, 249, 250);

            this.BackColor = backgroundColor;

            // تطبيق الألوان على الأزرار
            btnNewCase.BackColor = primaryColor;
            btnNewCase.ForeColor = Color.White;
            btnEditCase.BackColor = secondaryColor;
            btnEditCase.ForeColor = Color.White;
            btnDeleteCase.BackColor = Color.FromArgb(220, 53, 69);
            btnDeleteCase.ForeColor = Color.White;
            btnRefresh.BackColor = Color.FromArgb(40, 167, 69);
            btnRefresh.ForeColor = Color.White;

            foreach (Button btn in new[] { btnNewCase, btnEditCase, btnDeleteCase, btnRefresh })
            {
                btn.FlatStyle = FlatStyle.Flat;
                btn.FlatAppearance.BorderSize = 0;
                btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            }
        }

        private void SetupDataGridView()
        {
            dgvCases.AutoGenerateColumns = false;
            dgvCases.AllowUserToAddRows = false;
            dgvCases.AllowUserToDeleteRows = false;
            dgvCases.ReadOnly = true;
            dgvCases.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCases.MultiSelect = false;
            dgvCases.BackgroundColor = Color.White;
            dgvCases.BorderStyle = BorderStyle.None;
            dgvCases.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgvCases.DefaultCellStyle.SelectionBackColor = Color.FromArgb(25, 42, 86);
            dgvCases.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvCases.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvCases.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCases.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dgvCases.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CaseReference",
                HeaderText = _languageManager.GetString("CaseReference"),
                DataPropertyName = "CaseReference",
                Width = 120,
                ReadOnly = true
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OfficeReference",
                HeaderText = _languageManager.GetString("OfficeReference"),
                DataPropertyName = "OfficeReference",
                Width = 120,
                ReadOnly = true
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CaseTitle",
                HeaderText = _languageManager.GetString("CaseTitle"),
                DataPropertyName = "CaseTitle",
                Width = 250,
                ReadOnly = true
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ClientName",
                HeaderText = _languageManager.GetString("Client"),
                DataPropertyName = "ClientName",
                Width = 150,
                ReadOnly = true
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CaseStatus",
                HeaderText = _languageManager.GetString("Status"),
                DataPropertyName = "CaseStatus",
                Width = 100,
                ReadOnly = true
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Priority",
                HeaderText = _languageManager.GetString("Priority"),
                DataPropertyName = "Priority",
                Width = 80,
                ReadOnly = true
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OpenDate",
                HeaderText = _languageManager.GetString("OpenDate"),
                DataPropertyName = "OpenDate",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Format = "yyyy/MM/dd" }
            });

            dgvCases.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LawyerName",
                HeaderText = _languageManager.GetString("AssignedLawyer"),
                DataPropertyName = "LawyerName",
                Width = 150,
                ReadOnly = true
            });
        }

        private async void LoadDataAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل الملفات القانونية...";
                btnRefresh.Enabled = false;

                _cases = await _caseService.GetAllCasesAsync();
                _filteredCases = new List<LegalCase>(_cases);

                UpdateDataGridView();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                lblStatus.Text = "فشل في تحميل البيانات";
            }
            finally
            {
                btnRefresh.Enabled = true;
            }
        }

        private void UpdateDataGridView()
        {
            dgvCases.DataSource = null;
            dgvCases.DataSource = _filteredCases;
            dgvCases.Refresh();
        }

        private void UpdateStatusLabel()
        {
            var totalCases = _cases.Count;
            var activeCases = _cases.Count(c => c.CaseStatus == "جاري");
            var closedCases = _cases.Count(c => c.CaseStatus == "مغلق");

            lblStatus.Text = $"إجمالي الملفات: {totalCases} | الملفات النشطة: {activeCases} | الملفات المغلقة: {closedCases}";
        }

        private async void btnNewCase_Click(object sender, EventArgs e)
        {
            try
            {
                var caseForm = new CaseDetailsForm();
                if (caseForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الملف الجديد: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private async void btnEditCase_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvCases.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار ملف للتعديل",
                        _languageManager.GetString("ValidationError"),
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    return;
                }

                var selectedCase = (LegalCase)dgvCases.SelectedRows[0].DataBoundItem;
                var caseForm = new CaseDetailsForm(selectedCase);

                if (caseForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تعديل الملف: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private async void btnDeleteCase_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvCases.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار ملف للحذف",
                        _languageManager.GetString("ValidationError"),
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    return;
                }

                var selectedCase = (LegalCase)dgvCases.SelectedRows[0].DataBoundItem;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الملف: {selectedCase.CaseTitle}؟\nهذا الإجراء لا يمكن التراجع عنه.",
                    _languageManager.GetString("DeleteConfirmation"),
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var success = await _caseService.DeleteCaseAsync(selectedCase.CaseId, 1); // TODO: استخدام معرف المستخدم الحالي

                    if (success)
                    {
                        MessageBox.Show(_languageManager.GetString("DeleteSuccessful"),
                            _languageManager.GetString("Success"),
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);

                        await LoadDataAsync();
                    }
                    else
                    {
                        MessageBox.Show(_languageManager.GetString("DeleteFailed"),
                            _languageManager.GetString("Error"),
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الملف: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterCases();
        }

        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterCases();
        }

        private void FilterCases()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim().ToLower();
                var statusFilter = cmbStatusFilter.SelectedItem?.ToString();

                _filteredCases = _cases.Where(c =>
                {
                    var matchesSearch = string.IsNullOrEmpty(searchTerm) ||
                                      c.CaseReference.ToLower().Contains(searchTerm) ||
                                      c.OfficeReference.ToLower().Contains(searchTerm) ||
                                      c.CaseTitle.ToLower().Contains(searchTerm) ||
                                      (c.ClientName?.ToLower().Contains(searchTerm) ?? false);

                    var matchesStatus = string.IsNullOrEmpty(statusFilter) ||
                                      statusFilter == "الكل" ||
                                      c.CaseStatus == statusFilter;

                    return matchesSearch && matchesStatus;
                }).ToList();

                UpdateDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية البيانات: {ex.Message}",
                    _languageManager.GetString("Error"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private void dgvCases_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEditCase_Click(sender, e);
            }
        }

        private void dgvCases_SelectionChanged(object sender, EventArgs e)
        {
            var hasSelection = dgvCases.SelectedRows.Count > 0;
            btnEditCase.Enabled = hasSelection;
            btnDeleteCase.Enabled = hasSelection;
        }
    
    public partial class ClientManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public ClientManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            LoadData();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("ClientManagement");
            this.WindowState = FormWindowState.Maximized;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void LoadData()
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
        }
    }
    
    public partial class HearingManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public HearingManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            LoadData();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("HearingManagement");
            this.WindowState = FormWindowState.Maximized;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void LoadData()
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
        }
    }
    
    public partial class AppointmentManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public AppointmentManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            LoadData();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("AppointmentManagement");
            this.WindowState = FormWindowState.Maximized;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void LoadData()
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
        }
    }
    
    public partial class FinancialManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public FinancialManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            LoadData();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("FinancialManagement");
            this.WindowState = FormWindowState.Maximized;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void LoadData()
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
        }
    }
    
    public partial class ReportsForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public ReportsForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            LoadData();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("ReportsManagement");
            this.WindowState = FormWindowState.Maximized;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void LoadData()
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
        }
    }
    
    public partial class SettingsForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public SettingsForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            
            InitializeForm();
            LoadData();
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("GeneralSettings");
            this.WindowState = FormWindowState.Maximized;
            
            _languageManager.ApplyLanguageToForm(this);
        }
        
        private void LoadData()
        {
            // سيتم تنفيذ تحميل البيانات لاحقاً
        }
    }
}
