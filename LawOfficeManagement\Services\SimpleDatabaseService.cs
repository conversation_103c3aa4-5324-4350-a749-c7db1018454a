using System;
using System.Data;
using Microsoft.Data.Sqlite;
using System.Threading.Tasks;

namespace LawOfficeManagement.Services
{
    public class SimpleDatabaseService
    {
        private static SimpleDatabaseService? _instance;
        private static readonly object _lock = new object();
        private readonly string _connectionString;

        private SimpleDatabaseService()
        {
            _connectionString = "Data Source=LawOfficeDB.sqlite;";
            InitializeDatabase();
        }

        public static SimpleDatabaseService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new SimpleDatabaseService();
                    }
                }
                return _instance;
            }
        }

        private void InitializeDatabase()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                connection.Open();

                // إنشاء جدول المستخدمين
                var createUsersTable = @"
                    CREATE TABLE IF NOT EXISTS Users (
                        UserId INTEGER PRIMARY KEY AUTOINCREMENT,
                        Username TEXT NOT NULL UNIQUE,
                        PasswordHash TEXT NOT NULL,
                        FullName TEXT NOT NULL,
                        FullNameFr TEXT,
                        Email TEXT,
                        Phone TEXT,
                        Role TEXT NOT NULL DEFAULT 'User',
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        LastLogin TEXT,
                        FailedLoginAttempts INTEGER DEFAULT 0,
                        IsLocked INTEGER DEFAULT 0,
                        LockoutEnd TEXT,
                        CreatedDate TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        CreatedBy INTEGER,
                        ModifiedDate TEXT,
                        ModifiedBy INTEGER
                    );";

                using var command = new SqliteCommand(createUsersTable, connection);
                command.ExecuteNonQuery();

                // إدراج المستخدم الافتراضي إذا لم يكن موجوداً
                var checkAdminQuery = "SELECT COUNT(*) FROM Users WHERE Username = 'admin'";
                using var checkCommand = new SqliteCommand(checkAdminQuery, connection);
                var adminExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;

                if (!adminExists)
                {
                    var insertAdminQuery = @"
                        INSERT INTO Users (Username, PasswordHash, FullName, FullNameFr, Email, Role, IsActive, CreatedDate)
                        VALUES ('admin', 'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=', 'المدير العام', 'Administrateur', '<EMAIL>', 'Admin', 1, datetime('now'))";
                    
                    using var insertCommand = new SqliteCommand(insertAdminQuery, connection);
                    insertCommand.ExecuteNonQuery();
                }

                Console.WriteLine("تم إعداد قاعدة البيانات SQLite بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إعداد قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        public async Task<DataTable> ExecuteQueryAsync(string query, params SqliteParameter[] parameters)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new SqliteCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                using var reader = await command.ExecuteReaderAsync();
                var dataTable = new DataTable();
                dataTable.Load(reader);

                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تنفيذ الاستعلام: {ex.Message}");
                throw;
            }
        }

        public async Task<int> ExecuteNonQueryAsync(string query, params SqliteParameter[] parameters)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new SqliteCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تنفيذ الأمر: {ex.Message}");
                throw;
            }
        }

        public async Task<object?> ExecuteScalarAsync(string query, params SqliteParameter[] parameters)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new SqliteCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                return await command.ExecuteScalarAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تنفيذ الاستعلام: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();
                return connection.State == ConnectionState.Open;
            }
            catch
            {
                return false;
            }
        }
    }
}
