{"profiles": {"LawFirmManagementSystem": {"commandName": "Project", "dotnetRunMessages": true, "applicationUrl": "https://localhost:7001;http://localhost:5001", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "ASPNETCORE_ENVIRONMENT": "Development"}}, "LawFirmManagementSystem (Production)": {"commandName": "Project", "dotnetRunMessages": true, "applicationUrl": "https://localhost:7001;http://localhost:5001", "environmentVariables": {"DOTNET_ENVIRONMENT": "Production", "ASPNETCORE_ENVIRONMENT": "Production"}}, "LawFirmManagementSystem (Staging)": {"commandName": "Project", "dotnetRunMessages": true, "applicationUrl": "https://localhost:7002;http://localhost:5002", "environmentVariables": {"DOTNET_ENVIRONMENT": "Staging", "ASPNETCORE_ENVIRONMENT": "Staging"}}}}