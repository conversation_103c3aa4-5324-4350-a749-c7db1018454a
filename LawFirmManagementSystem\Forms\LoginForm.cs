using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using LawFirmManagementSystem.Services.Interfaces;
using LawFirmManagementSystem.Models.Entities;

namespace LawFirmManagementSystem.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول
    /// Login form
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserService _userService;
        private readonly IAuthenticationService _authenticationService;
        private readonly ILocalizationService _localizationService;
        
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private CheckBox chkRememberMe;
        private ComboBox cmbLanguage;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblLanguage;
        private PictureBox picLogo;
        private Panel pnlMain;
        private Panel pnlLogin;
        
        private string _currentLanguage = "ar";
        private int _loginAttempts = 0;
        private const int MaxLoginAttempts = 5;

        public LoginForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _authenticationService = _serviceProvider.GetRequiredService<IAuthenticationService>();
            _localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();
            
            InitializeComponent();
            InitializeForm();
            LoadLanguages();
            ApplyLocalization();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "تسجيل الدخول - نظام إدارة مكتب المحاماة";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.Font = new Font("Tahoma", 9F, FontStyle.Regular);
            
            // Main panel
            pnlMain = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            
            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(210, 20),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.Transparent
            };
            
            // Title
            lblTitle = new Label
            {
                Text = "نظام إدارة مكتب المحاماة",
                Font = new Font("Tahoma", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 30),
                Location = new Point(50, 110)
            };
            
            // Login panel
            pnlLogin = new Panel
            {
                Size = new Size(350, 200),
                Location = new Point(75, 150),
                BackColor = Color.FromArgb(250, 250, 250),
                BorderStyle = BorderStyle.FixedSingle
            };
            
            // Username label
            lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(100, 20),
                Location = new Point(240, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            // Username textbox
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 9F),
                Size = new Size(200, 23),
                Location = new Point(20, 20),
                RightToLeft = RightToLeft.No
            };
            
            // Password label
            lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(100, 20),
                Location = new Point(240, 60),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            // Password textbox
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 9F),
                Size = new Size(200, 23),
                Location = new Point(20, 60),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };
            
            // Remember me checkbox
            chkRememberMe = new CheckBox
            {
                Text = "تذكرني",
                Font = new Font("Tahoma", 8F),
                Size = new Size(100, 20),
                Location = new Point(20, 100),
                RightToLeft = RightToLeft.Yes
            };
            
            // Language label
            lblLanguage = new Label
            {
                Text = "اللغة:",
                Font = new Font("Tahoma", 8F),
                Size = new Size(40, 20),
                Location = new Point(300, 100),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            // Language combobox
            cmbLanguage = new ComboBox
            {
                Font = new Font("Tahoma", 8F),
                Size = new Size(80, 21),
                Location = new Point(210, 100),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            
            // Login button
            btnLogin = new Button
            {
                Text = "دخول",
                Font = new Font("Tahoma", 9F, FontStyle.Bold),
                Size = new Size(80, 30),
                Location = new Point(140, 140),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            
            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Font = new Font("Tahoma", 9F),
                Size = new Size(80, 30),
                Location = new Point(230, 140),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            
            // Add controls to login panel
            pnlLogin.Controls.AddRange(new Control[] {
                lblUsername, txtUsername,
                lblPassword, txtPassword,
                chkRememberMe, lblLanguage, cmbLanguage,
                btnLogin, btnCancel
            });
            
            // Add controls to main panel
            pnlMain.Controls.AddRange(new Control[] {
                picLogo, lblTitle, pnlLogin
            });
            
            // Add main panel to form
            this.Controls.Add(pnlMain);
            
            // Event handlers
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += BtnCancel_Click;
            cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            txtUsername.KeyPress += TxtUsername_KeyPress;
            
            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            // Set form icon if available
            try
            {
                var iconPath = Path.Combine(Application.StartupPath, "Resources", "Icons", "app-icon.ico");
                if (File.Exists(iconPath))
                {
                    this.Icon = new Icon(iconPath);
                }
            }
            catch { }
            
            // Set logo if available
            try
            {
                var logoPath = Path.Combine(Application.StartupPath, "Resources", "Images", "logo.png");
                if (File.Exists(logoPath))
                {
                    picLogo.Image = Image.FromFile(logoPath);
                }
            }
            catch { }
            
            // Focus on username textbox
            txtUsername.Focus();
        }

        private void LoadLanguages()
        {
            cmbLanguage.Items.Clear();
            cmbLanguage.Items.Add(new LanguageItem("ar", "العربية"));
            cmbLanguage.Items.Add(new LanguageItem("fr", "Français"));
            
            // Set default language
            cmbLanguage.SelectedIndex = 0;
        }

        private void ApplyLocalization()
        {
            if (_currentLanguage == "fr")
            {
                this.Text = "Connexion - Système de Gestion de Cabinet d'Avocat";
                lblTitle.Text = "Système de Gestion de Cabinet d'Avocat";
                lblUsername.Text = "Nom d'utilisateur:";
                lblPassword.Text = "Mot de passe:";
                lblLanguage.Text = "Langue:";
                chkRememberMe.Text = "Se souvenir de moi";
                btnLogin.Text = "Connexion";
                btnCancel.Text = "Annuler";
                
                // Adjust RTL for French
                this.RightToLeft = RightToLeft.No;
                lblUsername.TextAlign = ContentAlignment.MiddleLeft;
                lblPassword.TextAlign = ContentAlignment.MiddleLeft;
                lblLanguage.TextAlign = ContentAlignment.MiddleLeft;
                chkRememberMe.RightToLeft = RightToLeft.No;
                
                // Reposition controls for LTR
                lblUsername.Location = new Point(20, 20);
                txtUsername.Location = new Point(130, 20);
                lblPassword.Location = new Point(20, 60);
                txtPassword.Location = new Point(130, 60);
                lblLanguage.Location = new Point(20, 100);
                cmbLanguage.Location = new Point(70, 100);
                chkRememberMe.Location = new Point(200, 100);
            }
            else
            {
                this.Text = "تسجيل الدخول - نظام إدارة مكتب المحاماة";
                lblTitle.Text = "نظام إدارة مكتب المحاماة";
                lblUsername.Text = "اسم المستخدم:";
                lblPassword.Text = "كلمة المرور:";
                lblLanguage.Text = "اللغة:";
                chkRememberMe.Text = "تذكرني";
                btnLogin.Text = "دخول";
                btnCancel.Text = "إلغاء";
                
                // Adjust RTL for Arabic
                this.RightToLeft = RightToLeft.Yes;
                lblUsername.TextAlign = ContentAlignment.MiddleRight;
                lblPassword.TextAlign = ContentAlignment.MiddleRight;
                lblLanguage.TextAlign = ContentAlignment.MiddleRight;
                chkRememberMe.RightToLeft = RightToLeft.Yes;
                
                // Reposition controls for RTL
                lblUsername.Location = new Point(240, 20);
                txtUsername.Location = new Point(20, 20);
                lblPassword.Location = new Point(240, 60);
                txtPassword.Location = new Point(20, 60);
                lblLanguage.Location = new Point(300, 100);
                cmbLanguage.Location = new Point(210, 100);
                chkRememberMe.Location = new Point(20, 100);
            }
        }

        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbLanguage.SelectedItem is LanguageItem selectedLanguage)
            {
                _currentLanguage = selectedLanguage.Code;
                ApplyLocalization();
            }
        }

        private async void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                await PerformLoginAsync();
            }
        }

        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                txtPassword.Focus();
            }
        }

        private async Task PerformLoginAsync()
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    ShowMessage(_currentLanguage == "fr" ? "Veuillez saisir le nom d'utilisateur" : "يرجى إدخال اسم المستخدم", MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    ShowMessage(_currentLanguage == "fr" ? "Veuillez saisir le mot de passe" : "يرجى إدخال كلمة المرور", MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                // Disable controls during login
                SetControlsEnabled(false);
                
                // Show loading
                btnLogin.Text = _currentLanguage == "fr" ? "Connexion..." : "جاري الدخول...";

                // Perform authentication
                var user = await _userService.AuthenticateAsync(txtUsername.Text.Trim(), txtPassword.Text);

                if (user != null)
                {
                    // Create session
                    var session = await _userService.CreateSessionAsync(
                        user.Id, 
                        GetClientIPAddress(), 
                        Environment.MachineName);

                    // Store session info
                    SessionManager.CurrentUser = user;
                    SessionManager.CurrentSession = session;
                    SessionManager.CurrentLanguage = _currentLanguage;

                    // Update user preferences
                    await _userService.UpdatePreferencesAsync(user.Id, _currentLanguage, user.Theme, user.SessionTimeout);

                    // Show main form
                    this.Hide();
                    var mainForm = new MainForm(_serviceProvider);
                    mainForm.FormClosed += (s, e) => Application.Exit();
                    mainForm.Show();
                }
                else
                {
                    _loginAttempts++;
                    
                    if (_loginAttempts >= MaxLoginAttempts)
                    {
                        ShowMessage(
                            _currentLanguage == "fr" 
                                ? "Trop de tentatives de connexion échouées. L'application va se fermer."
                                : "عدد كبير من محاولات تسجيل الدخول الفاشلة. سيتم إغلاق التطبيق.",
                            MessageBoxIcon.Error);
                        Application.Exit();
                        return;
                    }

                    var remainingAttempts = MaxLoginAttempts - _loginAttempts;
                    ShowMessage(
                        _currentLanguage == "fr" 
                            ? $"Nom d'utilisateur ou mot de passe incorrect. {remainingAttempts} tentatives restantes."
                            : $"اسم المستخدم أو كلمة المرور غير صحيحة. {remainingAttempts} محاولات متبقية.",
                        MessageBoxIcon.Error);
                    
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowMessage(
                    _currentLanguage == "fr" 
                        ? $"Erreur lors de la connexion: {ex.Message}"
                        : $"خطأ في تسجيل الدخول: {ex.Message}",
                    MessageBoxIcon.Error);
            }
            finally
            {
                SetControlsEnabled(true);
                btnLogin.Text = _currentLanguage == "fr" ? "Connexion" : "دخول";
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnCancel.Enabled = enabled;
            cmbLanguage.Enabled = enabled;
            chkRememberMe.Enabled = enabled;
        }

        private void ShowMessage(string message, MessageBoxIcon icon)
        {
            var title = _currentLanguage == "fr" ? "Information" : "معلومات";
            if (icon == MessageBoxIcon.Error)
                title = _currentLanguage == "fr" ? "Erreur" : "خطأ";
            else if (icon == MessageBoxIcon.Warning)
                title = _currentLanguage == "fr" ? "Avertissement" : "تحذير";

            MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
        }

        private string GetClientIPAddress()
        {
            try
            {
                return System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
                    .AddressList
                    .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    ?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        private class LanguageItem
        {
            public string Code { get; }
            public string Name { get; }

            public LanguageItem(string code, string name)
            {
                Code = code;
                Name = name;
            }

            public override string ToString() => Name;
        }
    }

    /// <summary>
    /// مدير الجلسة
    /// Session manager
    /// </summary>
    public static class SessionManager
    {
        public static User? CurrentUser { get; set; }
        public static UserSession? CurrentSession { get; set; }
        public static string CurrentLanguage { get; set; } = "ar";
    }
}
