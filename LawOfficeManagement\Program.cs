using System;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using LawOfficeManagement.Forms;
using LawOfficeManagement.Services;
using LawOfficeManagement.Localization;

namespace LawOfficeManagement;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        // إعداد التطبيق
        ApplicationConfiguration.Initialize();

        // إعداد معالج الأخطاء العامة
        Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
        Application.ThreadException += Application_ThreadException;
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

        try
        {
            // تهيئة قاعدة البيانات
            var databaseService = DatabaseService.Instance;
            var isDbReady = await databaseService.InitializeDatabaseAsync();

            if (!isDbReady)
            {
                MessageBox.Show("فشل في الاتصال بقاعدة البيانات. يرجى التحقق من الإعدادات.",
                    "خطأ في قاعدة البيانات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                return;
            }

            // تهيئة نظام الترجمة
            var languageManager = LanguageManager.Instance;
            var savedLanguage = Properties.Settings.Default.Language;
            if (!string.IsNullOrEmpty(savedLanguage))
            {
                languageManager.SetLanguage(savedLanguage);
            }

            // تشغيل نافذة تسجيل الدخول
            Application.Run(new LoginForm());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}",
                "خطأ",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
    {
        LogException(e.Exception);

        MessageBox.Show($"حدث خطأ غير متوقع: {e.Exception.Message}",
            "خطأ",
            MessageBoxButtons.OK,
            MessageBoxIcon.Error);
    }

    private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception ex)
        {
            LogException(ex);

            MessageBox.Show($"خطأ حرج في التطبيق: {ex.Message}",
                "خطأ حرج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private static async void LogException(Exception ex)
    {
        try
        {
            var errorLogService = new ErrorLogService();
            await errorLogService.LogErrorAsync("Application", ex);
        }
        catch
        {
            // تجاهل أخطاء التسجيل
        }
    }
}