namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التشفير
    /// Encryption service interface
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// تشفير النص
        /// Encrypt text
        /// </summary>
        string Encrypt(string plainText);

        /// <summary>
        /// فك تشفير النص
        /// Decrypt text
        /// </summary>
        string Decrypt(string cipherText);

        /// <summary>
        /// تشفير الملف
        /// Encrypt file
        /// </summary>
        Task<byte[]> EncryptFileAsync(byte[] fileData);

        /// <summary>
        /// فك تشفير الملف
        /// Decrypt file
        /// </summary>
        Task<byte[]> DecryptFileAsync(byte[] encryptedData);

        /// <summary>
        /// إنشاء هاش للبيانات
        /// Generate hash for data
        /// </summary>
        string GenerateHash(string data);

        /// <summary>
        /// التحقق من الهاش
        /// Verify hash
        /// </summary>
        bool VerifyHash(string data, string hash);
    }
}
