using System;
using System.Windows.Forms;
using LawOfficeManagement.Localization;

namespace LawOfficeManagement.Forms
{
    public partial class CaseManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public CaseManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "إدارة الملفات القانونية";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("CaseManagement");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
    
    public partial class ClientManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public ClientManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "إدارة العملاء";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("ClientManagement");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
    
    public partial class HearingManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public HearingManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "إدارة الجلسات";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("HearingManagement");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
    
    public partial class AppointmentManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public AppointmentManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "إدارة المواعيد";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("AppointmentManagement");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
    
    public partial class FinancialManagementForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public FinancialManagementForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "الإدارة المالية";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("FinancialManagement");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
    
    public partial class ReportsForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public ReportsForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "إدارة التقارير";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("ReportsManagement");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
    
    public partial class SettingsForm : Form
    {
        private readonly LanguageManager _languageManager;
        
        public SettingsForm()
        {
            InitializeComponent();
            _languageManager = LanguageManager.Instance;
            InitializeForm();
        }
        
        private void InitializeComponent()
        {
            this.Text = "الإعدادات العامة";
            this.Size = new System.Drawing.Size(800, 600);
            this.WindowState = FormWindowState.Maximized;
        }
        
        private void InitializeForm()
        {
            this.Text = _languageManager.GetString("GeneralSettings");
            this.WindowState = FormWindowState.Maximized;
            _languageManager.ApplyLanguageToForm(this);
        }
    }
}
