# Use the official .NET 8.0 runtime as base image
FROM mcr.microsoft.com/dotnet/runtime:8.0-windowsservercore-ltsc2022 AS base
WORKDIR /app

# Use the official .NET 8.0 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0-windowsservercore-ltsc2022 AS build
WORKDIR /src

# Copy project file and restore dependencies
COPY ["LawFirmManagementSystem.csproj", "."]
RUN dotnet restore "LawFirmManagementSystem.csproj"

# Copy all source files
COPY . .

# Build the application
RUN dotnet build "LawFirmManagementSystem.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "LawFirmManagementSystem.csproj" -c Release -o /app/publish

# Final stage
FROM base AS final
WORKDIR /app

# Install required Windows features for WinForms
RUN powershell -Command \
    Add-WindowsFeature NET-Framework-45-Features; \
    Add-WindowsFeature NET-Framework-45-Core; \
    Add-WindowsFeature NET-Framework-45-ASPNET

# Copy published application
COPY --from=publish /app/publish .

# Create required directories
RUN mkdir C:\app\Logs && \
    mkdir C:\app\Documents && \
    mkdir C:\app\Backups && \
    mkdir C:\app\Reports && \
    mkdir C:\app\Templates

# Set environment variables
ENV DOTNET_ENVIRONMENT=Production
ENV ASPNETCORE_ENVIRONMENT=Production

# Expose any required ports (if needed for future web components)
# EXPOSE 80
# EXPOSE 443

# Set the entry point
ENTRYPOINT ["dotnet", "LawFirmManagementSystem.dll"]

# Health check (optional)
# HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
#   CMD powershell -command "try { \$response = Invoke-WebRequest -Uri http://localhost/health -UseBasicParsing; if (\$response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }"

# Labels for metadata
LABEL maintainer="Law Firm Management Solutions <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="نظام إدارة مكتب المحاماة / Law Firm Management System"
LABEL vendor="Law Firm Management Solutions"
