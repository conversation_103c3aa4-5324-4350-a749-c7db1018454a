@echo off
echo ========================================
echo إصلاح أخطاء البناء
echo Fixing Build Errors
echo ========================================
echo.

echo تنظيف المشروع...
echo Cleaning project...
dotnet clean

echo استعادة الحزم...
echo Restoring packages...
dotnet restore

echo محاولة البناء...
echo Attempting build...
dotnet build --verbosity minimal

if %errorlevel% equ 0 (
    echo ========================================
    echo تم البناء بنجاح!
    echo Build successful!
    echo ========================================
    echo.
    echo يمكنك الآن تشغيل التطبيق:
    echo You can now run the application:
    echo dotnet run
    echo.
) else (
    echo ========================================
    echo فشل البناء - يرجى مراجعة الأخطاء أعلاه
    echo Build failed - please review errors above
    echo ========================================
)

pause
