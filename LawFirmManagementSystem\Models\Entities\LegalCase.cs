using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الملف القانوني/القضية
    /// Legal case entity
    /// </summary>
    [Table("LegalCases")]
    public class LegalCase : BaseEntity
    {
        [Required]
        [StringLength(50)]
        [Column("CaseReference")]
        public string CaseReference { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column("OfficeReference")]
        public string OfficeReference { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Column("CaseTitle")]
        public string CaseTitle { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("CaseTitleFr")]
        public string? CaseTitleFr { get; set; }

        [Required]
        [Column("CaseTypeId")]
        public int CaseTypeId { get; set; }

        [Required]
        [Column("ClientId")]
        public int ClientId { get; set; }

        [Column("CourtId")]
        public int? CourtId { get; set; }

        [StringLength(100)]
        [Column("CourtCaseNumber")]
        public string? CourtCaseNumber { get; set; }

        [StringLength(50)]
        [Column("CourtDegree")]
        public string? CourtDegree { get; set; } // ابتدائية، استئناف، نقض

        [StringLength(50)]
        [Column("CourtDegreeFr")]
        public string? CourtDegreeFr { get; set; }

        [StringLength(100)]
        [Column("Jurisdiction")]
        public string? Jurisdiction { get; set; }

        [StringLength(100)]
        [Column("JurisdictionFr")]
        public string? JurisdictionFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("CaseStatus")]
        public string CaseStatus { get; set; } = "جاري"; // جاري، مغلق، معلق، مؤجل

        [StringLength(50)]
        [Column("CaseStatusFr")]
        public string? CaseStatusFr { get; set; }

        [Required]
        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [Required]
        [Column("OpenDate")]
        public DateTime OpenDate { get; set; } = DateTime.Now;

        [Column("CloseDate")]
        public DateTime? CloseDate { get; set; }

        [Column("ExpectedCloseDate")]
        public DateTime? ExpectedCloseDate { get; set; }

        [StringLength(2000)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(2000)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [StringLength(2000)]
        [Column("InternalNotes")]
        public string? InternalNotes { get; set; }

        [StringLength(2000)]
        [Column("InternalNotesFr")]
        public string? InternalNotesFr { get; set; }

        [Column("AssignedLawyerId")]
        public int? AssignedLawyerId { get; set; }

        [Column("SecondaryLawyerId")]
        public int? SecondaryLawyerId { get; set; }

        [StringLength(100)]
        [Column("LegalBasis")]
        public string? LegalBasis { get; set; }

        [StringLength(100)]
        [Column("LegalBasisFr")]
        public string? LegalBasisFr { get; set; }

        [Column("EstimatedValue")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? EstimatedValue { get; set; }

        [Column("ClaimedAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ClaimedAmount { get; set; }

        [Column("AwardedAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AwardedAmount { get; set; }

        [Column("TotalFees")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalFees { get; set; } = 0;

        [Column("PaidFees")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidFees { get; set; } = 0;

        [Column("RemainingFees")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingFees { get; set; } = 0;

        [Column("TotalExpenses")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalExpenses { get; set; } = 0;

        [Column("IsContingencyFee")]
        public bool IsContingencyFee { get; set; } = false;

        [Column("ContingencyPercentage")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? ContingencyPercentage { get; set; }

        [Column("IsSuccessful")]
        public bool? IsSuccessful { get; set; }

        [StringLength(2000)]
        [Column("Outcome")]
        public string? Outcome { get; set; }

        [StringLength(2000)]
        [Column("OutcomeFr")]
        public string? OutcomeFr { get; set; }

        [Column("NextHearingDate")]
        public DateTime? NextHearingDate { get; set; }

        [Column("NextAppointmentDate")]
        public DateTime? NextAppointmentDate { get; set; }

        [Column("StatuteOfLimitations")]
        public DateTime? StatuteOfLimitations { get; set; }

        [Column("IsUrgent")]
        public bool IsUrgent { get; set; } = false;

        [Column("IsConfidential")]
        public bool IsConfidential { get; set; } = false;

        [Column("IsProBono")]
        public bool IsProBono { get; set; } = false;

        [StringLength(50)]
        [Column("ConflictCheck")]
        public string ConflictCheck { get; set; } = "Pending"; // Pending, Cleared, Conflict

        [Column("ConflictCheckDate")]
        public DateTime? ConflictCheckDate { get; set; }

        [Column("ConflictCheckBy")]
        public int? ConflictCheckBy { get; set; }

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [Column("LastActivityDate")]
        public DateTime? LastActivityDate { get; set; }

        [StringLength(2000)]
        [Column("LastActivity")]
        public string? LastActivity { get; set; }

        [StringLength(2000)]
        [Column("LastActivityFr")]
        public string? LastActivityFr { get; set; }

        // Navigation Properties
        [ForeignKey("CaseTypeId")]
        public virtual CaseType CaseType { get; set; } = null!;

        [ForeignKey("ClientId")]
        public virtual Client Client { get; set; } = null!;

        [ForeignKey("CourtId")]
        public virtual Court? Court { get; set; }

        [ForeignKey("AssignedLawyerId")]
        public virtual User? AssignedLawyer { get; set; }

        [ForeignKey("SecondaryLawyerId")]
        public virtual User? SecondaryLawyer { get; set; }

        public virtual ICollection<Opponent> Opponents { get; set; } = new List<Opponent>();
        public virtual ICollection<Hearing> Hearings { get; set; } = new List<Hearing>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
        public virtual ICollection<CaseExpense> Expenses { get; set; } = new List<CaseExpense>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<CaseNote> CaseNotes { get; set; } = new List<CaseNote>();
        public virtual ICollection<CaseTask> Tasks { get; set; } = new List<CaseTask>();
        public virtual ICollection<CaseTimeline> Timeline { get; set; } = new List<CaseTimeline>();

        /// <summary>
        /// الحصول على عنوان القضية حسب اللغة
        /// Get case title by language
        /// </summary>
        public string GetTitle(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(CaseTitleFr) 
                ? CaseTitleFr 
                : CaseTitle;
        }

        /// <summary>
        /// الحصول على وصف القضية حسب اللغة
        /// Get case description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// تحديث الرسوم المتبقية
        /// Update remaining fees
        /// </summary>
        public void UpdateRemainingFees()
        {
            RemainingFees = TotalFees - PaidFees;
        }

        /// <summary>
        /// إضافة دفعة
        /// Add payment
        /// </summary>
        public void AddPayment(decimal amount)
        {
            PaidFees += amount;
            UpdateRemainingFees();
        }

        /// <summary>
        /// إضافة مصروف
        /// Add expense
        /// </summary>
        public void AddExpense(decimal amount)
        {
            TotalExpenses += amount;
        }

        /// <summary>
        /// إغلاق القضية
        /// Close case
        /// </summary>
        public void CloseCase(string outcome, bool isSuccessful, int? closedBy = null)
        {
            CaseStatus = "مغلق";
            CaseStatusFr = "Fermé";
            CloseDate = DateTime.Now;
            Outcome = outcome;
            IsSuccessful = isSuccessful;
            UpdateModificationInfo(closedBy);
        }

        /// <summary>
        /// إعادة فتح القضية
        /// Reopen case
        /// </summary>
        public void ReopenCase(int? reopenedBy = null)
        {
            CaseStatus = "جاري";
            CaseStatusFr = "En cours";
            CloseDate = null;
            Outcome = null;
            IsSuccessful = null;
            UpdateModificationInfo(reopenedBy);
        }

        /// <summary>
        /// تحديث آخر نشاط
        /// Update last activity
        /// </summary>
        public void UpdateLastActivity(string activity, string? activityFr = null)
        {
            LastActivityDate = DateTime.Now;
            LastActivity = activity;
            LastActivityFr = activityFr;
        }

        /// <summary>
        /// التحقق من انتهاء مدة التقادم
        /// Check if statute of limitations is expired
        /// </summary>
        public bool IsStatuteOfLimitationsExpired()
        {
            return StatuteOfLimitations.HasValue && StatuteOfLimitations.Value <= DateTime.Now;
        }

        /// <summary>
        /// التحقق من وجود جلسة قادمة
        /// Check if there's an upcoming hearing
        /// </summary>
        public bool HasUpcomingHearing()
        {
            return NextHearingDate.HasValue && NextHearingDate.Value > DateTime.Now;
        }
    }
}
