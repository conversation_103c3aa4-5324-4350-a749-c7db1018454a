namespace LawOfficeManagement.Forms
{
    partial class ForgotPasswordForm
    {
        private System.ComponentModel.IContainer components = null;
        private Panel pnlMain;
        private Label lblTitle;
        private Label lblInstruction;
        private Label lblUsername;
        private TextBox txtUsername;
        private Button btnSendOTP;
        private Panel pnlOTPVerification;
        private Label lblOTP;
        private TextBox txtOTP;
        private Label lblNewPassword;
        private TextBox txtNewPassword;
        private Label lblConfirmPassword;
        private TextBox txtConfirmPassword;
        private Button btnResetPassword;
        private Button btnCancel;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.pnlMain = new Panel();
            this.lblTitle = new Label();
            this.lblInstruction = new Label();
            this.lblUsername = new Label();
            this.txtUsername = new TextBox();
            this.btnSendOTP = new Button();
            this.pnlOTPVerification = new Panel();
            this.lblOTP = new Label();
            this.txtOTP = new TextBox();
            this.lblNewPassword = new Label();
            this.txtNewPassword = new TextBox();
            this.lblConfirmPassword = new Label();
            this.txtConfirmPassword = new TextBox();
            this.btnResetPassword = new Button();
            this.btnCancel = new Button();
            
            this.pnlMain.SuspendLayout();
            this.pnlOTPVerification.SuspendLayout();
            this.SuspendLayout();

            // 
            // pnlMain
            // 
            this.pnlMain.BackColor = Color.White;
            this.pnlMain.Controls.Add(this.lblTitle);
            this.pnlMain.Controls.Add(this.lblInstruction);
            this.pnlMain.Controls.Add(this.lblUsername);
            this.pnlMain.Controls.Add(this.txtUsername);
            this.pnlMain.Controls.Add(this.btnSendOTP);
            this.pnlMain.Controls.Add(this.pnlOTPVerification);
            this.pnlMain.Controls.Add(this.btnCancel);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Padding = new Padding(30);
            this.pnlMain.Location = new Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new Size(400, 500);
            this.pnlMain.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(25, 42, 86);
            this.lblTitle.Location = new Point(30, 30);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(340, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "استعادة كلمة المرور";
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // lblInstruction
            // 
            this.lblInstruction.Font = new Font("Segoe UI", 9F);
            this.lblInstruction.ForeColor = Color.FromArgb(108, 117, 125);
            this.lblInstruction.Location = new Point(30, 70);
            this.lblInstruction.Name = "lblInstruction";
            this.lblInstruction.Size = new Size(340, 40);
            this.lblInstruction.TabIndex = 1;
            this.lblInstruction.Text = "أدخل اسم المستخدم الخاص بك وسنرسل لك رمز التحقق لإعادة تعيين كلمة المرور";
            this.lblInstruction.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // lblUsername
            // 
            this.lblUsername.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblUsername.Location = new Point(30, 130);
            this.lblUsername.Name = "lblUsername";
            this.lblUsername.Size = new Size(340, 25);
            this.lblUsername.TabIndex = 2;
            this.lblUsername.Text = "اسم المستخدم";

            // 
            // txtUsername
            // 
            this.txtUsername.Font = new Font("Segoe UI", 11F);
            this.txtUsername.Location = new Point(30, 160);
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Size = new Size(340, 32);
            this.txtUsername.TabIndex = 3;

            // 
            // btnSendOTP
            // 
            this.btnSendOTP.BackColor = Color.FromArgb(25, 42, 86);
            this.btnSendOTP.FlatStyle = FlatStyle.Flat;
            this.btnSendOTP.FlatAppearance.BorderSize = 0;
            this.btnSendOTP.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSendOTP.ForeColor = Color.White;
            this.btnSendOTP.Location = new Point(30, 210);
            this.btnSendOTP.Name = "btnSendOTP";
            this.btnSendOTP.Size = new Size(340, 40);
            this.btnSendOTP.TabIndex = 4;
            this.btnSendOTP.Text = "إرسال رمز التحقق";
            this.btnSendOTP.UseVisualStyleBackColor = false;
            this.btnSendOTP.Click += new EventHandler(this.btnSendOTP_Click);

            // 
            // pnlOTPVerification
            // 
            this.pnlOTPVerification.Controls.Add(this.lblOTP);
            this.pnlOTPVerification.Controls.Add(this.txtOTP);
            this.pnlOTPVerification.Controls.Add(this.lblNewPassword);
            this.pnlOTPVerification.Controls.Add(this.txtNewPassword);
            this.pnlOTPVerification.Controls.Add(this.lblConfirmPassword);
            this.pnlOTPVerification.Controls.Add(this.txtConfirmPassword);
            this.pnlOTPVerification.Controls.Add(this.btnResetPassword);
            this.pnlOTPVerification.Location = new Point(30, 270);
            this.pnlOTPVerification.Name = "pnlOTPVerification";
            this.pnlOTPVerification.Size = new Size(340, 180);
            this.pnlOTPVerification.TabIndex = 5;
            this.pnlOTPVerification.Visible = false;

            // 
            // lblOTP
            // 
            this.lblOTP.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblOTP.Location = new Point(0, 0);
            this.lblOTP.Name = "lblOTP";
            this.lblOTP.Size = new Size(340, 25);
            this.lblOTP.TabIndex = 0;
            this.lblOTP.Text = "رمز التحقق";

            // 
            // txtOTP
            // 
            this.txtOTP.Font = new Font("Segoe UI", 11F);
            this.txtOTP.Location = new Point(0, 30);
            this.txtOTP.Name = "txtOTP";
            this.txtOTP.Size = new Size(340, 32);
            this.txtOTP.TabIndex = 1;
            this.txtOTP.TextAlign = HorizontalAlignment.Center;

            // 
            // lblNewPassword
            // 
            this.lblNewPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblNewPassword.Location = new Point(0, 70);
            this.lblNewPassword.Name = "lblNewPassword";
            this.lblNewPassword.Size = new Size(340, 25);
            this.lblNewPassword.TabIndex = 2;
            this.lblNewPassword.Text = "كلمة المرور الجديدة";

            // 
            // txtNewPassword
            // 
            this.txtNewPassword.Font = new Font("Segoe UI", 11F);
            this.txtNewPassword.Location = new Point(0, 100);
            this.txtNewPassword.Name = "txtNewPassword";
            this.txtNewPassword.Size = new Size(340, 32);
            this.txtNewPassword.TabIndex = 3;
            this.txtNewPassword.UseSystemPasswordChar = true;

            // 
            // lblConfirmPassword
            // 
            this.lblConfirmPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblConfirmPassword.Location = new Point(170, 70);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new Size(170, 25);
            this.lblConfirmPassword.TabIndex = 4;
            this.lblConfirmPassword.Text = "تأكيد كلمة المرور";

            // 
            // txtConfirmPassword
            // 
            this.txtConfirmPassword.Font = new Font("Segoe UI", 11F);
            this.txtConfirmPassword.Location = new Point(170, 100);
            this.txtConfirmPassword.Name = "txtConfirmPassword";
            this.txtConfirmPassword.Size = new Size(170, 32);
            this.txtConfirmPassword.TabIndex = 5;
            this.txtConfirmPassword.UseSystemPasswordChar = true;

            // 
            // btnResetPassword
            // 
            this.btnResetPassword.BackColor = Color.FromArgb(218, 165, 32);
            this.btnResetPassword.FlatStyle = FlatStyle.Flat;
            this.btnResetPassword.FlatAppearance.BorderSize = 0;
            this.btnResetPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnResetPassword.ForeColor = Color.White;
            this.btnResetPassword.Location = new Point(0, 140);
            this.btnResetPassword.Name = "btnResetPassword";
            this.btnResetPassword.Size = new Size(340, 40);
            this.btnResetPassword.TabIndex = 6;
            this.btnResetPassword.Text = "إعادة تعيين كلمة المرور";
            this.btnResetPassword.UseVisualStyleBackColor = false;
            this.btnResetPassword.Click += new EventHandler(this.btnResetPassword_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.Font = new Font("Segoe UI", 10F);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(30, 460);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(340, 35);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // 
            // ForgotPasswordForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(400, 500);
            this.Controls.Add(this.pnlMain);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ForgotPasswordForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "استعادة كلمة المرور";
            
            this.pnlMain.ResumeLayout(false);
            this.pnlMain.PerformLayout();
            this.pnlOTPVerification.ResumeLayout(false);
            this.pnlOTPVerification.PerformLayout();
            this.ResumeLayout(false);
        }
    }
}
