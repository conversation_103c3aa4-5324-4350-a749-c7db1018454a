using LawFirmManagementSystem.Data;
using LawFirmManagementSystem.Models.Entities;
using LawFirmManagementSystem.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة المصادقة
    /// Authentication service implementation
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly LawFirmDbContext _context;
        private readonly ILogger<AuthenticationService> _logger;
        private readonly IAuditService _auditService;

        public AuthenticationService(
            LawFirmDbContext context,
            ILogger<AuthenticationService> logger,
            IAuditService auditService)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
        }

        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    _logger.LogWarning("Authentication failed: Username or password is empty");
                    return null;
                }

                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("Authentication failed: User {Username} not found or inactive", username);
                    await _auditService.LogAsync(0, "Login", "User", null, $"Failed login attempt for username: {username}", severity: "Warning");
                    return null;
                }

                // Check if user is locked
                if (user.IsLocked && user.LockoutEnd > DateTime.Now)
                {
                    _logger.LogWarning("Authentication failed: User {Username} is locked until {LockoutEnd}", username, user.LockoutEnd);
                    await _auditService.LogAsync(user.Id, "Login", "User", user.Id, "Login attempt while account is locked", severity: "Warning");
                    return null;
                }

                // Verify password
                if (!VerifyPassword(password, user.PasswordHash))
                {
                    _logger.LogWarning("Authentication failed: Invalid password for user {Username}", username);
                    
                    // Increment failed login attempts
                    user.FailedLoginAttempts++;
                    user.LastFailedLogin = DateTime.Now;

                    // Lock account if too many failed attempts
                    if (user.FailedLoginAttempts >= 5)
                    {
                        user.IsLocked = true;
                        user.LockoutEnd = DateTime.Now.AddMinutes(15);
                        _logger.LogWarning("User {Username} locked due to too many failed login attempts", username);
                    }

                    await _context.SaveChangesAsync();
                    await _auditService.LogAsync(user.Id, "Login", "User", user.Id, "Failed login attempt - invalid password", severity: "Warning");
                    return null;
                }

                // Reset failed login attempts on successful login
                user.FailedLoginAttempts = 0;
                user.LastLogin = DateTime.Now;
                user.IsLocked = false;
                user.LockoutEnd = null;

                await _context.SaveChangesAsync();

                _logger.LogInformation("User {Username} authenticated successfully", username);
                await _auditService.LogAsync(user.Id, "Login", "User", user.Id, "Successful login");

                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for user {Username}", username);
                return null;
            }
        }

        public async Task<bool> LogoutAsync(string sessionToken)
        {
            try
            {
                var session = await _context.UserSessions
                    .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

                if (session != null)
                {
                    session.EndSession();
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("User {UserId} logged out successfully", session.UserId);
                    await _auditService.LogAsync(session.UserId, "Logout", "User", session.UserId, "User logged out");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout for session {SessionToken}", sessionToken);
                return false;
            }
        }

        public async Task<bool> ValidateSessionAsync(string sessionToken)
        {
            try
            {
                var session = await _context.UserSessions
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

                if (session == null || !session.IsActive() || !session.User.IsActive)
                {
                    return false;
                }

                // Update last activity
                session.UpdateActivity();
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating session {SessionToken}", sessionToken);
                return false;
            }
        }

        public async Task<bool> RefreshSessionAsync(string sessionToken)
        {
            try
            {
                var session = await _context.UserSessions
                    .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

                if (session != null && session.IsActive())
                {
                    session.ExtendSession();
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing session {SessionToken}", sessionToken);
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("Change password failed: User {UserId} not found", userId);
                    return false;
                }

                // Verify current password
                if (!VerifyPassword(currentPassword, user.PasswordHash))
                {
                    _logger.LogWarning("Change password failed: Invalid current password for user {UserId}", userId);
                    await _auditService.LogAsync(userId, "ChangePassword", "User", userId, "Failed password change - invalid current password", severity: "Warning");
                    return false;
                }

                // Validate new password strength
                if (!ValidatePasswordStrength(newPassword))
                {
                    _logger.LogWarning("Change password failed: New password does not meet strength requirements for user {UserId}", userId);
                    return false;
                }

                // Update password
                user.PasswordHash = HashPassword(newPassword);
                user.PasswordChanged = DateTime.Now;
                user.MustChangePassword = false;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Password changed successfully for user {UserId}", userId);
                await _auditService.LogAsync(userId, "ChangePassword", "User", userId, "Password changed successfully");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(string email)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == email && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("Password reset failed: User with email {Email} not found", email);
                    return false;
                }

                // Generate temporary password
                var tempPassword = GenerateTemporaryPassword();
                user.PasswordHash = HashPassword(tempPassword);
                user.MustChangePassword = true;
                user.PasswordChanged = DateTime.Now;

                await _context.SaveChangesAsync();

                // TODO: Send email with temporary password
                // await _emailService.SendPasswordResetEmailAsync(user.Email, tempPassword);

                _logger.LogInformation("Password reset successfully for user {UserId}", user.Id);
                await _auditService.LogAsync(user.Id, "ResetPassword", "User", user.Id, "Password reset requested");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password for email {Email}", email);
                return false;
            }
        }

        public bool ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return false;

            // Minimum 8 characters
            if (password.Length < 8)
                return false;

            // At least one uppercase letter
            if (!Regex.IsMatch(password, @"[A-Z]"))
                return false;

            // At least one lowercase letter
            if (!Regex.IsMatch(password, @"[a-z]"))
                return false;

            // At least one digit
            if (!Regex.IsMatch(password, @"\d"))
                return false;

            // At least one special character
            if (!Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
                return false;

            return true;
        }

        public string HashPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            using var sha256 = SHA256.Create();
            var salt = GenerateSalt();
            var saltedPassword = password + salt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            var hashedPassword = Convert.ToBase64String(hashedBytes);
            
            return $"{salt}:{hashedPassword}";
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hashedPassword))
                return false;

            try
            {
                var parts = hashedPassword.Split(':');
                if (parts.Length != 2)
                    return false;

                var salt = parts[0];
                var hash = parts[1];

                using var sha256 = SHA256.Create();
                var saltedPassword = password + salt;
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                var computedHash = Convert.ToBase64String(hashedBytes);

                return hash == computedHash;
            }
            catch
            {
                return false;
            }
        }

        private string GenerateSalt()
        {
            var saltBytes = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(saltBytes);
            return Convert.ToBase64String(saltBytes);
        }

        private string GenerateTemporaryPassword()
        {
            const string chars = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz0123456789!@#$%^&*";
            var random = new Random();
            var password = new StringBuilder();

            // Ensure at least one character from each required category
            password.Append(chars[random.Next(0, 26)]); // Uppercase
            password.Append(chars[random.Next(26, 52)]); // Lowercase
            password.Append(chars[random.Next(52, 62)]); // Digit
            password.Append(chars[random.Next(62, chars.Length)]); // Special

            // Fill the rest randomly
            for (int i = 4; i < 12; i++)
            {
                password.Append(chars[random.Next(chars.Length)]);
            }

            // Shuffle the password
            var passwordArray = password.ToString().ToCharArray();
            for (int i = passwordArray.Length - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                (passwordArray[i], passwordArray[j]) = (passwordArray[j], passwordArray[i]);
            }

            return new string(passwordArray);
        }
    }
}
