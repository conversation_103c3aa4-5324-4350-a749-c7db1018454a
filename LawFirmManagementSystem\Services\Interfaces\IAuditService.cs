using LawFirmManagementSystem.Models.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التدقيق
    /// Audit service interface
    /// </summary>
    public interface IAuditService
    {
        /// <summary>
        /// تسجيل حدث تدقيق
        /// Log audit event
        /// </summary>
        Task LogAsync(int userId, string action, string entityType, int? entityId = null, 
            string? description = null, string? oldValues = null, string? newValues = null, 
            string severity = "Information");

        /// <summary>
        /// تسجيل حدث تدقيق مع تفاصيل إضافية
        /// Log audit event with additional details
        /// </summary>
        Task LogAsync(AuditLog auditLog);

        /// <summary>
        /// الحصول على سجلات التدقيق للمستخدم
        /// Get audit logs for user
        /// </summary>
        Task<IEnumerable<AuditLog>> GetUserAuditLogsAsync(int userId, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// الحصول على سجلات التدقيق للكيان
        /// Get audit logs for entity
        /// </summary>
        Task<IEnumerable<AuditLog>> GetEntityAuditLogsAsync(string entityType, int entityId, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// الحصول على سجلات التدقيق حسب الفترة الزمنية
        /// Get audit logs by date range
        /// </summary>
        Task<IEnumerable<AuditLog>> GetAuditLogsByDateRangeAsync(DateTime fromDate, DateTime toDate, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// الحصول على سجلات التدقيق حسب النوع
        /// Get audit logs by action type
        /// </summary>
        Task<IEnumerable<AuditLog>> GetAuditLogsByActionAsync(string action, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// البحث في سجلات التدقيق
        /// Search audit logs
        /// </summary>
        Task<IEnumerable<AuditLog>> SearchAuditLogsAsync(string searchTerm, int pageNumber = 1, int pageSize = 50);

        /// <summary>
        /// الحصول على إحصائيات التدقيق
        /// Get audit statistics
        /// </summary>
        Task<AuditStatistics> GetAuditStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// تنظيف سجلات التدقيق القديمة
        /// Clean up old audit logs
        /// </summary>
        Task<int> CleanupOldLogsAsync(int retentionDays = 365);

        /// <summary>
        /// تصدير سجلات التدقيق
        /// Export audit logs
        /// </summary>
        Task<byte[]> ExportAuditLogsAsync(DateTime fromDate, DateTime toDate, string format = "Excel");

        /// <summary>
        /// مراجعة سجل التدقيق
        /// Review audit log
        /// </summary>
        Task<bool> ReviewAuditLogAsync(int auditLogId, int reviewedBy, string? notes = null);

        /// <summary>
        /// أرشفة سجلات التدقيق
        /// Archive audit logs
        /// </summary>
        Task<bool> ArchiveAuditLogsAsync(DateTime beforeDate);
    }

    /// <summary>
    /// إحصائيات التدقيق
    /// Audit statistics
    /// </summary>
    public class AuditStatistics
    {
        public int TotalLogs { get; set; }
        public int TotalUsers { get; set; }
        public int TotalActions { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int InfoCount { get; set; }
        public Dictionary<string, int> ActionCounts { get; set; } = new();
        public Dictionary<string, int> UserCounts { get; set; } = new();
        public Dictionary<string, int> EntityCounts { get; set; } = new();
        public DateTime? FirstLogDate { get; set; }
        public DateTime? LastLogDate { get; set; }
    }
}
