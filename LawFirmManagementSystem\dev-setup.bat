@echo off
echo ========================================
echo إعداد بيئة التطوير
echo Development Environment Setup
echo ========================================
echo.

echo تحقق من متطلبات التطوير...
echo Checking development requirements...

:: Check if .NET 8 SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8.0 SDK غير مثبت
    echo Error: .NET 8.0 SDK is not installed
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download/dotnet/8.0
    echo Please install .NET 8.0 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo .NET 8.0 SDK مثبت بنجاح
echo .NET 8.0 SDK is installed successfully

:: Check .NET version
for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo إصدار .NET: %DOTNET_VERSION%
echo .NET Version: %DOTNET_VERSION%
echo.

:: Check if Git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: Git غير مثبت
    echo Warning: Git is not installed
    echo يُنصح بتثبيت Git من: https://git-scm.com/
    echo Recommended to install Git from: https://git-scm.com/
) else (
    echo Git مثبت بنجاح
    echo Git is installed successfully
)
echo.

:: Check if Visual Studio Code is installed
code --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: Visual Studio Code غير مثبت
    echo Warning: Visual Studio Code is not installed
    echo يُنصح بتثبيت VS Code من: https://code.visualstudio.com/
    echo Recommended to install VS Code from: https://code.visualstudio.com/
) else (
    echo Visual Studio Code مثبت بنجاح
    echo Visual Studio Code is installed successfully
)
echo.

echo تنظيف المشروع...
echo Cleaning project...
dotnet clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع
    echo Error cleaning project
    pause
    exit /b 1
)

echo استعادة الحزم...
echo Restoring packages...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    echo Error restoring packages
    pause
    exit /b 1
)

echo بناء المشروع...
echo Building project...
dotnet build --configuration Debug
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    pause
    exit /b 1
)

echo إنشاء المجلدات المطلوبة...
echo Creating required directories...
if not exist "Logs" mkdir Logs
if not exist "Documents" mkdir Documents
if not exist "Backups" mkdir Backups
if not exist "Reports" mkdir Reports
if not exist "Templates" mkdir Templates

echo تحقق من قاعدة البيانات...
echo Checking database...
:: This would run EF migrations in a real scenario
:: dotnet ef database update

echo ========================================
echo تم إعداد بيئة التطوير بنجاح!
echo Development environment setup complete!
echo ========================================
echo.

echo الخطوات التالية:
echo Next steps:
echo.
echo 1. افتح المشروع في Visual Studio أو VS Code
echo    Open project in Visual Studio or VS Code
echo.
echo 2. قم بتشغيل التطبيق:
echo    Run the application:
echo    dotnet run
echo.
echo 3. أو استخدم F5 في IDE للتشغيل مع التصحيح
echo    Or use F5 in IDE to run with debugging
echo.

echo للتطوير، يُنصح بتشغيل:
echo For development, recommended to run:
echo dotnet run --environment Development
echo.

pause
