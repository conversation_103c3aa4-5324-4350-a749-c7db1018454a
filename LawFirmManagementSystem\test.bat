@echo off
echo ========================================
echo اختبار نظام إدارة مكتب المحاماة
echo Testing Law Firm Management System
echo ========================================
echo.

echo اختر نوع الاختبار:
echo Choose test type:
echo 1. اختبار سريع (Quick Test)
echo 2. اختبار شامل (Full Test)
echo 3. اختبار الأداء (Performance Test)
echo 4. اختبار قاعدة البيانات (Database Test)
echo 5. اختبار الخدمات (Services Test)
echo 6. تشغيل اختبار محدد (Run Specific Test)
echo.

set /p CHOICE="اختر رقم (1-6) / Choose number (1-6): "

if "%CHOICE%"=="1" goto quick_test
if "%CHOICE%"=="2" goto full_test
if "%CHOICE%"=="3" goto performance_test
if "%CHOICE%"=="4" goto database_test
if "%CHOICE%"=="5" goto services_test
if "%CHOICE%"=="6" goto specific_test
goto invalid_choice

:quick_test
echo تشغيل الاختبار السريع...
echo Running quick test...
echo.

echo بناء المشروع...
echo Building project...
dotnet build --configuration Debug
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    goto end
)

echo تشغيل الاختبارات الأساسية...
echo Running basic tests...
dotnet test --configuration Debug --logger "console;verbosity=normal" --filter "Category=Unit"

goto test_complete

:full_test
echo تشغيل الاختبار الشامل...
echo Running full test...
echo.

echo بناء المشروع...
echo Building project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    goto end
)

echo تشغيل جميع الاختبارات...
echo Running all tests...
dotnet test --configuration Release --logger "console;verbosity=normal" --collect:"XPlat Code Coverage"

echo إنشاء تقرير التغطية...
echo Generating coverage report...
dotnet tool install -g dotnet-reportgenerator-globaltool 2>nul
reportgenerator -reports:"TestResults\*\coverage.cobertura.xml" -targetdir:"TestResults\CoverageReport" -reporttypes:Html

goto test_complete

:performance_test
echo تشغيل اختبار الأداء...
echo Running performance test...
echo.

echo بناء المشروع للإنتاج...
echo Building project for production...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    goto end
)

echo تشغيل اختبارات الأداء...
echo Running performance tests...
dotnet test --configuration Release --logger "console;verbosity=normal" --filter "Category=Performance"

goto test_complete

:database_test
echo تشغيل اختبار قاعدة البيانات...
echo Running database test...
echo.

echo التحقق من قاعدة البيانات الاختبارية...
echo Checking test database...
set TEST_CONNECTION="Server=(localdb)\MSSQLLocalDB;Database=LawFirmManagementDB_Test;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"

echo إنشاء قاعدة البيانات الاختبارية...
echo Creating test database...
:: dotnet ef database update --environment Testing

echo تشغيل اختبارات قاعدة البيانات...
echo Running database tests...
dotnet test --configuration Debug --logger "console;verbosity=normal" --filter "Category=Database"

echo تنظيف قاعدة البيانات الاختبارية...
echo Cleaning test database...
:: dotnet ef database drop --environment Testing --force

goto test_complete

:services_test
echo تشغيل اختبار الخدمات...
echo Running services test...
echo.

echo تشغيل اختبارات الخدمات...
echo Running service tests...
dotnet test --configuration Debug --logger "console;verbosity=normal" --filter "Category=Service"

goto test_complete

:specific_test
echo تشغيل اختبار محدد...
echo Running specific test...
echo.

set /p TEST_FILTER="أدخل اسم الاختبار أو الفئة / Enter test name or category: "
if "%TEST_FILTER%"=="" (
    echo لم يتم تحديد اختبار
    echo No test specified
    goto end
)

echo تشغيل الاختبار: %TEST_FILTER%
echo Running test: %TEST_FILTER%
dotnet test --configuration Debug --logger "console;verbosity=normal" --filter "%TEST_FILTER%"

goto test_complete

:test_complete
echo ========================================
echo تم الانتهاء من الاختبارات
echo Testing completed
echo ========================================
echo.

echo عرض النتائج...
echo Showing results...

if exist "TestResults" (
    echo ملفات النتائج:
    echo Result files:
    dir "TestResults" /s /b
    echo.
    
    if exist "TestResults\CoverageReport\index.html" (
        echo تقرير التغطية متوفر في:
        echo Coverage report available at:
        echo TestResults\CoverageReport\index.html
        echo.
        
        set /p OPEN_REPORT="هل تريد فتح تقرير التغطية؟ (y/n) / Open coverage report? (y/n): "
        if /i "%OPEN_REPORT%"=="y" (
            start "" "TestResults\CoverageReport\index.html"
        )
    )
)

echo للحصول على تقارير مفصلة:
echo For detailed reports:
echo dotnet test --logger trx --results-directory TestResults
echo.

goto end

:invalid_choice
echo اختيار غير صحيح
echo Invalid choice

:end
echo.
pause
