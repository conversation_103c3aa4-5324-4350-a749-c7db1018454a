using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الوثيقة
    /// Document entity
    /// </summary>
    [Table("Documents")]
    public class Document : BaseEntity
    {
        [Column("CaseId")]
        public int? CaseId { get; set; }

        [Column("ClientId")]
        public int? ClientId { get; set; }

        [Required]
        [StringLength(200)]
        [Column("DocumentName")]
        public string DocumentName { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("DocumentNameFr")]
        public string? DocumentNameFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("DocumentType")]
        public string DocumentType { get; set; } = string.Empty; // عقد، مرافعة، حكم، شهادة

        [StringLength(50)]
        [Column("DocumentTypeFr")]
        public string? DocumentTypeFr { get; set; }

        [StringLength(50)]
        [Column("Category")]
        public string? Category { get; set; } // قانوني، إداري، مالي، شخصي

        [StringLength(50)]
        [Column("CategoryFr")]
        public string? CategoryFr { get; set; }

        [StringLength(500)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [Required]
        [StringLength(500)]
        [Column("FilePath")]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Column("FileName")]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column("FileExtension")]
        public string FileExtension { get; set; } = string.Empty;

        [Required]
        [Column("FileSize")]
        public long FileSize { get; set; }

        [StringLength(100)]
        [Column("MimeType")]
        public string? MimeType { get; set; }

        [StringLength(100)]
        [Column("FileHash")]
        public string? FileHash { get; set; }

        [Column("DocumentDate")]
        public DateTime? DocumentDate { get; set; }

        [Column("UploadDate")]
        public DateTime UploadDate { get; set; } = DateTime.Now;

        [Column("UploadedBy")]
        public int? UploadedBy { get; set; }

        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "نشط"; // نشط، مؤرشف، محذوف

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(50)]
        [Column("AccessLevel")]
        public string AccessLevel { get; set; } = "عام"; // عام، محدود، سري

        [StringLength(50)]
        [Column("AccessLevelFr")]
        public string? AccessLevelFr { get; set; }

        [Column("IsEncrypted")]
        public bool IsEncrypted { get; set; } = false;

        [StringLength(255)]
        [Column("EncryptionKey")]
        public string? EncryptionKey { get; set; }

        [Column("IsVersioned")]
        public bool IsVersioned { get; set; } = false;

        [Column("Version")]
        public int Version { get; set; } = 1;

        [Column("ParentDocumentId")]
        public int? ParentDocumentId { get; set; }

        [Column("IsTemplate")]
        public bool IsTemplate { get; set; } = false;

        [Column("TemplateId")]
        public int? TemplateId { get; set; }

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [Column("IsSignatureRequired")]
        public bool IsSignatureRequired { get; set; } = false;

        [Column("IsSigned")]
        public bool IsSigned { get; set; } = false;

        [Column("SignedDate")]
        public DateTime? SignedDate { get; set; }

        [Column("SignedBy")]
        public int? SignedBy { get; set; }

        [StringLength(500)]
        [Column("SignaturePath")]
        public string? SignaturePath { get; set; }

        [Column("ExpiryDate")]
        public DateTime? ExpiryDate { get; set; }

        [Column("IsExpired")]
        public bool IsExpired { get; set; } = false;

        [Column("ReminderDate")]
        public DateTime? ReminderDate { get; set; }

        [Column("ReminderSent")]
        public bool ReminderSent { get; set; } = false;

        [Column("DownloadCount")]
        public int DownloadCount { get; set; } = 0;

        [Column("ViewCount")]
        public int ViewCount { get; set; } = 0;

        [Column("LastAccessDate")]
        public DateTime? LastAccessDate { get; set; }

        [Column("LastAccessBy")]
        public int? LastAccessBy { get; set; }

        [Column("IsPublic")]
        public bool IsPublic { get; set; } = false;

        [StringLength(500)]
        [Column("PublicUrl")]
        public string? PublicUrl { get; set; }

        [Column("PublicUrlExpiry")]
        public DateTime? PublicUrlExpiry { get; set; }

        [Column("RequiresApproval")]
        public bool RequiresApproval { get; set; } = false;

        [Column("IsApproved")]
        public bool IsApproved { get; set; } = true;

        [Column("ApprovedBy")]
        public int? ApprovedBy { get; set; }

        [Column("ApprovedDate")]
        public DateTime? ApprovedDate { get; set; }

        [StringLength(500)]
        [Column("ApprovalNotes")]
        public string? ApprovalNotes { get; set; }

        [StringLength(500)]
        [Column("ApprovalNotesFr")]
        public string? ApprovalNotesFr { get; set; }

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [StringLength(20)]
        [Column("Language")]
        public string Language { get; set; } = "ar";

        [Column("PageCount")]
        public int? PageCount { get; set; }

        [StringLength(100)]
        [Column("Author")]
        public string? Author { get; set; }

        [StringLength(100)]
        [Column("AuthorFr")]
        public string? AuthorFr { get; set; }

        [StringLength(200)]
        [Column("Subject")]
        public string? Subject { get; set; }

        [StringLength(200)]
        [Column("SubjectFr")]
        public string? SubjectFr { get; set; }

        [StringLength(1000)]
        [Column("Keywords")]
        public string? Keywords { get; set; }

        [StringLength(1000)]
        [Column("KeywordsFr")]
        public string? KeywordsFr { get; set; }

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase? Case { get; set; }

        [ForeignKey("ClientId")]
        public virtual Client? Client { get; set; }

        [ForeignKey("UploadedBy")]
        public virtual User? UploadedByUser { get; set; }

        [ForeignKey("SignedBy")]
        public virtual User? SignedByUser { get; set; }

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        [ForeignKey("ParentDocumentId")]
        public virtual Document? ParentDocument { get; set; }

        [ForeignKey("TemplateId")]
        public virtual Document? Template { get; set; }

        /// <summary>
        /// الحصول على اسم الوثيقة حسب اللغة
        /// Get document name by language
        /// </summary>
        public string GetDocumentName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DocumentNameFr) 
                ? DocumentNameFr 
                : DocumentName;
        }

        /// <summary>
        /// الحصول على نوع الوثيقة حسب اللغة
        /// Get document type by language
        /// </summary>
        public string GetDocumentType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DocumentTypeFr) 
                ? DocumentTypeFr 
                : DocumentType;
        }

        /// <summary>
        /// الحصول على فئة الوثيقة حسب اللغة
        /// Get document category by language
        /// </summary>
        public string GetCategory(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(CategoryFr) 
                ? CategoryFr 
                : Category ?? string.Empty;
        }

        /// <summary>
        /// الحصول على وصف الوثيقة حسب اللغة
        /// Get document description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// الحصول على حجم الملف بصيغة قابلة للقراءة
        /// Get file size in readable format
        /// </summary>
        public string GetFileSizeFormatted()
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = FileSize;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الوثيقة
        /// Check if document is expired
        /// </summary>
        public bool CheckIfExpired()
        {
            if (ExpiryDate.HasValue)
            {
                IsExpired = ExpiryDate.Value <= DateTime.Now;
                return IsExpired;
            }
            return false;
        }

        /// <summary>
        /// تسجيل الوصول للوثيقة
        /// Record document access
        /// </summary>
        public void RecordAccess(int? accessedBy = null, bool isDownload = false)
        {
            if (isDownload)
            {
                DownloadCount++;
            }
            else
            {
                ViewCount++;
            }
            
            LastAccessDate = DateTime.Now;
            LastAccessBy = accessedBy;
        }

        /// <summary>
        /// الموافقة على الوثيقة
        /// Approve document
        /// </summary>
        public void ApproveDocument(int approvedBy, string? notes = null, string? notesFr = null)
        {
            IsApproved = true;
            ApprovedBy = approvedBy;
            ApprovedDate = DateTime.Now;
            ApprovalNotes = notes;
            ApprovalNotesFr = notesFr;
        }

        /// <summary>
        /// رفض الوثيقة
        /// Reject document
        /// </summary>
        public void RejectDocument(string reason, string? reasonFr = null)
        {
            IsApproved = false;
            ApprovalNotes = reason;
            ApprovalNotesFr = reasonFr;
        }

        /// <summary>
        /// توقيع الوثيقة
        /// Sign document
        /// </summary>
        public void SignDocument(int signedBy, string? signaturePath = null)
        {
            IsSigned = true;
            SignedBy = signedBy;
            SignedDate = DateTime.Now;
            SignaturePath = signaturePath;
        }

        /// <summary>
        /// أرشفة الوثيقة
        /// Archive document
        /// </summary>
        public void ArchiveDocument()
        {
            Status = "مؤرشف";
            StatusFr = "Archived";
        }

        /// <summary>
        /// استعادة الوثيقة من الأرشيف
        /// Restore document from archive
        /// </summary>
        public void RestoreDocument()
        {
            Status = "نشط";
            StatusFr = "Active";
        }
    }
}
