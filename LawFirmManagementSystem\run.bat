@echo off
echo ========================================
echo نظام إدارة مكتب المحاماة
echo Law Firm Management System
echo ========================================
echo.

echo تحقق من متطلبات النظام...
echo Checking system requirements...

:: Check if .NET 8 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8.0 غير مثبت
    echo Error: .NET 8.0 is not installed
    echo يرجى تثبيت .NET 8.0 من: https://dotnet.microsoft.com/download/dotnet/8.0
    echo Please install .NET 8.0 from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo .NET 8.0 مثبت بنجاح
echo .NET 8.0 is installed successfully
echo.

echo استعادة الحزم...
echo Restoring packages...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    echo Error restoring packages
    pause
    exit /b 1
)

echo بناء المشروع...
echo Building project...
dotnet build
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    pause
    exit /b 1
)

echo تشغيل التطبيق...
echo Running application...
echo.
dotnet run

pause
