# نظام إدارة مكتب المحاماة - المسار المصحح
# Law Firm Management System - Corrected Path

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "نظام إدارة مكتب المحاماة" -ForegroundColor Green
Write-Host "Law Firm Management System" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "المسار الحالي / Current Path:" -ForegroundColor Yellow
Write-Host $PWD -ForegroundColor White
Write-Host ""

Write-Host "المسار المطلوب / Required Path:" -ForegroundColor Yellow
Write-Host "C:\Users\<USER>\Desktop\law\LawFirmManagementSystem" -ForegroundColor White
Write-Host ""

# Check if we're in the correct directory
if (Test-Path "LawFirmManagementSystem.csproj") {
    Write-Host "✓ ملف المشروع موجود / Project file found" -ForegroundColor Green
} else {
    Write-Host "✗ ملف المشروع غير موجود / Project file not found" -ForegroundColor Red
    Write-Host ""
    Write-Host "يرجى التأكد من أنك في المجلد الصحيح:" -ForegroundColor Yellow
    Write-Host "Please make sure you are in the correct folder:" -ForegroundColor Yellow
    Write-Host "C:\Users\<USER>\Desktop\law\LawFirmManagementSystem" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة / Press Enter to continue"
    exit
}

Write-Host ""
Write-Host "بيانات تسجيل الدخول / Login Credentials:" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: Admin@123" -ForegroundColor White
Write-Host ""

Write-Host "فحص .NET SDK..." -ForegroundColor Blue
Write-Host "Checking .NET SDK..." -ForegroundColor Blue

try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET SDK غير متاح / .NET SDK not available" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة / Press Enter to continue"
    exit
}

Write-Host ""
Write-Host "تنظيف المشروع..." -ForegroundColor Blue
Write-Host "Cleaning project..." -ForegroundColor Blue

try {
    dotnet clean | Out-Null
    Write-Host "✓ تم تنظيف المشروع / Project cleaned" -ForegroundColor Green
} catch {
    Write-Host "⚠ تحذير في تنظيف المشروع / Warning in project cleaning" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "استعادة الحزم..." -ForegroundColor Blue
Write-Host "Restoring packages..." -ForegroundColor Blue

try {
    dotnet restore | Out-Null
    Write-Host "✓ تم استعادة الحزم بنجاح / Packages restored successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠ تحذير في استعادة الحزم / Warning in package restore" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "بناء المشروع..." -ForegroundColor Blue
Write-Host "Building project..." -ForegroundColor Blue

try {
    $buildResult = dotnet build --configuration Debug 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم البناء بنجاح / Build successful" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "تشغيل التطبيق..." -ForegroundColor Blue
        Write-Host "Running application..." -ForegroundColor Blue
        Write-Host ""
        
        dotnet run
    } else {
        Write-Host "⚠ فشل البناء - محاولة النسخة المبسطة / Build failed - trying simple version" -ForegroundColor Yellow
        Write-Host ""
        
        if (Test-Path "SimpleProgram.cs") {
            Write-Host "تجميع النسخة المبسطة..." -ForegroundColor Blue
            Write-Host "Compiling simple version..." -ForegroundColor Blue
            
            try {
                & csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleProgram.cs
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✓ تم تجميع النسخة المبسطة بنجاح / Simple version compiled successfully" -ForegroundColor Green
                    Write-Host ""
                    Write-Host "تشغيل النسخة المبسطة..." -ForegroundColor Blue
                    Write-Host "Running simple version..." -ForegroundColor Blue
                    & .\SimpleProgram.exe
                } else {
                    Write-Host "✗ فشل تجميع النسخة المبسطة / Simple version compilation failed" -ForegroundColor Red
                }
            } catch {
                Write-Host "✗ خطأ في تجميع النسخة المبسطة / Error compiling simple version" -ForegroundColor Red
            }
        } else {
            Write-Host "✗ ملف النسخة المبسطة غير موجود / Simple version file not found" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ خطأ في البناء / Build error" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "انتهى التشغيل / Execution completed" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "اضغط Enter للإغلاق / Press Enter to close"
