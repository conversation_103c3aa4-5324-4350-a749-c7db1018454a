{"ConnectionStrings": {"DefaultConnection": "Server=.\\SQLEXPRESS;Database=LawFirmDB;Integrated Security=true;TrustServerCertificate=true;MultipleActiveResultSets=true;", "BackupConnection": "Server=.\\SQLEXPRESS;Database=master;Integrated Security=true;TrustServerCertificate=true;"}, "ApplicationSettings": {"ApplicationName": "نظام إدارة مكتب المحاماة", "ApplicationNameFr": "Système de Gestion de Cabinet d'Avocat", "Version": "1.0.0", "DefaultLanguage": "ar", "SupportedLanguages": ["ar", "fr"], "DefaultTheme": "Professional", "SessionTimeoutMinutes": 30, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15, "PasswordMinLength": 8, "RequirePasswordComplexity": true, "EnableTwoFactorAuth": false, "AutoBackupEnabled": true, "AutoBackupIntervalHours": 24, "MaxBackupFiles": 30}, "LawFirmSettings": {"FirmName": "مكتب المحاماة", "FirmNameFr": "Cabinet d'Avocat", "ICE": "", "CommercialRegister": "", "Address": "", "AddressFr": "", "City": "", "CityFr": "", "Phone": "", "Fax": "", "Email": "", "Website": "", "LogoPath": "Resources\\Images\\logo.png", "BarAssociation": "", "BarAssociationFr": "", "LicenseNumber": ""}, "EmailSettings": {"SmtpServer": "", "SmtpPort": 587, "EnableSsl": true, "Username": "", "Password": "", "FromEmail": "", "FromName": "", "FromNameFr": "", "EnableEmailNotifications": false}, "WhatsAppSettings": {"TwilioAccountSid": "", "TwilioAuthToken": "", "TwilioPhoneNumber": "", "EnableWhatsAppNotifications": false, "DefaultCountryCode": "+212"}, "SecuritySettings": {"EncryptionKey": "LawFirm2025SecureKey!@#$%^&*()", "JwtSecretKey": "LawFirmJWTSecretKey2025!@#$%^&*()", "JwtExpirationHours": 8, "EnableAuditLog": true, "EnableDataEncryption": true, "RequirePasswordChange": false, "PasswordChangeIntervalDays": 90}, "FileSettings": {"DocumentsPath": "Documents", "BackupsPath": "Backups", "LogsPath": "Logs", "TempPath": "Temp", "ReportsPath": "Reports", "MaxFileSize": ********, "AllowedFileExtensions": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".txt", ".rtf"], "EnableFileEncryption": false, "AutoDeleteTempFiles": true, "TempFileRetentionDays": 7}, "ReportSettings": {"DefaultReportFormat": "PDF", "IncludeLogo": true, "IncludeWatermark": false, "WatermarkText": "مسودة", "WatermarkTextFr": "Brouillon", "ReportFooter": "تم إنشاء هذا التقرير بواسطة نظام إدارة مكتب المحاماة", "ReportFooterFr": "Ce rapport a été généré par le Système de Gestion de Cabinet d'Avocat", "DateFormat": "dd/MM/yyyy", "TimeFormat": "HH:mm", "CurrencySymbol": "درهم", "CurrencySymbolFr": "DH"}, "NotificationSettings": {"EnableDesktopNotifications": true, "EnableSoundNotifications": true, "NotificationSoundPath": "Resources\\Sounds\\notification.wav", "ReminderIntervals": [1440, 720, 360, 60, 15], "DefaultReminderInterval": 60, "EnableHearingReminders": true, "EnableAppointmentReminders": true, "EnablePaymentReminders": true, "EnableDeadlineReminders": true}, "DatabaseSettings": {"CommandTimeout": 30, "EnableConnectionPooling": true, "MaxPoolSize": 100, "MinPoolSize": 5, "ConnectionLifetime": 300, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30"}, "LoggingSettings": {"LogLevel": "Information", "EnableFileLogging": true, "EnableConsoleLogging": false, "LogFilePath": "Logs\\application-{Date}.log", "MaxLogFileSize": "10MB", "RetainedFileCountLimit": 30, "EnableStructuredLogging": true, "LogSensitiveData": false}, "UISettings": {"DefaultFormSize": "1024,768", "MinimumFormSize": "800,600", "EnableFormRememberSize": true, "EnableFormRememberPosition": true, "DefaultFont": "Segoe UI", "DefaultFontSize": 9, "ArabicFont": "<PERSON><PERSON><PERSON>", "ArabicFontSize": 9, "EnableHighDpiSupport": true, "EnableAnimations": true, "AnimationSpeed": "Normal"}}