using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان العميل/الموكل
    /// Client entity
    /// </summary>
    [Table("Clients")]
    public class Client : BaseEntity
    {
        [Required]
        [StringLength(20)]
        [Column("ClientReference")]
        public string ClientReference { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        [Column("ClientType")]
        public string ClientType { get; set; } = "Individual"; // Individual, Company, Government

        [Required]
        [StringLength(100)]
        [Column("FullName")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("FullNameFr")]
        public string? FullNameFr { get; set; }

        [StringLength(100)]
        [Column("FirstName")]
        public string? FirstName { get; set; }

        [StringLength(100)]
        [Column("FirstNameFr")]
        public string? FirstNameFr { get; set; }

        [StringLength(100)]
        [Column("LastName")]
        public string? LastName { get; set; }

        [StringLength(100)]
        [Column("LastNameFr")]
        public string? LastNameFr { get; set; }

        [StringLength(20)]
        [Column("CIN")]
        public string? CIN { get; set; }

        [StringLength(50)]
        [Column("PassportNumber")]
        public string? PassportNumber { get; set; }

        [StringLength(50)]
        [Column("CommercialRegister")]
        public string? CommercialRegister { get; set; }

        [StringLength(50)]
        [Column("ICE")]
        public string? ICE { get; set; }

        [StringLength(50)]
        [Column("TaxNumber")]
        public string? TaxNumber { get; set; }

        [Column("DateOfBirth")]
        public DateTime? DateOfBirth { get; set; }

        [StringLength(10)]
        [Column("Gender")]
        public string? Gender { get; set; } // Male, Female

        [StringLength(50)]
        [Column("Nationality")]
        public string? Nationality { get; set; }

        [StringLength(50)]
        [Column("NationalityFr")]
        public string? NationalityFr { get; set; }

        [StringLength(50)]
        [Column("MaritalStatus")]
        public string? MaritalStatus { get; set; }

        [StringLength(50)]
        [Column("MaritalStatusFr")]
        public string? MaritalStatusFr { get; set; }

        [StringLength(100)]
        [Column("Profession")]
        public string? Profession { get; set; }

        [StringLength(100)]
        [Column("ProfessionFr")]
        public string? ProfessionFr { get; set; }

        [StringLength(100)]
        [Column("Employer")]
        public string? Employer { get; set; }

        [StringLength(100)]
        [Column("EmployerFr")]
        public string? EmployerFr { get; set; }

        [StringLength(100)]
        [EmailAddress]
        [Column("Email")]
        public string? Email { get; set; }

        [StringLength(20)]
        [Column("Phone")]
        public string? Phone { get; set; }

        [StringLength(20)]
        [Column("Mobile")]
        public string? Mobile { get; set; }

        [StringLength(20)]
        [Column("Fax")]
        public string? Fax { get; set; }

        [StringLength(200)]
        [Column("Address")]
        public string? Address { get; set; }

        [StringLength(200)]
        [Column("AddressFr")]
        public string? AddressFr { get; set; }

        [StringLength(50)]
        [Column("City")]
        public string? City { get; set; }

        [StringLength(50)]
        [Column("CityFr")]
        public string? CityFr { get; set; }

        [StringLength(50)]
        [Column("Province")]
        public string? Province { get; set; }

        [StringLength(50)]
        [Column("ProvinceFr")]
        public string? ProvinceFr { get; set; }

        [StringLength(10)]
        [Column("PostalCode")]
        public string? PostalCode { get; set; }

        [StringLength(50)]
        [Column("Country")]
        public string? Country { get; set; } = "المغرب";

        [StringLength(50)]
        [Column("CountryFr")]
        public string? CountryFr { get; set; } = "Maroc";

        [StringLength(100)]
        [Column("EmergencyContact")]
        public string? EmergencyContact { get; set; }

        [StringLength(20)]
        [Column("EmergencyPhone")]
        public string? EmergencyPhone { get; set; }

        [StringLength(50)]
        [Column("EmergencyRelation")]
        public string? EmergencyRelation { get; set; }

        [StringLength(50)]
        [Column("EmergencyRelationFr")]
        public string? EmergencyRelationFr { get; set; }

        [StringLength(50)]
        [Column("PreferredLanguage")]
        public string PreferredLanguage { get; set; } = "ar";

        [StringLength(50)]
        [Column("PreferredContactMethod")]
        public string PreferredContactMethod { get; set; } = "Phone";

        [Column("IsVIP")]
        public bool IsVIP { get; set; } = false;

        [StringLength(50)]
        [Column("ClientStatus")]
        public string ClientStatus { get; set; } = "Active"; // Active, Inactive, Suspended

        [Column("RegistrationDate")]
        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        [Column("ProfilePicturePath")]
        public string? ProfilePicturePath { get; set; }

        [Column("CreditLimit")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CreditLimit { get; set; }

        [Column("CurrentBalance")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column("TotalPaid")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPaid { get; set; } = 0;

        [Column("TotalDue")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDue { get; set; } = 0;

        [Column("LastPaymentDate")]
        public DateTime? LastPaymentDate { get; set; }

        [Column("LastContactDate")]
        public DateTime? LastContactDate { get; set; }

        [StringLength(50)]
        [Column("ReferralSource")]
        public string? ReferralSource { get; set; }

        [StringLength(50)]
        [Column("ReferralSourceFr")]
        public string? ReferralSourceFr { get; set; }

        [StringLength(100)]
        [Column("ReferredBy")]
        public string? ReferredBy { get; set; }

        [Column("ConsentToMarketing")]
        public bool ConsentToMarketing { get; set; } = false;

        [Column("ConsentToDataProcessing")]
        public bool ConsentToDataProcessing { get; set; } = true;

        [StringLength(2000)]
        [Column("SpecialInstructions")]
        public string? SpecialInstructions { get; set; }

        [StringLength(2000)]
        [Column("SpecialInstructionsFr")]
        public string? SpecialInstructionsFr { get; set; }

        // Navigation Properties
        public virtual ICollection<LegalCase> Cases { get; set; } = new List<LegalCase>();
        public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
        public virtual ICollection<ClientContact> ContactHistory { get; set; } = new List<ClientContact>();

        /// <summary>
        /// الحصول على الاسم الكامل حسب اللغة
        /// Get full name by language
        /// </summary>
        public string GetFullName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(FullNameFr) 
                ? FullNameFr 
                : FullName;
        }

        /// <summary>
        /// الحصول على العنوان حسب اللغة
        /// Get address by language
        /// </summary>
        public string GetAddress(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(AddressFr) 
                ? AddressFr 
                : Address ?? string.Empty;
        }

        /// <summary>
        /// تحديث الرصيد
        /// Update balance
        /// </summary>
        public void UpdateBalance()
        {
            CurrentBalance = TotalDue - TotalPaid;
        }

        /// <summary>
        /// إضافة دفعة
        /// Add payment
        /// </summary>
        public void AddPayment(decimal amount)
        {
            TotalPaid += amount;
            LastPaymentDate = DateTime.Now;
            UpdateBalance();
        }

        /// <summary>
        /// إضافة مبلغ مستحق
        /// Add due amount
        /// </summary>
        public void AddDueAmount(decimal amount)
        {
            TotalDue += amount;
            UpdateBalance();
        }

        /// <summary>
        /// تحديث آخر تاريخ اتصال
        /// Update last contact date
        /// </summary>
        public void UpdateLastContact()
        {
            LastContactDate = DateTime.Now;
        }
    }
}
