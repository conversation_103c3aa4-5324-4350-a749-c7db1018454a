using System;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using LawOfficeManagement.Models;
using LawOfficeManagement.Utils;

namespace LawOfficeManagement.Services
{
    public class SimpleAuthenticationService
    {
        private readonly SimpleDatabaseService _databaseService;
        
        public SimpleAuthenticationService()
        {
            _databaseService = SimpleDatabaseService.Instance;
        }
        
        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new LoginResult { IsSuccess = false, ErrorMessage = "اسم المستخدم وكلمة المرور مطلوبان" };
                }
                
                // البحث عن المستخدم في قاعدة البيانات
                var query = @"
                    SELECT UserId, Username, PasswordHash, FullName, Email, Phone, Role, 
                           IsActive, FailedLoginAttempts, IsLocked, LockoutEnd
                    FROM Users 
                    WHERE Username = @Username";
                
                var parameters = new[] { new SqliteParameter("@Username", username) };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                
                if (dataTable.Rows.Count == 0)
                {
                    return new LoginResult { IsSuccess = false, ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }
                
                var row = dataTable.Rows[0];
                var user = new User
                {
                    UserId = (int)(long)row["UserId"],
                    Username = row["Username"].ToString()!,
                    PasswordHash = row["PasswordHash"].ToString()!,
                    FullName = row["FullName"].ToString()!,
                    Email = row["Email"] as string,
                    Phone = row["Phone"] as string,
                    Role = row["Role"].ToString()!,
                    IsActive = Convert.ToBoolean(row["IsActive"]),
                    FailedLoginAttempts = Convert.ToInt32(row["FailedLoginAttempts"]),
                    IsLocked = Convert.ToBoolean(row["IsLocked"])
                };
                
                // التحقق من حالة المستخدم
                if (!user.IsActive)
                {
                    return new LoginResult { IsSuccess = false, ErrorMessage = "الحساب غير نشط" };
                }
                
                if (user.IsLocked)
                {
                    return new LoginResult { IsSuccess = false, ErrorMessage = "الحساب مقفل مؤقتاً" };
                }
                
                // التحقق من كلمة المرور
                var hashedPassword = PasswordHelper.HashPassword(password);
                if (user.PasswordHash != hashedPassword)
                {
                    // زيادة عدد المحاولات الفاشلة
                    await IncrementFailedLoginAttemptsAsync(user.UserId);
                    return new LoginResult { IsSuccess = false, ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }
                
                // تسجيل دخول ناجح
                await ResetFailedLoginAttemptsAsync(user.UserId);
                await UpdateLastLoginAsync(user.UserId);
                
                return new LoginResult 
                { 
                    IsSuccess = true, 
                    User = user,
                    ErrorMessage = string.Empty
                };
            }
            catch (Exception ex)
            {
                return new LoginResult { IsSuccess = false, ErrorMessage = $"خطأ في تسجيل الدخول: {ex.Message}" };
            }
        }
        
        private async Task IncrementFailedLoginAttemptsAsync(int userId)
        {
            try
            {
                var query = @"
                    UPDATE Users 
                    SET FailedLoginAttempts = FailedLoginAttempts + 1,
                        IsLocked = CASE WHEN FailedLoginAttempts >= 4 THEN 1 ELSE 0 END
                    WHERE UserId = @UserId";
                
                var parameters = new[] { new SqliteParameter("@UserId", userId) };
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث محاولات تسجيل الدخول: {ex.Message}");
            }
        }
        
        private async Task ResetFailedLoginAttemptsAsync(int userId)
        {
            try
            {
                var query = @"
                    UPDATE Users 
                    SET FailedLoginAttempts = 0, IsLocked = 0
                    WHERE UserId = @UserId";
                
                var parameters = new[] { new SqliteParameter("@UserId", userId) };
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إعادة تعيين محاولات تسجيل الدخول: {ex.Message}");
            }
        }
        
        private async Task UpdateLastLoginAsync(int userId)
        {
            try
            {
                var query = "UPDATE Users SET LastLogin = @LastLogin WHERE UserId = @UserId";
                var parameters = new[]
                {
                    new SqliteParameter("@LastLogin", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")),
                    new SqliteParameter("@UserId", userId)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث آخر تسجيل دخول: {ex.Message}");
            }
        }
        
        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                // التحقق من كلمة المرور الحالية
                var query = "SELECT PasswordHash FROM Users WHERE UserId = @UserId";
                var parameters = new[] { new SqliteParameter("@UserId", userId) };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                
                if (dataTable.Rows.Count == 0)
                {
                    return false;
                }
                
                var currentHash = dataTable.Rows[0]["PasswordHash"].ToString();
                var inputHash = PasswordHelper.HashPassword(currentPassword);
                
                if (currentHash != inputHash)
                {
                    return false;
                }
                
                // تحديث كلمة المرور
                var newHash = PasswordHelper.HashPassword(newPassword);
                var updateQuery = @"
                    UPDATE Users 
                    SET PasswordHash = @PasswordHash, ModifiedDate = @ModifiedDate 
                    WHERE UserId = @UserId";
                
                var updateParameters = new[]
                {
                    new SqliteParameter("@PasswordHash", newHash),
                    new SqliteParameter("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")),
                    new SqliteParameter("@UserId", userId)
                };
                
                var result = await _databaseService.ExecuteNonQueryAsync(updateQuery, updateParameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تغيير كلمة المرور: {ex.Message}");
                return false;
            }
        }
    }
}
