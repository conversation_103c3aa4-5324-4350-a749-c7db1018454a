using LawFirmManagementSystem.Services.Interfaces;
using LawFirmManagementSystem.Utils;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Forms.Authentication
{
    /// <summary>
    /// نموذج تسجيل الدخول
    /// Login form
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthenticationService _authenticationService;
        private readonly ILocalizationService _localizationService;
        private readonly ILogger<LoginForm> _logger;

        // Controls
        private Panel mainPanel;
        private Panel loginPanel;
        private Panel headerPanel;
        private Label titleLabel;
        private Label subtitleLabel;
        private PictureBox logoBox;
        private TextBox usernameTextBox;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button cancelButton;
        private CheckBox rememberCheckBox;
        private LinkLabel forgotPasswordLink;
        private ComboBox languageComboBox;
        private Label languageLabel;
        private Label usernameLabel;
        private Label passwordLabel;
        private ProgressBar progressBar;
        private Label statusLabel;

        public LoginForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _authenticationService = _serviceProvider.GetRequiredService<IAuthenticationService>();
            _localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();
            _logger = _serviceProvider.GetRequiredService<ILogger<LoginForm>>();

            InitializeComponent();
            InitializeLocalization();
            SetupEventHandlers();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "تسجيل الدخول - Law Firm Management System";
            this.Size = new Size(450, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.Icon = SystemIcons.Application;

            // Main panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Header panel
            headerPanel = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(41, 128, 185)
            };

            // Logo
            logoBox = new PictureBox
            {
                Size = new Size(64, 64),
                Location = new Point(20, 28),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.Transparent
            };

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة مكتب المحاماة",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(100, 25),
                Size = new Size(300, 30),
                BackColor = Color.Transparent,
                Tag = "AppTitle"
            };

            // Subtitle
            subtitleLabel = new Label
            {
                Text = "Law Firm Management System",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(220, 220, 220),
                Location = new Point(100, 55),
                Size = new Size(300, 25),
                BackColor = Color.Transparent
            };

            // Login panel
            loginPanel = new Panel
            {
                Size = new Size(350, 400),
                Location = new Point(50, 150),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // Add shadow effect
            loginPanel.Paint += (s, e) =>
            {
                var rect = loginPanel.ClientRectangle;
                rect.Inflate(-1, -1);
                ControlPaint.DrawBorder(e.Graphics, rect, Color.FromArgb(200, 200, 200), ButtonBorderStyle.Solid);
            };

            // Language selection
            languageLabel = new Label
            {
                Text = "اللغة / Language:",
                Location = new Point(20, 20),
                Size = new Size(120, 23),
                Font = new Font("Tahoma", 9),
                Tag = "Language"
            };

            languageComboBox = new ComboBox
            {
                Location = new Point(150, 20),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Tahoma", 9)
            };
            languageComboBox.Items.AddRange(new[] { "العربية", "Français" });
            languageComboBox.SelectedIndex = 0;

            // Username
            usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Location = new Point(20, 70),
                Size = new Size(100, 23),
                Font = new Font("Tahoma", 9),
                Tag = "Common.Username"
            };

            usernameTextBox = new TextBox
            {
                Location = new Point(20, 95),
                Size = new Size(280, 25),
                Font = new Font("Tahoma", 10),
                PlaceholderText = "أدخل اسم المستخدم"
            };

            // Password
            passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Location = new Point(20, 130),
                Size = new Size(100, 23),
                Font = new Font("Tahoma", 9),
                Tag = "Common.Password"
            };

            passwordTextBox = new TextBox
            {
                Location = new Point(20, 155),
                Size = new Size(280, 25),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true,
                PlaceholderText = "أدخل كلمة المرور"
            };

            // Remember me
            rememberCheckBox = new CheckBox
            {
                Text = "تذكرني",
                Location = new Point(20, 190),
                Size = new Size(100, 23),
                Font = new Font("Tahoma", 9),
                Tag = "RememberMe"
            };

            // Forgot password
            forgotPasswordLink = new LinkLabel
            {
                Text = "نسيت كلمة المرور؟",
                Location = new Point(200, 190),
                Size = new Size(100, 23),
                Font = new Font("Tahoma", 9),
                LinkColor = Color.FromArgb(41, 128, 185),
                Tag = "ForgotPassword"
            };

            // Login button
            loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Location = new Point(20, 230),
                Size = new Size(130, 35),
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(41, 128, 185),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Tag = "Common.Login"
            };
            loginButton.FlatAppearance.BorderSize = 0;

            // Cancel button
            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(170, 230),
                Size = new Size(130, 35),
                Font = new Font("Tahoma", 10),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Tag = "Common.Cancel"
            };
            cancelButton.FlatAppearance.BorderSize = 0;

            // Progress bar
            progressBar = new ProgressBar
            {
                Location = new Point(20, 280),
                Size = new Size(280, 10),
                Style = ProgressBarStyle.Marquee,
                Visible = false
            };

            // Status label
            statusLabel = new Label
            {
                Location = new Point(20, 300),
                Size = new Size(280, 40),
                Font = new Font("Tahoma", 9),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter,
                Visible = false
            };

            // Add controls to panels
            headerPanel.Controls.AddRange(new Control[] { logoBox, titleLabel, subtitleLabel });
            
            loginPanel.Controls.AddRange(new Control[] {
                languageLabel, languageComboBox,
                usernameLabel, usernameTextBox,
                passwordLabel, passwordTextBox,
                rememberCheckBox, forgotPasswordLink,
                loginButton, cancelButton,
                progressBar, statusLabel
            });

            mainPanel.Controls.AddRange(new Control[] { headerPanel, loginPanel });
            this.Controls.Add(mainPanel);
        }

        private void InitializeLocalization()
        {
            // Initialize localization helper
            LocalizationHelper.Initialize(_localizationService);
            
            // Apply initial localization
            LocalizationHelper.LocalizeForm(this);
        }

        private void SetupEventHandlers()
        {
            // Language change
            languageComboBox.SelectedIndexChanged += LanguageComboBox_SelectedIndexChanged;

            // Login button
            loginButton.Click += LoginButton_Click;

            // Cancel button
            cancelButton.Click += (s, e) => this.Close();

            // Forgot password
            forgotPasswordLink.LinkClicked += ForgotPasswordLink_LinkClicked;

            // Enter key handling
            usernameTextBox.KeyDown += TextBox_KeyDown;
            passwordTextBox.KeyDown += TextBox_KeyDown;

            // Form events
            this.Load += LoginForm_Load;
            this.Shown += LoginForm_Shown;
        }

        private void LanguageComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var language = languageComboBox.SelectedIndex == 0 ? "ar" : "fr";
            LocalizationHelper.SwitchLanguage(this, language);
            
            // Update form title
            this.Text = language == "ar" 
                ? "تسجيل الدخول - نظام إدارة مكتب المحاماة"
                : "Connexion - Système de Gestion de Cabinet d'Avocat";
        }

        private async void LoginButton_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        private void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                _ = PerformLoginAsync();
            }
        }

        private void ForgotPasswordLink_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var message = _localizationService.GetCurrentLanguage() == "ar"
                ? "يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور."
                : "Veuillez contacter l'administrateur système pour réinitialiser le mot de passe.";
            
            MessageBox.Show(message, LocalizationHelper.GetText("Messages.Information"), 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Set default values for testing
            usernameTextBox.Text = "admin";
            passwordTextBox.Text = "Admin@123";
        }

        private void LoginForm_Shown(object sender, EventArgs e)
        {
            usernameTextBox.Focus();
        }

        private async Task PerformLoginAsync()
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(usernameTextBox.Text))
                {
                    ShowError(LocalizationHelper.GetText("Messages.RequiredField") + ": " + LocalizationHelper.GetText("Common.Username"));
                    usernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    ShowError(LocalizationHelper.GetText("Messages.RequiredField") + ": " + LocalizationHelper.GetText("Common.Password"));
                    passwordTextBox.Focus();
                    return;
                }

                // Show progress
                SetLoginInProgress(true);

                // Perform authentication
                var user = await _authenticationService.AuthenticateAsync(usernameTextBox.Text, passwordTextBox.Text);

                if (user != null)
                {
                    _logger.LogInformation("User {Username} logged in successfully", user.Username);
                    
                    // Store user session info
                    UserSession.CurrentUser = user;
                    UserSession.CurrentLanguage = _localizationService.GetCurrentLanguage();

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowError(LocalizationHelper.GetText("Messages.LoginFailed"));
                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                ShowError(LocalizationHelper.GetText("Messages.UnexpectedError"));
            }
            finally
            {
                SetLoginInProgress(false);
            }
        }

        private void SetLoginInProgress(bool inProgress)
        {
            loginButton.Enabled = !inProgress;
            cancelButton.Enabled = !inProgress;
            usernameTextBox.Enabled = !inProgress;
            passwordTextBox.Enabled = !inProgress;
            languageComboBox.Enabled = !inProgress;
            progressBar.Visible = inProgress;
            
            if (inProgress)
            {
                statusLabel.Text = LocalizationHelper.GetText("Common.Connecting");
                statusLabel.ForeColor = Color.Blue;
                statusLabel.Visible = true;
            }
            else
            {
                statusLabel.Visible = false;
            }
        }

        private void ShowError(string message)
        {
            statusLabel.Text = message;
            statusLabel.ForeColor = Color.Red;
            statusLabel.Visible = true;
        }
    }

    /// <summary>
    /// جلسة المستخدم الحالية
    /// Current user session
    /// </summary>
    public static class UserSession
    {
        public static Models.Entities.User? CurrentUser { get; set; }
        public static string CurrentLanguage { get; set; } = "ar";
    }
}
