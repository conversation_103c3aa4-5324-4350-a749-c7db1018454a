using System;
using System.ComponentModel.DataAnnotations;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// الكيان الأساسي الذي ترث منه جميع الكيانات الأخرى
    /// Base entity that all other entities inherit from
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public int? CreatedBy { get; set; }

        public int? ModifiedBy { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;

        public bool IsDeleted { get; set; } = false;

        public DateTime? DeletedDate { get; set; }

        public int? DeletedBy { get; set; }

        /// <summary>
        /// معرف فريد للكيان
        /// Unique identifier for the entity
        /// </summary>
        public Guid UniqueId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// رقم الإصدار للتحكم في التزامن المتفائل
        /// Version number for optimistic concurrency control
        /// </summary>
        [Timestamp]
        public byte[]? RowVersion { get; set; }

        /// <summary>
        /// ملاحظات عامة
        /// General notes
        /// </summary>
        [StringLength(2000)]
        public string? Notes { get; set; }

        /// <summary>
        /// ملاحظات عامة بالفرنسية
        /// General notes in French
        /// </summary>
        [StringLength(2000)]
        public string? NotesFr { get; set; }

        /// <summary>
        /// بيانات إضافية بصيغة JSON
        /// Additional data in JSON format
        /// </summary>
        public string? AdditionalData { get; set; }

        /// <summary>
        /// تحديث تاريخ التعديل
        /// Update modification date
        /// </summary>
        public virtual void UpdateModificationInfo(int? modifiedBy = null)
        {
            ModifiedDate = DateTime.Now;
            ModifiedBy = modifiedBy;
        }

        /// <summary>
        /// وضع علامة الحذف الناعم
        /// Mark for soft delete
        /// </summary>
        public virtual void MarkAsDeleted(int? deletedBy = null)
        {
            IsDeleted = true;
            IsActive = false;
            DeletedDate = DateTime.Now;
            DeletedBy = deletedBy;
        }

        /// <summary>
        /// استعادة من الحذف الناعم
        /// Restore from soft delete
        /// </summary>
        public virtual void RestoreFromDeleted()
        {
            IsDeleted = false;
            IsActive = true;
            DeletedDate = null;
            DeletedBy = null;
        }
    }
}
