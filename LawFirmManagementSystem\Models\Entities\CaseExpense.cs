using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان مصروف القضية
    /// Case expense entity
    /// </summary>
    [Table("CaseExpenses")]
    public class CaseExpense : BaseEntity
    {
        [Required]
        [Column("CaseId")]
        public int CaseId { get; set; }

        [Required]
        [StringLength(100)]
        [Column("ExpenseType")]
        public string ExpenseType { get; set; } = string.Empty; // أتعاب، رسوم محكمة، تحرير، تنقل، خبرة

        [StringLength(100)]
        [Column("ExpenseTypeFr")]
        public string? ExpenseTypeFr { get; set; }

        [Required]
        [StringLength(200)]
        [Column("Description")]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [Required]
        [Column("Amount", TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [Column("ExpenseDate")]
        public DateTime ExpenseDate { get; set; } = DateTime.Now;

        [Column("DueDate")]
        public DateTime? DueDate { get; set; }

        [Column("PaidDate")]
        public DateTime? PaidDate { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مستحق"; // مستحق، مدفوع، مؤجل، ملغي

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(50)]
        [Column("PaymentMethod")]
        public string? PaymentMethod { get; set; } // نقد، شيك، تحويل، بطاقة

        [StringLength(50)]
        [Column("PaymentMethodFr")]
        public string? PaymentMethodFr { get; set; }

        [StringLength(100)]
        [Column("PaymentReference")]
        public string? PaymentReference { get; set; }

        [StringLength(100)]
        [Column("Vendor")]
        public string? Vendor { get; set; }

        [StringLength(100)]
        [Column("VendorFr")]
        public string? VendorFr { get; set; }

        [StringLength(100)]
        [Column("VendorContact")]
        public string? VendorContact { get; set; }

        [StringLength(20)]
        [Column("VendorPhone")]
        public string? VendorPhone { get; set; }

        [StringLength(100)]
        [EmailAddress]
        [Column("VendorEmail")]
        public string? VendorEmail { get; set; }

        [StringLength(100)]
        [Column("InvoiceNumber")]
        public string? InvoiceNumber { get; set; }

        [Column("InvoiceDate")]
        public DateTime? InvoiceDate { get; set; }

        [StringLength(500)]
        [Column("ReceiptPath")]
        public string? ReceiptPath { get; set; }

        [Column("IsBillableToClient")]
        public bool IsBillableToClient { get; set; } = true;

        [Column("IsReimbursable")]
        public bool IsReimbursable { get; set; } = false;

        [Column("IsRecurring")]
        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        [Column("RecurrencePattern")]
        public string? RecurrencePattern { get; set; }

        [Column("RecurrenceInterval")]
        public int? RecurrenceInterval { get; set; }

        [Column("RecurrenceEndDate")]
        public DateTime? RecurrenceEndDate { get; set; }

        [Column("TaxAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxAmount { get; set; }

        [Column("TaxRate")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? TaxRate { get; set; }

        [Column("DiscountAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? DiscountAmount { get; set; }

        [Column("DiscountRate")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? DiscountRate { get; set; }

        [Column("NetAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }

        [StringLength(50)]
        [Column("Currency")]
        public string Currency { get; set; } = "MAD";

        [Column("ExchangeRate")]
        [Column(TypeName = "decimal(10,4)")]
        public decimal? ExchangeRate { get; set; }

        [Column("AmountInBaseCurrency")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AmountInBaseCurrency { get; set; }

        [StringLength(50)]
        [Column("Category")]
        public string? Category { get; set; } // إجباري، اختياري، طارئ

        [StringLength(50)]
        [Column("CategoryFr")]
        public string? CategoryFr { get; set; }

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [Column("ApprovedBy")]
        public int? ApprovedBy { get; set; }

        [Column("ApprovedDate")]
        public DateTime? ApprovedDate { get; set; }

        [StringLength(500)]
        [Column("ApprovalNotes")]
        public string? ApprovalNotes { get; set; }

        [StringLength(500)]
        [Column("ApprovalNotesFr")]
        public string? ApprovalNotesFr { get; set; }

        [Column("RequiresApproval")]
        public bool RequiresApproval { get; set; } = false;

        [Column("IsApproved")]
        public bool IsApproved { get; set; } = true;

        [StringLength(2000)]
        [Column("InternalNotes")]
        public string? InternalNotes { get; set; }

        [StringLength(2000)]
        [Column("InternalNotesFr")]
        public string? InternalNotesFr { get; set; }

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase Case { get; set; } = null!;

        [ForeignKey("ApprovedBy")]
        public virtual User? ApprovedByUser { get; set; }

        /// <summary>
        /// الحصول على نوع المصروف حسب اللغة
        /// Get expense type by language
        /// </summary>
        public string GetExpenseType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ExpenseTypeFr) 
                ? ExpenseTypeFr 
                : ExpenseType;
        }

        /// <summary>
        /// الحصول على وصف المصروف حسب اللغة
        /// Get expense description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description;
        }

        /// <summary>
        /// الحصول على حالة المصروف حسب اللغة
        /// Get expense status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// التحقق من دفع المصروف
        /// Check if expense is paid
        /// </summary>
        public bool IsPaid()
        {
            return Status == "مدفوع" || Status == "Paid";
        }

        /// <summary>
        /// التحقق من استحقاق المصروف
        /// Check if expense is due
        /// </summary>
        public bool IsDue()
        {
            return Status == "مستحق" || Status == "Due";
        }

        /// <summary>
        /// التحقق من تأخر المصروف
        /// Check if expense is overdue
        /// </summary>
        public bool IsOverdue()
        {
            return IsDue() && DueDate.HasValue && DueDate.Value < DateTime.Now;
        }

        /// <summary>
        /// حساب المبلغ الصافي
        /// Calculate net amount
        /// </summary>
        public void CalculateNetAmount()
        {
            var taxAmount = TaxAmount ?? 0;
            var discountAmount = DiscountAmount ?? 0;
            NetAmount = Amount + taxAmount - discountAmount;
        }

        /// <summary>
        /// دفع المصروف
        /// Pay expense
        /// </summary>
        public void PayExpense(string paymentMethod, string? paymentReference = null)
        {
            Status = "مدفوع";
            StatusFr = "Paid";
            PaidDate = DateTime.Now;
            PaymentMethod = paymentMethod;
            PaymentReference = paymentReference;
        }

        /// <summary>
        /// إلغاء المصروف
        /// Cancel expense
        /// </summary>
        public void CancelExpense()
        {
            Status = "ملغي";
            StatusFr = "Cancelled";
        }

        /// <summary>
        /// الموافقة على المصروف
        /// Approve expense
        /// </summary>
        public void ApproveExpense(int approvedBy, string? notes = null, string? notesFr = null)
        {
            IsApproved = true;
            ApprovedBy = approvedBy;
            ApprovedDate = DateTime.Now;
            ApprovalNotes = notes;
            ApprovalNotesFr = notesFr;
        }

        /// <summary>
        /// رفض المصروف
        /// Reject expense
        /// </summary>
        public void RejectExpense(string reason, string? reasonFr = null)
        {
            IsApproved = false;
            ApprovalNotes = reason;
            ApprovalNotesFr = reasonFr;
            Status = "مرفوض";
            StatusFr = "Rejected";
        }
    }
}
