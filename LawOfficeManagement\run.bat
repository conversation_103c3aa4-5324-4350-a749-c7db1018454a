@echo off
chcp 65001 > nul
echo ========================================
echo    نظام إدارة مكتب المحاماة
echo    Law Office Management System
echo ========================================
echo.
echo تشغيل التطبيق...
echo Starting application...
echo.

dotnet run --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo ❌ Failed to start application
    echo.
    echo يرجى التأكد من:
    echo Please make sure:
    echo 1. تم تشغيل setup.bat أولاً
    echo 1. setup.bat was run first
    echo 2. SQL Server يعمل بشكل صحيح
    echo 2. SQL Server is running properly
    echo 3. لا توجد تطبيقات أخرى تستخدم نفس المنفذ
    echo 3. No other applications are using the same port
    echo.
    pause
    exit /b 1
)

echo.
echo تم إغلاق التطبيق
echo Application closed
pause
