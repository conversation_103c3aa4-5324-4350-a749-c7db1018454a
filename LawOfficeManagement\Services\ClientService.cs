using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Services
{
    public class ClientService
    {
        private readonly DatabaseService _databaseService;
        private readonly ErrorLogService _errorLogService;
        
        public ClientService()
        {
            _databaseService = DatabaseService.Instance;
            _errorLogService = new ErrorLogService();
        }
        
        public async Task<List<Client>> GetAllClientsAsync()
        {
            try
            {
                var query = @"
                    SELECT 
                        ClientId, ClientType, FullName, FullNameFr, CIN, PassportNumber,
                        CompanyRegister, ICE, DateOfBirth, Gender, Nationality, Address,
                        City, Phone, Mobile, Email, Fax, Profession, Notes, IsActive,
                        CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                    FROM Clients
                    WHERE IsActive = 1
                    ORDER BY FullName";
                
                var dataTable = await _databaseService.ExecuteQueryAsync(query);
                var clients = new List<Client>();
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var client = MapDataRowToClient(row);
                    clients.Add(client);
                }
                
                return clients;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ClientService.GetAllClients", ex);
                throw;
            }
        }
        
        public async Task<Client?> GetClientByIdAsync(int clientId)
        {
            try
            {
                var query = @"
                    SELECT 
                        ClientId, ClientType, FullName, FullNameFr, CIN, PassportNumber,
                        CompanyRegister, ICE, DateOfBirth, Gender, Nationality, Address,
                        City, Phone, Mobile, Email, Fax, Profession, Notes, IsActive,
                        CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                    FROM Clients
                    WHERE ClientId = @ClientId AND IsActive = 1";
                
                var parameters = new[] { new SqlParameter("@ClientId", clientId) };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                
                if (dataTable.Rows.Count == 0)
                    return null;
                
                return MapDataRowToClient(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ClientService.GetClientById", ex);
                throw;
            }
        }
        
        public async Task<int> CreateClientAsync(Client client, int createdBy)
        {
            try
            {
                var query = @"
                    INSERT INTO Clients (
                        ClientType, FullName, FullNameFr, CIN, PassportNumber, CompanyRegister,
                        ICE, DateOfBirth, Gender, Nationality, Address, City, Phone, Mobile,
                        Email, Fax, Profession, Notes, IsActive, CreatedDate, CreatedBy
                    ) VALUES (
                        @ClientType, @FullName, @FullNameFr, @CIN, @PassportNumber, @CompanyRegister,
                        @ICE, @DateOfBirth, @Gender, @Nationality, @Address, @City, @Phone, @Mobile,
                        @Email, @Fax, @Profession, @Notes, 1, GETDATE(), @CreatedBy
                    );
                    SELECT SCOPE_IDENTITY();";
                
                var parameters = new[]
                {
                    new SqlParameter("@ClientType", client.ClientType.ToString()),
                    new SqlParameter("@FullName", client.FullName),
                    new SqlParameter("@FullNameFr", (object?)client.FullNameFr ?? DBNull.Value),
                    new SqlParameter("@CIN", (object?)client.CIN ?? DBNull.Value),
                    new SqlParameter("@PassportNumber", (object?)client.PassportNumber ?? DBNull.Value),
                    new SqlParameter("@CompanyRegister", (object?)client.CompanyRegister ?? DBNull.Value),
                    new SqlParameter("@ICE", (object?)client.ICE ?? DBNull.Value),
                    new SqlParameter("@DateOfBirth", (object?)client.DateOfBirth ?? DBNull.Value),
                    new SqlParameter("@Gender", (object?)client.Gender ?? DBNull.Value),
                    new SqlParameter("@Nationality", client.Nationality),
                    new SqlParameter("@Address", (object?)client.Address ?? DBNull.Value),
                    new SqlParameter("@City", (object?)client.City ?? DBNull.Value),
                    new SqlParameter("@Phone", (object?)client.Phone ?? DBNull.Value),
                    new SqlParameter("@Mobile", (object?)client.Mobile ?? DBNull.Value),
                    new SqlParameter("@Email", (object?)client.Email ?? DBNull.Value),
                    new SqlParameter("@Fax", (object?)client.Fax ?? DBNull.Value),
                    new SqlParameter("@Profession", (object?)client.Profession ?? DBNull.Value),
                    new SqlParameter("@Notes", (object?)client.Notes ?? DBNull.Value),
                    new SqlParameter("@CreatedBy", createdBy)
                };
                
                var result = await _databaseService.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ClientService.CreateClient", ex);
                throw;
            }
        }
        
        public async Task<bool> UpdateClientAsync(Client client, int modifiedBy)
        {
            try
            {
                var query = @"
                    UPDATE Clients SET
                        ClientType = @ClientType,
                        FullName = @FullName,
                        FullNameFr = @FullNameFr,
                        CIN = @CIN,
                        PassportNumber = @PassportNumber,
                        CompanyRegister = @CompanyRegister,
                        ICE = @ICE,
                        DateOfBirth = @DateOfBirth,
                        Gender = @Gender,
                        Nationality = @Nationality,
                        Address = @Address,
                        City = @City,
                        Phone = @Phone,
                        Mobile = @Mobile,
                        Email = @Email,
                        Fax = @Fax,
                        Profession = @Profession,
                        Notes = @Notes,
                        ModifiedDate = GETDATE(),
                        ModifiedBy = @ModifiedBy
                    WHERE ClientId = @ClientId AND IsActive = 1";
                
                var parameters = new[]
                {
                    new SqlParameter("@ClientId", client.ClientId),
                    new SqlParameter("@ClientType", client.ClientType.ToString()),
                    new SqlParameter("@FullName", client.FullName),
                    new SqlParameter("@FullNameFr", (object?)client.FullNameFr ?? DBNull.Value),
                    new SqlParameter("@CIN", (object?)client.CIN ?? DBNull.Value),
                    new SqlParameter("@PassportNumber", (object?)client.PassportNumber ?? DBNull.Value),
                    new SqlParameter("@CompanyRegister", (object?)client.CompanyRegister ?? DBNull.Value),
                    new SqlParameter("@ICE", (object?)client.ICE ?? DBNull.Value),
                    new SqlParameter("@DateOfBirth", (object?)client.DateOfBirth ?? DBNull.Value),
                    new SqlParameter("@Gender", (object?)client.Gender ?? DBNull.Value),
                    new SqlParameter("@Nationality", client.Nationality),
                    new SqlParameter("@Address", (object?)client.Address ?? DBNull.Value),
                    new SqlParameter("@City", (object?)client.City ?? DBNull.Value),
                    new SqlParameter("@Phone", (object?)client.Phone ?? DBNull.Value),
                    new SqlParameter("@Mobile", (object?)client.Mobile ?? DBNull.Value),
                    new SqlParameter("@Email", (object?)client.Email ?? DBNull.Value),
                    new SqlParameter("@Fax", (object?)client.Fax ?? DBNull.Value),
                    new SqlParameter("@Profession", (object?)client.Profession ?? DBNull.Value),
                    new SqlParameter("@Notes", (object?)client.Notes ?? DBNull.Value),
                    new SqlParameter("@ModifiedBy", modifiedBy)
                };
                
                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ClientService.UpdateClient", ex);
                throw;
            }
        }
        
        public async Task<bool> DeleteClientAsync(int clientId, int deletedBy)
        {
            try
            {
                var query = @"
                    UPDATE Clients SET
                        IsActive = 0,
                        ModifiedDate = GETDATE(),
                        ModifiedBy = @DeletedBy
                    WHERE ClientId = @ClientId";
                
                var parameters = new[]
                {
                    new SqlParameter("@ClientId", clientId),
                    new SqlParameter("@DeletedBy", deletedBy)
                };
                
                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ClientService.DeleteClient", ex);
                throw;
            }
        }
        
        public async Task<List<Client>> SearchClientsAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT 
                        ClientId, ClientType, FullName, FullNameFr, CIN, PassportNumber,
                        CompanyRegister, ICE, DateOfBirth, Gender, Nationality, Address,
                        City, Phone, Mobile, Email, Fax, Profession, Notes, IsActive,
                        CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                    FROM Clients
                    WHERE IsActive = 1 AND (
                        FullName LIKE @SearchTerm OR
                        CIN LIKE @SearchTerm OR
                        Phone LIKE @SearchTerm OR
                        Mobile LIKE @SearchTerm OR
                        Email LIKE @SearchTerm
                    )
                    ORDER BY FullName";
                
                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                var clients = new List<Client>();
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var client = MapDataRowToClient(row);
                    clients.Add(client);
                }
                
                return clients;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ClientService.SearchClients", ex);
                throw;
            }
        }
        
        private Client MapDataRowToClient(DataRow row)
        {
            return new Client
            {
                ClientId = (int)row["ClientId"],
                ClientType = Enum.Parse<ClientType>(row["ClientType"].ToString()!),
                FullName = row["FullName"].ToString()!,
                FullNameFr = row["FullNameFr"] as string,
                CIN = row["CIN"] as string,
                PassportNumber = row["PassportNumber"] as string,
                CompanyRegister = row["CompanyRegister"] as string,
                ICE = row["ICE"] as string,
                DateOfBirth = row["DateOfBirth"] as DateTime?,
                Gender = row["Gender"] as string,
                Nationality = row["Nationality"].ToString()!,
                Address = row["Address"] as string,
                City = row["City"] as string,
                Phone = row["Phone"] as string,
                Mobile = row["Mobile"] as string,
                Email = row["Email"] as string,
                Fax = row["Fax"] as string,
                Profession = row["Profession"] as string,
                Notes = row["Notes"] as string,
                IsActive = (bool)row["IsActive"],
                CreatedDate = (DateTime)row["CreatedDate"],
                CreatedBy = row["CreatedBy"] as int?,
                ModifiedDate = row["ModifiedDate"] as DateTime?,
                ModifiedBy = row["ModifiedBy"] as int?
            };
        }
    }
}
