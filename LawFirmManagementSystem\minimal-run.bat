@echo off
echo ========================================
echo تشغيل النظام المبسط
echo Running Minimal System
echo ========================================
echo.

echo إنشاء مشروع مبسط مؤقت...
echo Creating temporary minimal project...

echo تنظيف المشروع...
echo Cleaning project...
dotnet clean

echo إزالة الملفات المعقدة مؤقتاً...
echo Temporarily removing complex files...

if exist "Models\Entities\CaseExpense.cs" (
    ren "Models\Entities\CaseExpense.cs" "CaseExpense.cs.bak"
)

if exist "Models\Entities\CaseTask.cs" (
    ren "Models\Entities\CaseTask.cs" "CaseTask.cs.bak"
)

if exist "Models\Entities\Court.cs" (
    ren "Models\Entities\Court.cs" "Court.cs.bak"
)

if exist "Models\Entities\Hearing.cs" (
    ren "Models\Entities\Hearing.cs" "Hearing.cs.bak"
)

if exist "Models\Entities\ClientContact.cs" (
    ren "Models\Entities\ClientContact.cs" "ClientContact.cs.bak"
)

if exist "Models\Entities\Appointment.cs" (
    ren "Models\Entities\Appointment.cs" "Appointment.cs.bak"
)

if exist "Models\Entities\CaseType.cs" (
    ren "Models\Entities\CaseType.cs" "CaseType.cs.bak"
)

if exist "Models\Entities\User.cs" (
    ren "Models\Entities\User.cs" "User.cs.bak"
)

echo استعادة الحزم...
echo Restoring packages...
dotnet restore

echo محاولة البناء...
echo Attempting build...
dotnet build --configuration Debug

if %errorlevel% equ 0 (
    echo ========================================
    echo ✓ نجح البناء المبسط!
    echo ✓ Minimal build successful!
    echo ========================================
    echo.
    echo تشغيل التطبيق...
    echo Running application...
    echo.
    echo بيانات تسجيل الدخول:
    echo Login credentials:
    echo Username: admin
    echo Password: Admin@123
    echo.
    dotnet run
) else (
    echo ✗ فشل البناء المبسط أيضاً
    echo ✗ Minimal build also failed
)

echo.
echo استعادة الملفات...
echo Restoring files...

if exist "Models\Entities\CaseExpense.cs.bak" (
    ren "Models\Entities\CaseExpense.cs.bak" "CaseExpense.cs"
)

if exist "Models\Entities\CaseTask.cs.bak" (
    ren "Models\Entities\CaseTask.cs.bak" "CaseTask.cs"
)

if exist "Models\Entities\Court.cs.bak" (
    ren "Models\Entities\Court.cs.bak" "Court.cs"
)

if exist "Models\Entities\Hearing.cs.bak" (
    ren "Models\Entities\Hearing.cs.bak" "Hearing.cs"
)

if exist "Models\Entities\ClientContact.cs.bak" (
    ren "Models\Entities\ClientContact.cs.bak" "ClientContact.cs"
)

if exist "Models\Entities\Appointment.cs.bak" (
    ren "Models\Entities\Appointment.cs.bak" "Appointment.cs"
)

if exist "Models\Entities\CaseType.cs.bak" (
    ren "Models\Entities\CaseType.cs.bak" "CaseType.cs"
)

if exist "Models\Entities\User.cs.bak" (
    ren "Models\Entities\User.cs.bak" "User.cs"
)

pause
