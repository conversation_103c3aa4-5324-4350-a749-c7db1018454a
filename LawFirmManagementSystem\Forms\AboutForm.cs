using System;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Forms
{
    /// <summary>
    /// نموذج حول البرنامج
    /// About form
    /// </summary>
    public partial class AboutForm : Form
    {
        private PictureBox picLogo;
        private Label lblTitle;
        private Label lblVersion;
        private Label lblCopyright;
        private Label lblDescription;
        private Label lblDeveloper;
        private Label lblContact;
        private Button btnOK;
        private Panel pnlMain;

        public AboutForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "حول البرنامج";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Tahoma", 9F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Main panel
            pnlMain = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            
            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(210, 20),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.Transparent
            };
            
            // Title
            lblTitle = new Label
            {
                Text = "نظام إدارة مكتب المحاماة",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(460, 35),
                Location = new Point(20, 110)
            };
            
            // Version
            lblVersion = new Label
            {
                Text = GetVersionInfo(),
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(460, 25),
                Location = new Point(20, 150)
            };
            
            // Description
            lblDescription = new Label
            {
                Text = "نظام شامل لإدارة مكاتب المحاماة يوفر جميع الأدوات اللازمة لإدارة العملاء والقضايا والمواعيد والوثائق والأمور المالية بكفاءة عالية.",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(460, 60),
                Location = new Point(20, 180)
            };
            
            // Developer
            lblDeveloper = new Label
            {
                Text = "تطوير: فريق حلول إدارة مكاتب المحاماة",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(460, 25),
                Location = new Point(20, 250)
            };
            
            // Contact
            lblContact = new Label
            {
                Text = "للدعم الفني: <EMAIL> | +212 5XX-XXXXXX",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(460, 25),
                Location = new Point(20, 275)
            };
            
            // Copyright
            lblCopyright = new Label
            {
                Text = $"© {DateTime.Now.Year} Law Firm Management Solutions. جميع الحقوق محفوظة.",
                Font = new Font("Tahoma", 8F, FontStyle.Regular),
                ForeColor = Color.FromArgb(153, 153, 153),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(460, 20),
                Location = new Point(20, 310)
            };
            
            // OK button
            btnOK = new Button
            {
                Text = "موافق",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                Size = new Size(80, 30),
                Location = new Point(210, 340),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                DialogResult = DialogResult.OK
            };
            btnOK.FlatAppearance.BorderSize = 0;
            
            // Add controls to main panel
            pnlMain.Controls.AddRange(new Control[] {
                picLogo, lblTitle, lblVersion, lblDescription,
                lblDeveloper, lblContact, lblCopyright, btnOK
            });
            
            // Add main panel to form
            this.Controls.Add(pnlMain);
            
            // Event handlers
            btnOK.Click += BtnOK_Click;
            this.KeyDown += AboutForm_KeyDown;
            
            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            // Set form icon if available
            try
            {
                var iconPath = Path.Combine(Application.StartupPath, "Resources", "Icons", "app-icon.ico");
                if (File.Exists(iconPath))
                {
                    this.Icon = new Icon(iconPath);
                }
            }
            catch { }
            
            // Set logo if available
            try
            {
                var logoPath = Path.Combine(Application.StartupPath, "Resources", "Images", "logo.png");
                if (File.Exists(logoPath))
                {
                    picLogo.Image = Image.FromFile(logoPath);
                }
                else
                {
                    // Create a simple logo placeholder
                    var bitmap = new Bitmap(80, 80);
                    using (var g = Graphics.FromImage(bitmap))
                    {
                        g.Clear(Color.FromArgb(0, 123, 255));
                        using (var brush = new SolidBrush(Color.White))
                        using (var font = new Font("Tahoma", 20F, FontStyle.Bold))
                        {
                            var text = "⚖";
                            var size = g.MeasureString(text, font);
                            var x = (80 - size.Width) / 2;
                            var y = (80 - size.Height) / 2;
                            g.DrawString(text, font, brush, x, y);
                        }
                    }
                    picLogo.Image = bitmap;
                }
            }
            catch
            {
                // Create a simple colored rectangle as fallback
                var bitmap = new Bitmap(80, 80);
                using (var g = Graphics.FromImage(bitmap))
                {
                    g.Clear(Color.FromArgb(0, 123, 255));
                }
                picLogo.Image = bitmap;
            }
            
            // Set default button
            this.AcceptButton = btnOK;
        }

        private string GetVersionInfo()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                var buildDate = GetBuildDate(assembly);
                
                return $"الإصدار {version?.ToString(3) ?? "1.0.0"} - تاريخ البناء: {buildDate:yyyy/MM/dd}";
            }
            catch
            {
                return "الإصدار 1.0.0";
            }
        }

        private DateTime GetBuildDate(Assembly assembly)
        {
            try
            {
                var attribute = assembly.GetCustomAttribute<AssemblyMetadataAttribute>();
                if (attribute != null && attribute.Key == "BuildDate")
                {
                    if (DateTime.TryParse(attribute.Value, out var buildDate))
                    {
                        return buildDate;
                    }
                }
                
                // Fallback to file creation time
                var location = assembly.Location;
                if (!string.IsNullOrEmpty(location) && File.Exists(location))
                {
                    return File.GetCreationTime(location);
                }
            }
            catch { }
            
            return DateTime.Now;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void AboutForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            
            // Add fade-in effect
            this.Opacity = 0;
            var fadeTimer = new Timer { Interval = 50 };
            fadeTimer.Tick += (s, args) =>
            {
                if (this.Opacity < 1)
                {
                    this.Opacity += 0.1;
                }
                else
                {
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
            };
            fadeTimer.Start();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                picLogo?.Image?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
