using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الجدول الزمني للقضية
    /// Case timeline entity
    /// </summary>
    [Table("CaseTimeline")]
    public class CaseTimeline : BaseEntity
    {
        [Required]
        [Column("CaseId")]
        public int CaseId { get; set; }

        [Required]
        [Column("EventDate")]
        public DateTime EventDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(200)]
        [Column("EventTitle")]
        public string EventTitle { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("EventTitleFr")]
        public string? EventTitleFr { get; set; }

        [StringLength(2000)]
        [Column("EventDescription")]
        public string? EventDescription { get; set; }

        [StringLength(2000)]
        [Column("EventDescriptionFr")]
        public string? EventDescriptionFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("EventType")]
        public string EventType { get; set; } = "عام"; // عام، جلسة، موعد، مهمة، وثيقة، دفعة

        [StringLength(50)]
        [Column("EventTypeFr")]
        public string? EventTypeFr { get; set; }

        [StringLength(50)]
        [Column("EventCategory")]
        public string? EventCategory { get; set; } // قانوني، إداري، مالي

        [StringLength(50)]
        [Column("EventCategoryFr")]
        public string? EventCategoryFr { get; set; }

        [StringLength(20)]
        [Column("Priority")]
        public string Priority { get; set; } = "متوسط"; // عالي، متوسط، منخفض

        [StringLength(20)]
        [Column("PriorityFr")]
        public string? PriorityFr { get; set; }

        [Column("RelatedEntityType")]
        public string? RelatedEntityType { get; set; } // Hearing, Appointment, Task, Document, Payment

        [Column("RelatedEntityId")]
        public int? RelatedEntityId { get; set; }

        [StringLength(200)]
        [Column("RelatedEntityName")]
        public string? RelatedEntityName { get; set; }

        [StringLength(200)]
        [Column("RelatedEntityNameFr")]
        public string? RelatedEntityNameFr { get; set; }

        [Column("IsSystemGenerated")]
        public bool IsSystemGenerated { get; set; } = false;

        [StringLength(100)]
        [Column("SystemSource")]
        public string? SystemSource { get; set; }

        [Column("IsManual")]
        public bool IsManual { get; set; } = true;

        [Column("IsImportant")]
        public bool IsImportant { get; set; } = false;

        [Column("IsMilestone")]
        public bool IsMilestone { get; set; } = false;

        [Column("IsPublic")]
        public bool IsPublic { get; set; } = true;

        [Column("IsVisible")]
        public bool IsVisible { get; set; } = true;

        [StringLength(500)]
        [Column("Participants")]
        public string? Participants { get; set; }

        [StringLength(500)]
        [Column("ParticipantsFr")]
        public string? ParticipantsFr { get; set; }

        [StringLength(200)]
        [Column("Location")]
        public string? Location { get; set; }

        [StringLength(200)]
        [Column("LocationFr")]
        public string? LocationFr { get; set; }

        [Column("Duration")]
        public int? Duration { get; set; } // بالدقائق

        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مكتمل"; // مجدول، جاري، مكتمل، ملغي

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [StringLength(2000)]
        [Column("Outcome")]
        public string? Outcome { get; set; }

        [StringLength(2000)]
        [Column("OutcomeFr")]
        public string? OutcomeFr { get; set; }

        [StringLength(2000)]
        [Column("NextSteps")]
        public string? NextSteps { get; set; }

        [StringLength(2000)]
        [Column("NextStepsFr")]
        public string? NextStepsFr { get; set; }

        [Column("NextEventDate")]
        public DateTime? NextEventDate { get; set; }

        [StringLength(200)]
        [Column("NextEventTitle")]
        public string? NextEventTitle { get; set; }

        [StringLength(200)]
        [Column("NextEventTitleFr")]
        public string? NextEventTitleFr { get; set; }

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [StringLength(500)]
        [Column("AttachmentPaths")]
        public string? AttachmentPaths { get; set; } // JSON array

        [Column("HasAttachments")]
        public bool HasAttachments { get; set; } = false;

        [Column("AttachmentCount")]
        public int AttachmentCount { get; set; } = 0;

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; }

        [StringLength(50)]
        [Column("Icon")]
        public string? Icon { get; set; }

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        [Column("IsTemplate")]
        public bool IsTemplate { get; set; } = false;

        [StringLength(100)]
        [Column("TemplateName")]
        public string? TemplateName { get; set; }

        [StringLength(100)]
        [Column("TemplateNameFr")]
        public string? TemplateNameFr { get; set; }

        [Column("TemplateId")]
        public int? TemplateId { get; set; }

        [Column("IsRecurring")]
        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        [Column("RecurrencePattern")]
        public string? RecurrencePattern { get; set; }

        [Column("RecurrenceInterval")]
        public int? RecurrenceInterval { get; set; }

        [Column("RecurrenceEndDate")]
        public DateTime? RecurrenceEndDate { get; set; }

        [Column("ParentEventId")]
        public int? ParentEventId { get; set; }

        [Column("IsChildEvent")]
        public bool IsChildEvent { get; set; } = false;

        [Column("ChildEventCount")]
        public int ChildEventCount { get; set; } = 0;

        [Column("ViewCount")]
        public int ViewCount { get; set; } = 0;

        [Column("LastViewDate")]
        public DateTime? LastViewDate { get; set; }

        [Column("LastViewBy")]
        public int? LastViewBy { get; set; }

        [Column("IsArchived")]
        public bool IsArchived { get; set; } = false;

        [Column("ArchiveDate")]
        public DateTime? ArchiveDate { get; set; }

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase Case { get; set; } = null!;

        [ForeignKey("ParentEventId")]
        public virtual CaseTimeline? ParentEvent { get; set; }

        [ForeignKey("TemplateId")]
        public virtual CaseTimeline? Template { get; set; }

        /// <summary>
        /// الحصول على عنوان الحدث حسب اللغة
        /// Get event title by language
        /// </summary>
        public string GetEventTitle(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(EventTitleFr) 
                ? EventTitleFr 
                : EventTitle;
        }

        /// <summary>
        /// الحصول على وصف الحدث حسب اللغة
        /// Get event description by language
        /// </summary>
        public string GetEventDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(EventDescriptionFr) 
                ? EventDescriptionFr 
                : EventDescription ?? string.Empty;
        }

        /// <summary>
        /// الحصول على نوع الحدث حسب اللغة
        /// Get event type by language
        /// </summary>
        public string GetEventType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(EventTypeFr) 
                ? EventTypeFr 
                : EventType;
        }

        /// <summary>
        /// الحصول على حالة الحدث حسب اللغة
        /// Get event status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// الحصول على النتيجة حسب اللغة
        /// Get outcome by language
        /// </summary>
        public string GetOutcome(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(OutcomeFr) 
                ? OutcomeFr 
                : Outcome ?? string.Empty;
        }

        /// <summary>
        /// التحقق من كون الحدث في المستقبل
        /// Check if event is in the future
        /// </summary>
        public bool IsFutureEvent()
        {
            return EventDate > DateTime.Now;
        }

        /// <summary>
        /// التحقق من كون الحدث اليوم
        /// Check if event is today
        /// </summary>
        public bool IsToday()
        {
            return EventDate.Date == DateTime.Now.Date;
        }

        /// <summary>
        /// التحقق من كون الحدث هذا الأسبوع
        /// Check if event is this week
        /// </summary>
        public bool IsThisWeek()
        {
            var startOfWeek = DateTime.Now.Date.AddDays(-(int)DateTime.Now.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(7);
            return EventDate.Date >= startOfWeek && EventDate.Date < endOfWeek;
        }

        /// <summary>
        /// التحقق من كون الحدث هذا الشهر
        /// Check if event is this month
        /// </summary>
        public bool IsThisMonth()
        {
            return EventDate.Year == DateTime.Now.Year && EventDate.Month == DateTime.Now.Month;
        }

        /// <summary>
        /// الحصول على الوقت النسبي للحدث
        /// Get relative time for event
        /// </summary>
        public string GetRelativeTime(string language = "ar")
        {
            var diff = EventDate - DateTime.Now;
            var absDiff = Math.Abs(diff.TotalDays);

            if (language.ToLower() == "fr")
            {
                if (absDiff < 1)
                {
                    if (diff.TotalHours > 0)
                        return $"Dans {Math.Ceiling(diff.TotalHours)} heures";
                    else if (diff.TotalHours < 0)
                        return $"Il y a {Math.Ceiling(Math.Abs(diff.TotalHours))} heures";
                    else
                        return "Maintenant";
                }
                else if (absDiff < 7)
                {
                    return diff.TotalDays > 0 
                        ? $"Dans {Math.Ceiling(diff.TotalDays)} jours"
                        : $"Il y a {Math.Ceiling(absDiff)} jours";
                }
                else if (absDiff < 30)
                {
                    var weeks = Math.Ceiling(absDiff / 7);
                    return diff.TotalDays > 0 
                        ? $"Dans {weeks} semaines"
                        : $"Il y a {weeks} semaines";
                }
                else
                {
                    var months = Math.Ceiling(absDiff / 30);
                    return diff.TotalDays > 0 
                        ? $"Dans {months} mois"
                        : $"Il y a {months} mois";
                }
            }
            else
            {
                if (absDiff < 1)
                {
                    if (diff.TotalHours > 0)
                        return $"خلال {Math.Ceiling(diff.TotalHours)} ساعة";
                    else if (diff.TotalHours < 0)
                        return $"منذ {Math.Ceiling(Math.Abs(diff.TotalHours))} ساعة";
                    else
                        return "الآن";
                }
                else if (absDiff < 7)
                {
                    return diff.TotalDays > 0 
                        ? $"خلال {Math.Ceiling(diff.TotalDays)} أيام"
                        : $"منذ {Math.Ceiling(absDiff)} أيام";
                }
                else if (absDiff < 30)
                {
                    var weeks = Math.Ceiling(absDiff / 7);
                    return diff.TotalDays > 0 
                        ? $"خلال {weeks} أسابيع"
                        : $"منذ {weeks} أسابيع";
                }
                else
                {
                    var months = Math.Ceiling(absDiff / 30);
                    return diff.TotalDays > 0 
                        ? $"خلال {months} أشهر"
                        : $"منذ {months} أشهر";
                }
            }
        }

        /// <summary>
        /// تسجيل عرض الحدث
        /// Record event view
        /// </summary>
        public void RecordView(int? viewedBy = null)
        {
            ViewCount++;
            LastViewDate = DateTime.Now;
            LastViewBy = viewedBy;
        }

        /// <summary>
        /// أرشفة الحدث
        /// Archive event
        /// </summary>
        public void ArchiveEvent()
        {
            IsArchived = true;
            ArchiveDate = DateTime.Now;
        }

        /// <summary>
        /// استعادة الحدث من الأرشيف
        /// Restore event from archive
        /// </summary>
        public void RestoreEvent()
        {
            IsArchived = false;
            ArchiveDate = null;
        }

        /// <summary>
        /// إنشاء حدث من قالب
        /// Create event from template
        /// </summary>
        public static CaseTimeline CreateFromTemplate(CaseTimeline template, int caseId, DateTime eventDate)
        {
            return new CaseTimeline
            {
                CaseId = caseId,
                EventDate = eventDate,
                EventTitle = template.EventTitle,
                EventTitleFr = template.EventTitleFr,
                EventDescription = template.EventDescription,
                EventDescriptionFr = template.EventDescriptionFr,
                EventType = template.EventType,
                EventTypeFr = template.EventTypeFr,
                EventCategory = template.EventCategory,
                EventCategoryFr = template.EventCategoryFr,
                Priority = template.Priority,
                PriorityFr = template.PriorityFr,
                Duration = template.Duration,
                Color = template.Color,
                Icon = template.Icon,
                TemplateId = template.Id,
                IsTemplate = false
            };
        }
    }
}
