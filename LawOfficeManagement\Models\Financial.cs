using System;
using System.ComponentModel.DataAnnotations;

namespace LawOfficeManagement.Models
{
    public class CaseExpense
    {
        public int ExpenseId { get; set; }
        
        [Required]
        public int CaseId { get; set; }
        
        [Required]
        public int ExpenseTypeId { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public DateTime ExpenseDate { get; set; }
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaymentDate { get; set; }
        
        [StringLength(50)]
        public string? PaymentMethod { get; set; }
        
        public byte[]? Receipt { get; set; }
        
        public string? Notes { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual LegalCase? LegalCase { get; set; }
        public virtual ExpenseType? ExpenseType { get; set; }
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
    }
    
    public class ExpenseType
    {
        public int ExpenseTypeId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string TypeName { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? TypeNameFr { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual ICollection<CaseExpense> CaseExpenses { get; set; } = new List<CaseExpense>();
    }
    
    public class Invoice
    {
        public int InvoiceId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public int CaseId { get; set; }
        
        [Required]
        public int ClientId { get; set; }
        
        [Required]
        public DateTime InvoiceDate { get; set; }
        
        public DateTime? DueDate { get; set; }
        
        [Required]
        public decimal TotalAmount { get; set; }
        
        public decimal PaidAmount { get; set; } = 0;
        
        [StringLength(50)]
        public string Status { get; set; } = "غير مدفوعة";
        
        public string? Notes { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual LegalCase? LegalCase { get; set; }
        public virtual Client? Client { get; set; }
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
        public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
    }
    
    public class InvoiceDetail
    {
        public int InvoiceDetailId { get; set; }
        
        [Required]
        public int InvoiceId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public decimal Quantity { get; set; } = 1;
        
        [Required]
        public decimal UnitPrice { get; set; }
        
        [Required]
        public decimal TotalPrice { get; set; }
        
        // Navigation properties
        public virtual Invoice? Invoice { get; set; }
    }
}
