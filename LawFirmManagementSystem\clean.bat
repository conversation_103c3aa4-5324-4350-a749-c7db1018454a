@echo off
echo ========================================
echo تنظيف نظام إدارة مكتب المحاماة
echo Cleaning Law Firm Management System
echo ========================================
echo.

echo اختر نوع التنظيف:
echo Choose cleaning type:
echo 1. تنظيف سريع (Quick Clean)
echo 2. تنظيف شامل (Deep Clean)
echo 3. تنظيف ملفات البناء (Build Clean)
echo 4. تنظيف ملفات الاختبار (Test Clean)
echo 5. تنظيف ملفات النشر (Publish Clean)
echo 6. تنظيف السجلات (Logs Clean)
echo 7. تنظيف كامل (Full Clean)
echo 8. إعادة تعيين المشروع (Reset Project)
echo.

set /p CHOICE="اختر رقم (1-8) / Choose number (1-8): "

if "%CHOICE%"=="1" goto quick_clean
if "%CHOICE%"=="2" goto deep_clean
if "%CHOICE%"=="3" goto build_clean
if "%CHOICE%"=="4" goto test_clean
if "%CHOICE%"=="5" goto publish_clean
if "%CHOICE%"=="6" goto logs_clean
if "%CHOICE%"=="7" goto full_clean
if "%CHOICE%"=="8" goto reset_project
goto invalid_choice

:quick_clean
echo تنظيف سريع...
echo Quick cleaning...
echo.

echo تنظيف ملفات البناء...
echo Cleaning build files...
dotnet clean

echo حذف مجلدات bin و obj...
echo Removing bin and obj folders...
for /d /r . %%d in (bin,obj) do @if exist "%%d" rd /s /q "%%d"

echo ✓ تم التنظيف السريع
echo ✓ Quick clean completed

goto end

:deep_clean
echo تنظيف شامل...
echo Deep cleaning...
echo.

echo تنظيف ملفات البناء...
echo Cleaning build files...
dotnet clean

echo حذف مجلدات bin و obj...
echo Removing bin and obj folders...
for /d /r . %%d in (bin,obj) do @if exist "%%d" rd /s /q "%%d"

echo حذف ملفات NuGet المؤقتة...
echo Removing NuGet temp files...
dotnet nuget locals all --clear

echo حذف ملفات Visual Studio المؤقتة...
echo Removing Visual Studio temp files...
if exist ".vs" rd /s /q ".vs"
del /q /s *.user 2>nul
del /q /s *.suo 2>nul

echo ✓ تم التنظيف الشامل
echo ✓ Deep clean completed

goto end

:build_clean
echo تنظيف ملفات البناء...
echo Cleaning build files...
echo.

dotnet clean
for /d /r . %%d in (bin,obj) do @if exist "%%d" rd /s /q "%%d"

echo ✓ تم تنظيف ملفات البناء
echo ✓ Build files cleaned

goto end

:test_clean
echo تنظيف ملفات الاختبار...
echo Cleaning test files...
echo.

if exist "TestResults" (
    echo حذف نتائج الاختبارات...
    echo Removing test results...
    rd /s /q "TestResults"
)

if exist "coverage.json" del "coverage.json"
if exist "coverage.xml" del "coverage.xml"

echo ✓ تم تنظيف ملفات الاختبار
echo ✓ Test files cleaned

goto end

:publish_clean
echo تنظيف ملفات النشر...
echo Cleaning publish files...
echo.

if exist "publish" (
    echo حذف مجلد النشر...
    echo Removing publish folder...
    rd /s /q "publish"
)

del /q *.zip 2>nul

echo ✓ تم تنظيف ملفات النشر
echo ✓ Publish files cleaned

goto end

:logs_clean
echo تنظيف السجلات...
echo Cleaning logs...
echo.

if exist "Logs" (
    echo حذف ملفات السجلات...
    echo Removing log files...
    del /q "Logs\*.*" 2>nul
    echo تم الاحتفاظ بمجلد Logs فارغاً
    echo Logs folder kept empty
)

if exist "*.log" del /q "*.log"

echo ✓ تم تنظيف السجلات
echo ✓ Logs cleaned

goto end

:full_clean
echo تنظيف كامل...
echo Full cleaning...
echo.

echo تحذير: سيتم حذف جميع الملفات المؤقتة والمولدة
echo Warning: All temporary and generated files will be deleted
set /p CONFIRM="هل أنت متأكد؟ (y/n) / Are you sure? (y/n): "
if /i not "%CONFIRM%"=="y" goto end

echo تنظيف ملفات البناء...
echo Cleaning build files...
dotnet clean
for /d /r . %%d in (bin,obj) do @if exist "%%d" rd /s /q "%%d"

echo تنظيف ملفات NuGet...
echo Cleaning NuGet files...
dotnet nuget locals all --clear

echo تنظيف ملفات Visual Studio...
echo Cleaning Visual Studio files...
if exist ".vs" rd /s /q ".vs"
del /q /s *.user 2>nul
del /q /s *.suo 2>nul

echo تنظيف ملفات الاختبار...
echo Cleaning test files...
if exist "TestResults" rd /s /q "TestResults"

echo تنظيف ملفات النشر...
echo Cleaning publish files...
if exist "publish" rd /s /q "publish"

echo تنظيف السجلات...
echo Cleaning logs...
if exist "Logs" del /q "Logs\*.*" 2>nul

echo تنظيف ملفات مؤقتة أخرى...
echo Cleaning other temp files...
del /q *.tmp 2>nul
del /q *.temp 2>nul
del /q *.log 2>nul
del /q *.zip 2>nul

echo ✓ تم التنظيف الكامل
echo ✓ Full clean completed

goto end

:reset_project
echo إعادة تعيين المشروع...
echo Resetting project...
echo.

echo تحذير: سيتم إعادة تعيين المشروع إلى حالته الأولية
echo Warning: Project will be reset to initial state
echo هذا سيحذف جميع الملفات المولدة والإعدادات المحلية
echo This will delete all generated files and local settings
set /p CONFIRM="هل أنت متأكد؟ (y/n) / Are you sure? (y/n): "
if /i not "%CONFIRM%"=="y" goto end

echo تنظيف كامل...
echo Full cleaning...
dotnet clean
for /d /r . %%d in (bin,obj) do @if exist "%%d" rd /s /q "%%d"
dotnet nuget locals all --clear

echo حذف الملفات المولدة...
echo Removing generated files...
if exist ".vs" rd /s /q ".vs"
if exist "TestResults" rd /s /q "TestResults"
if exist "publish" rd /s /q "publish"
if exist "Logs" del /q "Logs\*.*" 2>nul
if exist "Documents" del /q "Documents\*.*" 2>nul
if exist "Backups" del /q "Backups\*.*" 2>nul

echo حذف ملفات الإعدادات المحلية...
echo Removing local settings...
del /q /s *.user 2>nul
del /q /s *.suo 2>nul
if exist "appsettings.Local.json" del "appsettings.Local.json"

echo استعادة الحزم...
echo Restoring packages...
dotnet restore

echo إعادة بناء المشروع...
echo Rebuilding project...
dotnet build

echo ✓ تم إعادة تعيين المشروع
echo ✓ Project reset completed

goto end

:invalid_choice
echo اختيار غير صحيح
echo Invalid choice

:end
echo.

echo معلومات إضافية:
echo Additional information:
echo.

echo للتنظيف اليدوي:
echo For manual cleaning:
echo - حذف مجلدات bin/obj: for /d /r . %%d in (bin,obj) do @if exist "%%d" rd /s /q "%%d"
echo - تنظيف NuGet: dotnet nuget locals all --clear
echo - تنظيف .NET: dotnet clean
echo.

echo للحصول على مساحة القرص المحررة:
echo To see freed disk space:
echo dir /s | find "bytes"
echo.

pause
