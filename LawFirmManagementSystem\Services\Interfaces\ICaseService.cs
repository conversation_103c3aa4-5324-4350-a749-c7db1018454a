using LawFirmManagementSystem.Models.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة القضايا
    /// Case service interface
    /// </summary>
    public interface ICaseService
    {
        // Case Management
        Task<LegalCase?> GetCaseByIdAsync(int id);
        Task<LegalCase?> GetCaseByReferenceAsync(string reference);
        Task<LegalCase?> GetCaseByOfficeReferenceAsync(string officeReference);
        Task<LegalCase?> GetCaseByCourtNumberAsync(string courtNumber);
        Task<IEnumerable<LegalCase>> GetAllCasesAsync();
        Task<IEnumerable<LegalCase>> GetActiveCasesAsync();
        Task<LegalCase> CreateCaseAsync(LegalCase legalCase);
        Task<LegalCase> UpdateCaseAsync(LegalCase legalCase);
        Task<bool> DeleteCaseAsync(int id);
        Task<bool> CloseCaseAsync(int id, string outcome, bool isSuccessful, int? closedBy = null);
        Task<bool> ReopenCaseAsync(int id, int? reopenedBy = null);

        // Case Information
        Task<bool> UpdateCaseBasicInfoAsync(int caseId, string title, string? titleFr, string? description, string? descriptionFr);
        Task<bool> UpdateCaseStatusAsync(int caseId, string status, string? statusFr = null);
        Task<bool> UpdateCasePriorityAsync(int caseId, string priority, string? priorityFr = null);
        Task<bool> AssignCaseToLawyerAsync(int caseId, int lawyerId);
        Task<bool> AssignSecondaryCaseToLawyerAsync(int caseId, int? secondaryLawyerId);
        Task<bool> UpdateCaseCourtInfoAsync(int caseId, int? courtId, string? courtCaseNumber, string? courtDegree);

        // Case Financial Information
        Task<decimal> GetCaseTotalFeesAsync(int caseId);
        Task<decimal> GetCasePaidFeesAsync(int caseId);
        Task<decimal> GetCaseRemainingFeesAsync(int caseId);
        Task<decimal> GetCaseTotalExpensesAsync(int caseId);
        Task<bool> UpdateCaseFeesAsync(int caseId, decimal totalFees);
        Task<bool> AddCasePaymentAsync(int caseId, decimal amount);
        Task<bool> AddCaseExpenseAsync(int caseId, CaseExpense expense);

        // Case Opponents
        Task<IEnumerable<Opponent>> GetCaseOpponentsAsync(int caseId);
        Task<Opponent> AddCaseOpponentAsync(Opponent opponent);
        Task<Opponent> UpdateCaseOpponentAsync(Opponent opponent);
        Task<bool> RemoveCaseOpponentAsync(int opponentId);

        // Case Hearings
        Task<IEnumerable<Hearing>> GetCaseHearingsAsync(int caseId);
        Task<IEnumerable<Hearing>> GetCaseUpcomingHearingsAsync(int caseId);
        Task<Hearing?> GetNextCaseHearingAsync(int caseId);
        Task<Hearing> AddCaseHearingAsync(Hearing hearing);
        Task<Hearing> UpdateCaseHearingAsync(Hearing hearing);
        Task<bool> CancelCaseHearingAsync(int hearingId, string reason);

        // Case Appointments
        Task<IEnumerable<Appointment>> GetCaseAppointmentsAsync(int caseId);
        Task<IEnumerable<Appointment>> GetCaseUpcomingAppointmentsAsync(int caseId);
        Task<Appointment> AddCaseAppointmentAsync(Appointment appointment);
        Task<Appointment> UpdateCaseAppointmentAsync(Appointment appointment);

        // Case Documents
        Task<IEnumerable<Document>> GetCaseDocumentsAsync(int caseId);
        Task<Document> AddCaseDocumentAsync(Document document);
        Task<Document> UpdateCaseDocumentAsync(Document document);
        Task<bool> RemoveCaseDocumentAsync(int documentId);
        Task<IEnumerable<Document>> GetCaseDocumentsByTypeAsync(int caseId, string documentType);

        // Case Notes
        Task<IEnumerable<CaseNote>> GetCaseNotesAsync(int caseId);
        Task<CaseNote> AddCaseNoteAsync(CaseNote note);
        Task<CaseNote> UpdateCaseNoteAsync(CaseNote note);
        Task<bool> DeleteCaseNoteAsync(int noteId);
        Task<IEnumerable<CaseNote>> GetCaseNotesByTypeAsync(int caseId, string noteType);

        // Case Tasks
        Task<IEnumerable<CaseTask>> GetCaseTasksAsync(int caseId);
        Task<IEnumerable<CaseTask>> GetCasePendingTasksAsync(int caseId);
        Task<CaseTask> AddCaseTaskAsync(CaseTask task);
        Task<CaseTask> UpdateCaseTaskAsync(CaseTask task);
        Task<bool> CompleteCaseTaskAsync(int taskId, string? notes = null);
        Task<bool> CancelCaseTaskAsync(int taskId, string reason);

        // Case Timeline
        Task<IEnumerable<CaseTimeline>> GetCaseTimelineAsync(int caseId);
        Task<CaseTimeline> AddCaseTimelineEventAsync(CaseTimeline timelineEvent);
        Task<CaseTimeline> UpdateCaseTimelineEventAsync(CaseTimeline timelineEvent);
        Task<bool> DeleteCaseTimelineEventAsync(int eventId);

        // Case Expenses
        Task<IEnumerable<CaseExpense>> GetCaseExpensesAsync(int caseId);
        Task<CaseExpense> AddCaseExpenseAsync(CaseExpense expense);
        Task<CaseExpense> UpdateCaseExpenseAsync(CaseExpense expense);
        Task<bool> PayCaseExpenseAsync(int expenseId, string paymentMethod, string? paymentReference = null);
        Task<bool> ApproveCaseExpenseAsync(int expenseId, int approvedBy, string? notes = null);

        // Case Invoices
        Task<IEnumerable<Invoice>> GetCaseInvoicesAsync(int caseId);
        Task<Invoice> CreateCaseInvoiceAsync(int caseId, Invoice invoice);
        Task<decimal> GetCaseInvoicedAmountAsync(int caseId);
        Task<decimal> GetCaseUnpaidAmountAsync(int caseId);

        // Case Statistics
        Task<int> GetTotalCasesCountAsync();
        Task<int> GetActiveCasesCountAsync();
        Task<int> GetClosedCasesCountAsync();
        Task<int> GetUrgentCasesCountAsync();
        Task<IEnumerable<LegalCase>> GetRecentCasesAsync(int count = 10);
        Task<IEnumerable<LegalCase>> GetCasesByLawyerAsync(int lawyerId);

        // Search and Filter
        Task<IEnumerable<LegalCase>> SearchCasesAsync(string searchTerm);
        Task<IEnumerable<LegalCase>> GetCasesByTypeAsync(int caseTypeId);
        Task<IEnumerable<LegalCase>> GetCasesByStatusAsync(string status);
        Task<IEnumerable<LegalCase>> GetCasesByPriorityAsync(string priority);
        Task<IEnumerable<LegalCase>> GetCasesByCourtAsync(int courtId);
        Task<IEnumerable<LegalCase>> GetCasesByClientAsync(int clientId);
        Task<IEnumerable<LegalCase>> GetCasesByDateRangeAsync(DateTime fromDate, DateTime toDate);

        // Case Validation
        Task<bool> IsCaseReferenceAvailableAsync(string reference, int? excludeCaseId = null);
        Task<bool> IsOfficeReferenceAvailableAsync(string officeReference, int? excludeCaseId = null);
        Task<bool> ValidateCaseDataAsync(LegalCase legalCase);

        // Case Reports
        Task<byte[]> GenerateCaseReportAsync(int caseId, string reportType, string language = "ar");
        Task<byte[]> GenerateCaseSummaryAsync(int caseId, string language = "ar");
        Task<byte[]> GenerateCaseTimelineReportAsync(int caseId, string language = "ar");
        Task<byte[]> GenerateCaseFinancialReportAsync(int caseId, string language = "ar");

        // Case Import/Export
        Task<IEnumerable<LegalCase>> ImportCasesAsync(byte[] fileData, string fileName);
        Task<byte[]> ExportCasesAsync(IEnumerable<int> caseIds, string format = "Excel");
        Task<byte[]> ExportAllCasesAsync(string format = "Excel");

        // Case Archive
        Task<bool> ArchiveCaseAsync(int caseId, string reason);
        Task<bool> UnarchiveCaseAsync(int caseId);
        Task<IEnumerable<LegalCase>> GetArchivedCasesAsync();

        // Case Types and Courts
        Task<IEnumerable<CaseType>> GetAllCaseTypesAsync();
        Task<IEnumerable<Court>> GetAllCourtsAsync();
        Task<CaseType> CreateCaseTypeAsync(CaseType caseType);
        Task<Court> CreateCourtAsync(Court court);

        // Case Reminders
        Task<IEnumerable<LegalCase>> GetCasesWithUpcomingDeadlinesAsync(int days = 7);
        Task<IEnumerable<LegalCase>> GetCasesWithExpiredDeadlinesAsync();
        Task<bool> SendCaseReminderAsync(int caseId, string reminderType);

        // Case Conflict Check
        Task<bool> CheckCaseConflictAsync(int caseId, int clientId, IEnumerable<int> opponentIds);
        Task<IEnumerable<LegalCase>> GetPotentialConflictCasesAsync(int clientId, IEnumerable<int> opponentIds);
        Task<bool> ClearCaseConflictAsync(int caseId, int clearedBy);
    }
}
