using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان الفاتورة
    /// Invoice entity
    /// </summary>
    [Table("Invoices")]
    public class Invoice : BaseEntity
    {
        [Required]
        [StringLength(50)]
        [Column("InvoiceNumber")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Column("CaseId")]
        public int? CaseId { get; set; }

        [Required]
        [Column("ClientId")]
        public int ClientId { get; set; }

        [Required]
        [Column("InvoiceDate")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Required]
        [Column("DueDate")]
        public DateTime DueDate { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Status")]
        public string Status { get; set; } = "مسودة"; // مسودة، مرسلة، مدفوعة، متأخرة، ملغية

        [StringLength(50)]
        [Column("StatusFr")]
        public string? StatusFr { get; set; }

        [Required]
        [StringLength(50)]
        [Column("InvoiceType")]
        public string InvoiceType { get; set; } = "أتعاب"; // أتعاب، مصاريف، مختلط

        [StringLength(50)]
        [Column("InvoiceTypeFr")]
        public string? InvoiceTypeFr { get; set; }

        [StringLength(500)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [Required]
        [Column("SubTotal", TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        [Column("TaxAmount", TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column("TaxRate", TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 20; // VAT 20%

        [Column("DiscountAmount", TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column("DiscountRate", TypeName = "decimal(5,2)")]
        public decimal DiscountRate { get; set; } = 0;

        [Required]
        [Column("TotalAmount", TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [Column("PaidAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [Column("RemainingAmount", TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; }

        [StringLength(50)]
        [Column("Currency")]
        public string Currency { get; set; } = "MAD";

        [Column("ExchangeRate", TypeName = "decimal(10,4)")]
        public decimal? ExchangeRate { get; set; }

        [StringLength(50)]
        [Column("PaymentTerms")]
        public string? PaymentTerms { get; set; } // فوري، 30 يوم، 60 يوم

        [StringLength(50)]
        [Column("PaymentTermsFr")]
        public string? PaymentTermsFr { get; set; }

        [StringLength(2000)]
        [Column("PaymentInstructions")]
        public string? PaymentInstructions { get; set; }

        [StringLength(2000)]
        [Column("PaymentInstructionsFr")]
        public string? PaymentInstructionsFr { get; set; }

        [StringLength(2000)]
        [Column("Terms")]
        public string? Terms { get; set; }

        [StringLength(2000)]
        [Column("TermsFr")]
        public string? TermsFr { get; set; }

        [StringLength(2000)]
        [Column("InternalNotes")]
        public string? InternalNotes { get; set; }

        [StringLength(2000)]
        [Column("InternalNotesFr")]
        public string? InternalNotesFr { get; set; }

        [Column("SentDate")]
        public DateTime? SentDate { get; set; }

        [Column("SentBy")]
        public int? SentBy { get; set; }

        [StringLength(50)]
        [Column("SentMethod")]
        public string? SentMethod { get; set; } // بريد إلكتروني، بريد عادي، يد بيد

        [StringLength(50)]
        [Column("SentMethodFr")]
        public string? SentMethodFr { get; set; }

        [Column("ViewedDate")]
        public DateTime? ViewedDate { get; set; }

        [Column("ViewCount")]
        public int ViewCount { get; set; } = 0;

        [Column("LastReminderDate")]
        public DateTime? LastReminderDate { get; set; }

        [Column("ReminderCount")]
        public int ReminderCount { get; set; } = 0;

        [Column("IsRecurring")]
        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        [Column("RecurrencePattern")]
        public string? RecurrencePattern { get; set; }

        [Column("RecurrenceInterval")]
        public int? RecurrenceInterval { get; set; }

        [Column("NextInvoiceDate")]
        public DateTime? NextInvoiceDate { get; set; }

        [Column("RecurrenceEndDate")]
        public DateTime? RecurrenceEndDate { get; set; }

        [StringLength(500)]
        [Column("PdfPath")]
        public string? PdfPath { get; set; }

        [Column("PdfGenerated")]
        public bool PdfGenerated { get; set; } = false;

        [Column("PdfGeneratedDate")]
        public DateTime? PdfGeneratedDate { get; set; }

        [StringLength(100)]
        [Column("Template")]
        public string Template { get; set; } = "Default";

        [StringLength(20)]
        [Column("Language")]
        public string Language { get; set; } = "ar";

        [Column("IncludeLogo")]
        public bool IncludeLogo { get; set; } = true;

        [Column("IncludeWatermark")]
        public bool IncludeWatermark { get; set; } = false;

        [StringLength(100)]
        [Column("WatermarkText")]
        public string? WatermarkText { get; set; }

        // Navigation Properties
        [ForeignKey("CaseId")]
        public virtual LegalCase? Case { get; set; }

        [ForeignKey("ClientId")]
        public virtual Client Client { get; set; } = null!;

        [ForeignKey("SentBy")]
        public virtual User? SentByUser { get; set; }

        public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        /// <summary>
        /// الحصول على حالة الفاتورة حسب اللغة
        /// Get invoice status by language
        /// </summary>
        public string GetStatus(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(StatusFr) 
                ? StatusFr 
                : Status;
        }

        /// <summary>
        /// الحصول على نوع الفاتورة حسب اللغة
        /// Get invoice type by language
        /// </summary>
        public string GetInvoiceType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(InvoiceTypeFr) 
                ? InvoiceTypeFr 
                : InvoiceType;
        }

        /// <summary>
        /// حساب المبلغ الإجمالي
        /// Calculate total amount
        /// </summary>
        public void CalculateTotalAmount()
        {
            TotalAmount = SubTotal + TaxAmount - DiscountAmount;
            RemainingAmount = TotalAmount - PaidAmount;
        }

        /// <summary>
        /// حساب الضريبة
        /// Calculate tax
        /// </summary>
        public void CalculateTax()
        {
            TaxAmount = SubTotal * (TaxRate / 100);
        }

        /// <summary>
        /// حساب الخصم
        /// Calculate discount
        /// </summary>
        public void CalculateDiscount()
        {
            if (DiscountRate > 0)
            {
                DiscountAmount = SubTotal * (DiscountRate / 100);
            }
        }

        /// <summary>
        /// التحقق من دفع الفاتورة
        /// Check if invoice is paid
        /// </summary>
        public bool IsPaid()
        {
            return Status == "مدفوعة" || Status == "Paid" || RemainingAmount <= 0;
        }

        /// <summary>
        /// التحقق من تأخر الفاتورة
        /// Check if invoice is overdue
        /// </summary>
        public bool IsOverdue()
        {
            return !IsPaid() && DueDate < DateTime.Now;
        }

        /// <summary>
        /// إرسال الفاتورة
        /// Send invoice
        /// </summary>
        public void SendInvoice(int sentBy, string method, string? methodFr = null)
        {
            Status = "مرسلة";
            StatusFr = "Sent";
            SentDate = DateTime.Now;
            SentBy = sentBy;
            SentMethod = method;
            SentMethodFr = methodFr;
        }

        /// <summary>
        /// إضافة دفعة
        /// Add payment
        /// </summary>
        public void AddPayment(decimal amount)
        {
            PaidAmount += amount;
            RemainingAmount = TotalAmount - PaidAmount;
            
            if (RemainingAmount <= 0)
            {
                Status = "مدفوعة";
                StatusFr = "Paid";
            }
            else if (PaidAmount > 0)
            {
                Status = "مدفوعة جزئياً";
                StatusFr = "Partially Paid";
            }
        }

        /// <summary>
        /// إلغاء الفاتورة
        /// Cancel invoice
        /// </summary>
        public void CancelInvoice()
        {
            Status = "ملغية";
            StatusFr = "Cancelled";
        }

        /// <summary>
        /// تسجيل عرض الفاتورة
        /// Record invoice view
        /// </summary>
        public void RecordView()
        {
            ViewCount++;
            ViewedDate = DateTime.Now;
        }

        /// <summary>
        /// إرسال تذكير
        /// Send reminder
        /// </summary>
        public void SendReminder()
        {
            ReminderCount++;
            LastReminderDate = DateTime.Now;
        }
    }
}
