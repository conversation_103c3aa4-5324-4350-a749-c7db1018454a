using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة التخويل
    /// Authorization service interface
    /// </summary>
    public interface IAuthorizationService
    {
        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// Check user permission
        /// </summary>
        Task<bool> HasPermissionAsync(int userId, string module, string action);

        /// <summary>
        /// التحقق من دور المستخدم
        /// Check user role
        /// </summary>
        Task<bool> HasRoleAsync(int userId, string role);

        /// <summary>
        /// التحقق من ملكية المورد
        /// Check resource ownership
        /// </summary>
        Task<bool> CanAccessResourceAsync(int userId, string resourceType, int resourceId);

        /// <summary>
        /// التحقق من صلاحية تعديل المورد
        /// Check resource modification permission
        /// </summary>
        Task<bool> CanModifyResourceAsync(int userId, string resourceType, int resourceId);

        /// <summary>
        /// التحقق من صلاحية حذف المورد
        /// Check resource deletion permission
        /// </summary>
        Task<bool> CanDeleteResourceAsync(int userId, string resourceType, int resourceId);
    }
}
