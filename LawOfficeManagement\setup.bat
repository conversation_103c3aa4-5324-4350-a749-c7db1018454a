@echo off
chcp 65001 > nul
echo ========================================
echo    نظام إدارة مكتب المحاماة
echo    Law Office Management System
echo ========================================
echo.

echo [1/4] التحقق من متطلبات النظام...
echo Checking system requirements...

:: التحقق من وجود .NET
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت على النظام
    echo ❌ .NET is not installed
    echo يرجى تحميل وتثبيت .NET 8.0 من: https://dotnet.microsoft.com/download
    echo Please download and install .NET 8.0 from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ .NET متوفر
echo ✅ .NET is available

:: التحقق من وجود SQL Server
sqlcmd -S .\SQLEXPRESS -Q "SELECT @@VERSION" > nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  SQL Server Express غير متوفر
    echo ⚠️  SQL Server Express is not available
    echo سيتم محاولة الاتصال بـ SQL Server المحلي...
    echo Trying to connect to local SQL Server...
    
    sqlcmd -S . -Q "SELECT @@VERSION" > nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ SQL Server غير متوفر
        echo ❌ SQL Server is not available
        echo يرجى تثبيت SQL Server أو SQL Server Express
        echo Please install SQL Server or SQL Server Express
        pause
        exit /b 1
    )
)

echo ✅ SQL Server متوفر
echo ✅ SQL Server is available

echo.
echo [2/4] بناء المشروع...
echo Building project...

dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo ❌ Failed to build project
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo ✅ Project built successfully

echo.
echo [3/4] إعداد قاعدة البيانات...
echo Setting up database...

:: إنشاء قاعدة البيانات
echo إنشاء قاعدة البيانات...
echo Creating database...
sqlcmd -S .\SQLEXPRESS -i "Database\DatabaseSchema.sql" > nul 2>&1
if %errorlevel% neq 0 (
    echo محاولة الاتصال بـ SQL Server المحلي...
    echo Trying local SQL Server...
    sqlcmd -S . -i "Database\DatabaseSchema.sql" > nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        echo ❌ Failed to create database
        pause
        exit /b 1
    )
)

:: إدراج البيانات الأولية
echo إدراج البيانات الأولية...
echo Inserting initial data...
sqlcmd -S .\SQLEXPRESS -i "Database\InitialData.sql" > nul 2>&1
if %errorlevel% neq 0 (
    sqlcmd -S . -i "Database\InitialData.sql" > nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ فشل في إدراج البيانات الأولية
        echo ❌ Failed to insert initial data
        pause
        exit /b 1
    )
)

echo ✅ تم إعداد قاعدة البيانات بنجاح
echo ✅ Database setup completed successfully

echo.
echo [4/4] إنشاء المجلدات المطلوبة...
echo Creating required directories...

if not exist "Documents" mkdir Documents
if not exist "Backups" mkdir Backups
if not exist "Logs" mkdir Logs
if not exist "Temp" mkdir Temp
if not exist "Reports" mkdir Reports

echo ✅ تم إنشاء المجلدات بنجاح
echo ✅ Directories created successfully

echo.
echo ========================================
echo ✅ تم إعداد النظام بنجاح!
echo ✅ System setup completed successfully!
echo ========================================
echo.
echo معلومات تسجيل الدخول الافتراضية:
echo Default login credentials:
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: admin123
echo.
echo ⚠️  يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول
echo ⚠️  It is recommended to change the password after first login
echo.

set /p choice="هل تريد تشغيل التطبيق الآن؟ (y/n) / Do you want to run the application now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo تشغيل التطبيق...
    echo Starting application...
    dotnet run --configuration Release
) else (
    echo.
    echo يمكنك تشغيل التطبيق لاحقاً باستخدام الأمر:
    echo You can run the application later using:
    echo dotnet run --configuration Release
    echo.
    echo أو تشغيل الملف:
    echo Or run the file:
    echo run.bat
)

echo.
pause
