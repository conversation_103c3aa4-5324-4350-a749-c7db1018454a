using LawFirmManagementSystem.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة التوطين
    /// Localization service implementation
    /// </summary>
    public class LocalizationService : ILocalizationService
    {
        private readonly ILogger<LocalizationService> _logger;
        private readonly Dictionary<string, Dictionary<string, string>> _translations;
        private string _currentLanguage = "ar";

        public LocalizationService(ILogger<LocalizationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _translations = new Dictionary<string, Dictionary<string, string>>();
            InitializeTranslations();
        }

        public string GetLocalizedString(string key, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            
            if (_translations.TryGetValue(lang, out var langTranslations) &&
                langTranslations.TryGetValue(key, out var translation))
            {
                return translation;
            }

            // Fallback to Arabic if French translation not found
            if (lang == "fr" && _translations.TryGetValue("ar", out var arTranslations) &&
                arTranslations.TryGetValue(key, out var arTranslation))
            {
                return arTranslation;
            }

            _logger.LogWarning("Translation not found for key: {Key} in language: {Language}", key, lang);
            return key; // Return key if translation not found
        }

        public string GetLocalizedString(string key, object[] args, string? language = null)
        {
            var template = GetLocalizedString(key, language);
            try
            {
                return string.Format(template, args);
            }
            catch (FormatException ex)
            {
                _logger.LogError(ex, "Error formatting localized string for key: {Key}", key);
                return template;
            }
        }

        public void SetCurrentLanguage(string language)
        {
            if (IsLanguageSupported(language))
            {
                _currentLanguage = language;
                _logger.LogInformation("Current language set to: {Language}", language);
            }
            else
            {
                _logger.LogWarning("Unsupported language: {Language}", language);
            }
        }

        public string GetCurrentLanguage()
        {
            return _currentLanguage;
        }

        public IEnumerable<CultureInfo> GetSupportedLanguages()
        {
            yield return new CultureInfo("ar-MA");
            yield return new CultureInfo("fr-FR");
        }

        public bool IsLanguageSupported(string language)
        {
            return language == "ar" || language == "fr";
        }

        public string GetTextDirection(string? language = null)
        {
            var lang = language ?? _currentLanguage;
            return lang == "ar" ? "rtl" : "ltr";
        }

        public bool IsRightToLeft(string? language = null)
        {
            var lang = language ?? _currentLanguage;
            return lang == "ar";
        }

        public string FormatDate(DateTime date, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return date.ToString("d", culture);
        }

        public string FormatTime(DateTime time, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return time.ToString("t", culture);
        }

        public string FormatDateTime(DateTime dateTime, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return dateTime.ToString("g", culture);
        }

        public string FormatNumber(decimal number, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return number.ToString("N", culture);
        }

        public string FormatCurrency(decimal amount, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            
            // Use MAD (Moroccan Dirham) for both languages
            return $"{amount:N2} {(lang == "fr" ? "MAD" : "د.م.")}";
        }

        public async Task LoadTranslationsAsync()
        {
            // This would typically load from files or database
            // For now, we'll use the initialized translations
            await Task.CompletedTask;
            _logger.LogInformation("Translations loaded successfully");
        }

        public async Task ReloadTranslationsAsync()
        {
            _translations.Clear();
            InitializeTranslations();
            await LoadTranslationsAsync();
            _logger.LogInformation("Translations reloaded successfully");
        }

        private void InitializeTranslations()
        {
            // Arabic translations
            var arTranslations = new Dictionary<string, string>
            {
                // Common
                {"Save", "حفظ"},
                {"Cancel", "إلغاء"},
                {"Delete", "حذف"},
                {"Edit", "تعديل"},
                {"Add", "إضافة"},
                {"Search", "بحث"},
                {"Close", "إغلاق"},
                {"Yes", "نعم"},
                {"No", "لا"},
                {"OK", "موافق"},
                {"Error", "خطأ"},
                {"Warning", "تحذير"},
                {"Information", "معلومات"},
                {"Success", "نجح"},
                
                // Navigation
                {"Clients", "العملاء"},
                {"Cases", "القضايا"},
                {"Calendar", "التقويم"},
                {"Financial", "المالية"},
                {"Documents", "الوثائق"},
                {"Reports", "التقارير"},
                {"Settings", "الإعدادات"},
                
                // Client Management
                {"NewClient", "عميل جديد"},
                {"ClientList", "قائمة العملاء"},
                {"ClientName", "اسم العميل"},
                {"ClientType", "نوع العميل"},
                {"Individual", "فرد"},
                {"Company", "شركة"},
                
                // Case Management
                {"NewCase", "قضية جديدة"},
                {"CaseList", "قائمة القضايا"},
                {"CaseTitle", "عنوان القضية"},
                {"CaseType", "نوع القضية"},
                {"CaseStatus", "حالة القضية"},
                
                // Messages
                {"SaveSuccess", "تم الحفظ بنجاح"},
                {"DeleteConfirm", "هل تريد حذف هذا العنصر؟"},
                {"LoginFailed", "فشل في تسجيل الدخول"},
                {"AccessDenied", "ليس لديك صلاحية للوصول"}
            };

            // French translations
            var frTranslations = new Dictionary<string, string>
            {
                // Common
                {"Save", "Enregistrer"},
                {"Cancel", "Annuler"},
                {"Delete", "Supprimer"},
                {"Edit", "Modifier"},
                {"Add", "Ajouter"},
                {"Search", "Rechercher"},
                {"Close", "Fermer"},
                {"Yes", "Oui"},
                {"No", "Non"},
                {"OK", "OK"},
                {"Error", "Erreur"},
                {"Warning", "Avertissement"},
                {"Information", "Information"},
                {"Success", "Succès"},
                
                // Navigation
                {"Clients", "Clients"},
                {"Cases", "Affaires"},
                {"Calendar", "Calendrier"},
                {"Financial", "Financier"},
                {"Documents", "Documents"},
                {"Reports", "Rapports"},
                {"Settings", "Paramètres"},
                
                // Client Management
                {"NewClient", "Nouveau Client"},
                {"ClientList", "Liste des Clients"},
                {"ClientName", "Nom du Client"},
                {"ClientType", "Type de Client"},
                {"Individual", "Particulier"},
                {"Company", "Société"},
                
                // Case Management
                {"NewCase", "Nouvelle Affaire"},
                {"CaseList", "Liste des Affaires"},
                {"CaseTitle", "Titre de l'Affaire"},
                {"CaseType", "Type d'Affaire"},
                {"CaseStatus", "Statut de l'Affaire"},
                
                // Messages
                {"SaveSuccess", "Enregistré avec succès"},
                {"DeleteConfirm", "Voulez-vous supprimer cet élément ?"},
                {"LoginFailed", "Échec de la connexion"},
                {"AccessDenied", "Vous n'avez pas l'autorisation d'accéder"}
            };

            _translations["ar"] = arTranslations;
            _translations["fr"] = frTranslations;
        }
    }
}
