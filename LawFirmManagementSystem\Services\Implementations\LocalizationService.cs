using LawFirmManagementSystem.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using System.Text.Json;
using System.IO;

namespace LawFirmManagementSystem.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة التوطين
    /// Localization service implementation
    /// </summary>
    public class LocalizationService : ILocalizationService
    {
        private readonly ILogger<LocalizationService> _logger;
        private readonly Dictionary<string, Dictionary<string, object>> _translations;
        private string _currentLanguage = "ar";
        private readonly string _localizationPath;

        public LocalizationService(ILogger<LocalizationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _translations = new Dictionary<string, Dictionary<string, object>>();
            _localizationPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Localization");

            // Initialize with fallback translations first
            InitializeFallbackTranslations();

            // Then try to load from files
            _ = LoadTranslationsAsync();
        }

        public string GetLocalizedString(string key, string? language = null)
        {
            var lang = language ?? _currentLanguage;

            // Try to get nested key (e.g., "Common.Save")
            var translation = GetNestedTranslation(lang, key);
            if (!string.IsNullOrEmpty(translation))
            {
                return translation;
            }

            // Fallback to Arabic if French translation not found
            if (lang == "fr")
            {
                translation = GetNestedTranslation("ar", key);
                if (!string.IsNullOrEmpty(translation))
                {
                    return translation;
                }
            }

            _logger.LogWarning("Translation not found for key: {Key} in language: {Language}", key, lang);
            return key; // Return key if translation not found
        }

        private string GetNestedTranslation(string language, string key)
        {
            if (!_translations.TryGetValue(language, out var langTranslations))
                return string.Empty;

            var keyParts = key.Split('.');
            object current = langTranslations;

            foreach (var part in keyParts)
            {
                if (current is Dictionary<string, object> dict && dict.TryGetValue(part, out var value))
                {
                    current = value;
                }
                else
                {
                    return string.Empty;
                }
            }

            return current?.ToString() ?? string.Empty;
        }

        public string GetLocalizedString(string key, object[] args, string? language = null)
        {
            var template = GetLocalizedString(key, language);
            try
            {
                return string.Format(template, args);
            }
            catch (FormatException ex)
            {
                _logger.LogError(ex, "Error formatting localized string for key: {Key}", key);
                return template;
            }
        }

        public void SetCurrentLanguage(string language)
        {
            if (IsLanguageSupported(language))
            {
                _currentLanguage = language;
                _logger.LogInformation("Current language set to: {Language}", language);
            }
            else
            {
                _logger.LogWarning("Unsupported language: {Language}", language);
            }
        }

        public string GetCurrentLanguage()
        {
            return _currentLanguage;
        }

        public IEnumerable<CultureInfo> GetSupportedLanguages()
        {
            yield return new CultureInfo("ar-MA");
            yield return new CultureInfo("fr-FR");
        }

        public bool IsLanguageSupported(string language)
        {
            return language == "ar" || language == "fr";
        }

        public string GetTextDirection(string? language = null)
        {
            var lang = language ?? _currentLanguage;
            return lang == "ar" ? "rtl" : "ltr";
        }

        public bool IsRightToLeft(string? language = null)
        {
            var lang = language ?? _currentLanguage;
            return lang == "ar";
        }

        public string FormatDate(DateTime date, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return date.ToString("d", culture);
        }

        public string FormatTime(DateTime time, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return time.ToString("t", culture);
        }

        public string FormatDateTime(DateTime dateTime, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return dateTime.ToString("g", culture);
        }

        public string FormatNumber(decimal number, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            return number.ToString("N", culture);
        }

        public string FormatCurrency(decimal amount, string? language = null)
        {
            var lang = language ?? _currentLanguage;
            var culture = lang == "fr" ? new CultureInfo("fr-FR") : new CultureInfo("ar-MA");
            
            // Use MAD (Moroccan Dirham) for both languages
            return $"{amount:N2} {(lang == "fr" ? "MAD" : "د.م.")}";
        }

        public async Task LoadTranslationsAsync()
        {
            try
            {
                if (!Directory.Exists(_localizationPath))
                {
                    _logger.LogWarning("Localization directory not found: {Path}", _localizationPath);
                    return;
                }

                var supportedLanguages = new[] { "ar", "fr" };

                foreach (var language in supportedLanguages)
                {
                    var filePath = Path.Combine(_localizationPath, $"{language}.json");

                    if (File.Exists(filePath))
                    {
                        var jsonContent = await File.ReadAllTextAsync(filePath);
                        var translations = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);

                        if (translations != null)
                        {
                            _translations[language] = translations;
                            _logger.LogInformation("Loaded translations for language: {Language}", language);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Translation file not found: {FilePath}", filePath);
                    }
                }

                _logger.LogInformation("Translations loaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading translations");
            }
        }

        public async Task ReloadTranslationsAsync()
        {
            _translations.Clear();
            InitializeFallbackTranslations();
            await LoadTranslationsAsync();
            _logger.LogInformation("Translations reloaded successfully");
        }

        private void InitializeFallbackTranslations()
        {
            // Arabic fallback translations
            var arTranslations = new Dictionary<string, object>
            {
                ["Common"] = new Dictionary<string, object>
                {
                    ["Save"] = "حفظ",
                    ["Cancel"] = "إلغاء",
                    ["Delete"] = "حذف",
                    ["Edit"] = "تعديل",
                    ["Add"] = "إضافة",
                    ["Search"] = "بحث",
                    ["Close"] = "إغلاق",
                    ["Yes"] = "نعم",
                    ["No"] = "لا",
                    ["OK"] = "موافق",
                    ["Error"] = "خطأ",
                    ["Warning"] = "تحذير",
                    ["Information"] = "معلومات",
                    ["Success"] = "نجح"
                },
                ["Navigation"] = new Dictionary<string, object>
                {
                    ["Clients"] = "العملاء",
                    ["Cases"] = "القضايا",
                    ["Calendar"] = "التقويم",
                    ["Financial"] = "المالية",
                    ["Documents"] = "الوثائق",
                    ["Reports"] = "التقارير",
                    ["Settings"] = "الإعدادات"
                },
                ["Messages"] = new Dictionary<string, object>
                {
                    ["SaveSuccess"] = "تم الحفظ بنجاح",
                    ["DeleteConfirm"] = "هل تريد حذف هذا العنصر؟",
                    ["LoginFailed"] = "فشل في تسجيل الدخول",
                    ["AccessDenied"] = "ليس لديك صلاحية للوصول"
                }
            };

            // French fallback translations
            var frTranslations = new Dictionary<string, object>
            {
                ["Common"] = new Dictionary<string, object>
                {
                    ["Save"] = "Enregistrer",
                    ["Cancel"] = "Annuler",
                    ["Delete"] = "Supprimer",
                    ["Edit"] = "Modifier",
                    ["Add"] = "Ajouter",
                    ["Search"] = "Rechercher",
                    ["Close"] = "Fermer",
                    ["Yes"] = "Oui",
                    ["No"] = "Non",
                    ["OK"] = "OK",
                    ["Error"] = "Erreur",
                    ["Warning"] = "Avertissement",
                    ["Information"] = "Information",
                    ["Success"] = "Succès"
                },
                ["Navigation"] = new Dictionary<string, object>
                {
                    ["Clients"] = "Clients",
                    ["Cases"] = "Affaires",
                    ["Calendar"] = "Calendrier",
                    ["Financial"] = "Financier",
                    ["Documents"] = "Documents",
                    ["Reports"] = "Rapports",
                    ["Settings"] = "Paramètres"
                },
                ["Messages"] = new Dictionary<string, object>
                {
                    ["SaveSuccess"] = "Enregistré avec succès",
                    ["DeleteConfirm"] = "Voulez-vous supprimer cet élément ?",
                    ["LoginFailed"] = "Échec de la connexion",
                    ["AccessDenied"] = "Vous n'avez pas l'autorisation d'accéder"
                }
            };

            _translations["ar"] = arTranslations;
            _translations["fr"] = frTranslations;
        }
    }
}
