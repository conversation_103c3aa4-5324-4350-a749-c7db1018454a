========================================
نظام إدارة مكتب المحاماة
Law Firm Management System
========================================

🚀 كيفية تشغيل النظام / How to Run the System:

الطريقة الأولى / Method 1:
1. افتح Command Prompt أو PowerShell
   Open Command Prompt or PowerShell

2. انتقل إلى مجلد المشروع
   Navigate to project folder:
   cd "C:\Users\<USER>\Desktop\law\LawFirmManagementSystem"

3. شغل الأمر التالي
   Run the following command:
   dotnet run

الطريقة الثانية / Method 2:
1. انقر نقراً مزدوجاً على ملف
   Double-click on file:
   simple-run.bat

الطريقة الثالثة / Method 3:
1. افتح المشروع في Visual Studio
   Open project in Visual Studio

2. اضغط F5 أو اختر Debug > Start Debugging
   Press F5 or choose Debug > Start Debugging

========================================
بيانات تسجيل الدخول / Login Credentials:
========================================

اسم المستخدم / Username: admin
كلمة المرور / Password: Admin@123

========================================
الميزات المتاحة / Available Features:
========================================

✅ نافذة تسجيل الدخول مع تبديل اللغة
   Login window with language switching

✅ النافذة الرئيسية مع القوائم الكاملة
   Main window with complete menus

✅ نافذة قائمة العملاء مع بيانات تجريبية
   Client list window with sample data

✅ نظام ترجمة كامل (عربي/فرنسي)
   Complete localization system (Arabic/French)

✅ دعم RTL/LTR للنصوص
   RTL/LTR text support

========================================
في حالة وجود مشاكل / If You Have Problems:
========================================

1. تأكد من تثبيت .NET 8.0 SDK
   Make sure .NET 8.0 SDK is installed

2. شغل الأمر التالي لإصلاح المشاكل:
   Run the following command to fix issues:
   dotnet restore
   dotnet build

3. إذا استمرت المشاكل، شغل:
   If problems persist, run:
   dotnet clean
   dotnet restore
   dotnet build
   dotnet run

========================================
الملفات المهمة / Important Files:
========================================

📁 Forms/Authentication/LoginForm.cs - نافذة تسجيل الدخول
📁 Forms/Main/MainForm.cs - النافذة الرئيسية
📁 Forms/Clients/ClientListForm.cs - نافذة العملاء
📁 Services/Implementations/SimpleClientService.cs - خدمة العملاء
📁 Resources/Localization/ - ملفات الترجمة
📁 appsettings.json - إعدادات التطبيق

========================================
الدعم / Support:
========================================

إذا واجهت أي مشاكل، تحقق من:
If you encounter any issues, check:

1. PROJECT_STATUS.md - حالة المشروع
2. README.md - دليل التشغيل
3. DEVELOPER_GUIDE.md - دليل المطور

========================================
نصائح مهمة / Important Tips:
========================================

🔹 تأكد من أن .NET 8.0 مثبت على النظام
   Make sure .NET 8.0 is installed on your system

🔹 شغل التطبيق من Command Prompt للحصول على رسائل الأخطاء
   Run the application from Command Prompt to see error messages

🔹 استخدم Visual Studio للتطوير والتصحيح
   Use Visual Studio for development and debugging

🔹 البيانات التجريبية متاحة في SimpleClientService.cs
   Sample data is available in SimpleClientService.cs

========================================
تم إنشاء هذا النظام بواسطة Augment Agent
Created by Augment Agent
========================================
