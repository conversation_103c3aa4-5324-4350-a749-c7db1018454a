using LawFirmManagementSystem.Forms.Authentication;
using LawFirmManagementSystem.Services.Interfaces;
using LawFirmManagementSystem.Utils;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Forms.Main
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// Main application window
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILocalizationService _localizationService;
        private readonly ILogger<MainForm> _logger;

        // Main layout controls
        private MenuStrip mainMenuStrip;
        private ToolStrip mainToolStrip;
        private StatusStrip statusStrip;
        private SplitContainer mainSplitContainer;
        private TreeView navigationTreeView;
        private Panel contentPanel;
        private TabControl contentTabControl;

        // Status bar controls
        private ToolStripStatusLabel userStatusLabel;
        private ToolStripStatusLabel timeStatusLabel;
        private ToolStripStatusLabel languageStatusLabel;
        private ToolStripProgressBar progressStatusBar;

        // Menu items
        private ToolStripMenuItem fileMenuItem;
        private ToolStripMenuItem clientsMenuItem;
        private ToolStripMenuItem casesMenuItem;
        private ToolStripMenuItem calendarMenuItem;
        private ToolStripMenuItem financialMenuItem;
        private ToolStripMenuItem documentsMenuItem;
        private ToolStripMenuItem reportsMenuItem;
        private ToolStripMenuItem toolsMenuItem;
        private ToolStripMenuItem helpMenuItem;

        // Toolbar buttons
        private ToolStripButton newClientButton;
        private ToolStripButton newCaseButton;
        private ToolStripButton calendarButton;
        private ToolStripButton searchButton;
        private ToolStripButton settingsButton;

        public MainForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();
            _logger = _serviceProvider.GetRequiredService<ILogger<MainForm>>();

            InitializeComponent();
            InitializeLocalization();
            SetupEventHandlers();
            LoadUserInterface();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "نظام إدارة مكتب المحاماة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = SystemIcons.Application;

            // Create main menu
            CreateMainMenu();

            // Create toolbar
            CreateToolbar();

            // Create status bar
            CreateStatusBar();

            // Create main layout
            CreateMainLayout();

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                mainSplitContainer,
                mainToolStrip,
                mainMenuStrip,
                statusStrip
            });

            this.MainMenuStrip = mainMenuStrip;
        }

        private void CreateMainMenu()
        {
            mainMenuStrip = new MenuStrip
            {
                Font = new Font("Tahoma", 9)
            };

            // File menu
            fileMenuItem = new ToolStripMenuItem
            {
                Text = "ملف",
                Tag = "Navigation.File"
            };
            fileMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("جديد", null, null, "Navigation.New") { Tag = "Navigation.New" },
                new ToolStripMenuItem("فتح", null, null, "Navigation.Open") { Tag = "Navigation.Open" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("إعدادات", null, SettingsMenuItem_Click, "Navigation.Settings") { Tag = "Navigation.Settings" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("خروج", null, ExitMenuItem_Click, "Navigation.Exit") { Tag = "Navigation.Exit" }
            });

            // Clients menu
            clientsMenuItem = new ToolStripMenuItem
            {
                Text = "العملاء",
                Tag = "Navigation.Clients"
            };
            clientsMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("عميل جديد", null, NewClientMenuItem_Click, "ClientManagement.NewClient") { Tag = "ClientManagement.NewClient" },
                new ToolStripMenuItem("قائمة العملاء", null, ClientListMenuItem_Click, "ClientManagement.ClientList") { Tag = "ClientManagement.ClientList" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("بحث في العملاء", null, SearchClientsMenuItem_Click, "SearchClients") { Tag = "SearchClients" }
            });

            // Cases menu
            casesMenuItem = new ToolStripMenuItem
            {
                Text = "القضايا",
                Tag = "Navigation.Cases"
            };
            casesMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("قضية جديدة", null, NewCaseMenuItem_Click, "CaseManagement.NewCase") { Tag = "CaseManagement.NewCase" },
                new ToolStripMenuItem("قائمة القضايا", null, CaseListMenuItem_Click, "CaseManagement.CaseList") { Tag = "CaseManagement.CaseList" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("الجلسات", null, HearingsMenuItem_Click, "Navigation.Hearings") { Tag = "Navigation.Hearings" }
            });

            // Calendar menu
            calendarMenuItem = new ToolStripMenuItem
            {
                Text = "التقويم",
                Tag = "Navigation.Calendar",
                Click = CalendarMenuItem_Click
            };

            // Financial menu
            financialMenuItem = new ToolStripMenuItem
            {
                Text = "المالية",
                Tag = "Navigation.Financial"
            };
            financialMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("الفواتير", null, InvoicesMenuItem_Click, "Navigation.Invoices") { Tag = "Navigation.Invoices" },
                new ToolStripMenuItem("الدفعات", null, PaymentsMenuItem_Click, "Navigation.Payments") { Tag = "Navigation.Payments" },
                new ToolStripMenuItem("المصروفات", null, ExpensesMenuItem_Click, "Navigation.Expenses") { Tag = "Navigation.Expenses" }
            });

            // Documents menu
            documentsMenuItem = new ToolStripMenuItem
            {
                Text = "الوثائق",
                Tag = "Navigation.Documents",
                Click = DocumentsMenuItem_Click
            };

            // Reports menu
            reportsMenuItem = new ToolStripMenuItem
            {
                Text = "التقارير",
                Tag = "Navigation.Reports",
                Click = ReportsMenuItem_Click
            };

            // Tools menu
            toolsMenuItem = new ToolStripMenuItem
            {
                Text = "أدوات",
                Tag = "Navigation.Tools"
            };
            toolsMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("النسخ الاحتياطي", null, BackupMenuItem_Click, "Navigation.Backup") { Tag = "Navigation.Backup" },
                new ToolStripMenuItem("السجلات", null, LogsMenuItem_Click, "Navigation.Logs") { Tag = "Navigation.Logs" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("المستخدمون", null, UsersMenuItem_Click, "Navigation.Users") { Tag = "Navigation.Users" }
            });

            // Help menu
            helpMenuItem = new ToolStripMenuItem
            {
                Text = "مساعدة",
                Tag = "Navigation.Help"
            };
            helpMenuItem.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("دليل المستخدم", null, UserGuideMenuItem_Click, "UserGuide") { Tag = "UserGuide" },
                new ToolStripMenuItem("الدعم الفني", null, SupportMenuItem_Click, "TechnicalSupport") { Tag = "TechnicalSupport" },
                new ToolStripSeparator(),
                new ToolStripMenuItem("حول البرنامج", null, AboutMenuItem_Click, "Common.About") { Tag = "Common.About" }
            });

            mainMenuStrip.Items.AddRange(new ToolStripItem[]
            {
                fileMenuItem,
                clientsMenuItem,
                casesMenuItem,
                calendarMenuItem,
                financialMenuItem,
                documentsMenuItem,
                reportsMenuItem,
                toolsMenuItem,
                helpMenuItem
            });
        }

        private void CreateToolbar()
        {
            mainToolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24),
                Font = new Font("Tahoma", 9)
            };

            newClientButton = new ToolStripButton
            {
                Text = "عميل جديد",
                Tag = "ClientManagement.NewClient",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Click = NewClientMenuItem_Click
            };

            newCaseButton = new ToolStripButton
            {
                Text = "قضية جديدة",
                Tag = "CaseManagement.NewCase",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Click = NewCaseMenuItem_Click
            };

            calendarButton = new ToolStripButton
            {
                Text = "التقويم",
                Tag = "Navigation.Calendar",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Click = CalendarMenuItem_Click
            };

            searchButton = new ToolStripButton
            {
                Text = "بحث",
                Tag = "Common.Search",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Click = SearchButton_Click
            };

            settingsButton = new ToolStripButton
            {
                Text = "إعدادات",
                Tag = "Navigation.Settings",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Click = SettingsMenuItem_Click
            };

            mainToolStrip.Items.AddRange(new ToolStripItem[]
            {
                newClientButton,
                newCaseButton,
                new ToolStripSeparator(),
                calendarButton,
                searchButton,
                new ToolStripSeparator(),
                settingsButton
            });
        }

        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip
            {
                Font = new Font("Tahoma", 9)
            };

            userStatusLabel = new ToolStripStatusLabel
            {
                Text = $"المستخدم: {UserSession.CurrentUser?.FullName ?? "غير محدد"}",
                Spring = false
            };

            timeStatusLabel = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                Spring = false
            };

            languageStatusLabel = new ToolStripStatusLabel
            {
                Text = _localizationService.GetCurrentLanguage() == "ar" ? "العربية" : "Français",
                Spring = false
            };

            progressStatusBar = new ToolStripProgressBar
            {
                Visible = false,
                Size = new Size(100, 16)
            };

            statusStrip.Items.AddRange(new ToolStripItem[]
            {
                userStatusLabel,
                new ToolStripStatusLabel { Spring = true },
                languageStatusLabel,
                timeStatusLabel,
                progressStatusBar
            });
        }

        private void CreateMainLayout()
        {
            // Main split container
            mainSplitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                SplitterDistance = 250,
                FixedPanel = FixedPanel.Panel1
            };

            // Navigation tree
            navigationTreeView = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 9),
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = false,
                FullRowSelect = true,
                HideSelection = false
            };

            // Content panel
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            // Content tab control
            contentTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 9)
            };

            contentPanel.Controls.Add(contentTabControl);

            // Add to split container
            mainSplitContainer.Panel1.Controls.Add(navigationTreeView);
            mainSplitContainer.Panel2.Controls.Add(contentPanel);

            // Populate navigation tree
            PopulateNavigationTree();
        }

        private void PopulateNavigationTree()
        {
            navigationTreeView.Nodes.Clear();

            var dashboardNode = new TreeNode("لوحة التحكم") { Tag = "Dashboard" };
            var clientsNode = new TreeNode("العملاء") { Tag = "Clients" };
            var casesNode = new TreeNode("القضايا") { Tag = "Cases" };
            var calendarNode = new TreeNode("التقويم") { Tag = "Calendar" };
            var financialNode = new TreeNode("المالية") { Tag = "Financial" };
            var documentsNode = new TreeNode("الوثائق") { Tag = "Documents" };
            var reportsNode = new TreeNode("التقارير") { Tag = "Reports" };
            var settingsNode = new TreeNode("الإعدادات") { Tag = "Settings" };

            // Add child nodes
            clientsNode.Nodes.AddRange(new[]
            {
                new TreeNode("عميل جديد") { Tag = "NewClient" },
                new TreeNode("قائمة العملاء") { Tag = "ClientList" }
            });

            casesNode.Nodes.AddRange(new[]
            {
                new TreeNode("قضية جديدة") { Tag = "NewCase" },
                new TreeNode("قائمة القضايا") { Tag = "CaseList" },
                new TreeNode("الجلسات") { Tag = "Hearings" }
            });

            financialNode.Nodes.AddRange(new[]
            {
                new TreeNode("الفواتير") { Tag = "Invoices" },
                new TreeNode("الدفعات") { Tag = "Payments" },
                new TreeNode("المصروفات") { Tag = "Expenses" }
            });

            navigationTreeView.Nodes.AddRange(new[]
            {
                dashboardNode,
                clientsNode,
                casesNode,
                calendarNode,
                financialNode,
                documentsNode,
                reportsNode,
                settingsNode
            });

            navigationTreeView.ExpandAll();
        }

        private void InitializeLocalization()
        {
            LocalizationHelper.Initialize(_localizationService);
            LocalizationHelper.LocalizeForm(this);
        }

        private void SetupEventHandlers()
        {
            // Navigation tree
            navigationTreeView.NodeMouseClick += NavigationTreeView_NodeMouseClick;

            // Timer for status bar
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += (s, e) => timeStatusLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();

            // Form events
            this.Load += MainForm_Load;
            this.FormClosing += MainForm_FormClosing;
        }

        private void LoadUserInterface()
        {
            // Add welcome tab
            var welcomeTab = new TabPage("مرحباً")
            {
                Tag = "Welcome"
            };

            var welcomeLabel = new Label
            {
                Text = $"مرحباً {UserSession.CurrentUser?.FullName}\n\nمرحباً بك في نظام إدارة مكتب المحاماة",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Tahoma", 14),
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            welcomeTab.Controls.Add(welcomeLabel);
            contentTabControl.TabPages.Add(welcomeTab);
        }

        // Event handlers for menu items
        private void NewClientMenuItem_Click(object sender, EventArgs e) => OpenTab("NewClient", "عميل جديد");
        private void ClientListMenuItem_Click(object sender, EventArgs e) => OpenTab("ClientList", "قائمة العملاء");
        private void SearchClientsMenuItem_Click(object sender, EventArgs e) => OpenTab("SearchClients", "بحث العملاء");
        private void NewCaseMenuItem_Click(object sender, EventArgs e) => OpenTab("NewCase", "قضية جديدة");
        private void CaseListMenuItem_Click(object sender, EventArgs e) => OpenTab("CaseList", "قائمة القضايا");
        private void HearingsMenuItem_Click(object sender, EventArgs e) => OpenTab("Hearings", "الجلسات");
        private void CalendarMenuItem_Click(object sender, EventArgs e) => OpenTab("Calendar", "التقويم");
        private void InvoicesMenuItem_Click(object sender, EventArgs e) => OpenTab("Invoices", "الفواتير");
        private void PaymentsMenuItem_Click(object sender, EventArgs e) => OpenTab("Payments", "الدفعات");
        private void ExpensesMenuItem_Click(object sender, EventArgs e) => OpenTab("Expenses", "المصروفات");
        private void DocumentsMenuItem_Click(object sender, EventArgs e) => OpenTab("Documents", "الوثائق");
        private void ReportsMenuItem_Click(object sender, EventArgs e) => OpenTab("Reports", "التقارير");
        private void BackupMenuItem_Click(object sender, EventArgs e) => OpenTab("Backup", "النسخ الاحتياطي");
        private void LogsMenuItem_Click(object sender, EventArgs e) => OpenTab("Logs", "السجلات");
        private void UsersMenuItem_Click(object sender, EventArgs e) => OpenTab("Users", "المستخدمون");
        private void SettingsMenuItem_Click(object sender, EventArgs e) => OpenTab("Settings", "الإعدادات");
        private void SearchButton_Click(object sender, EventArgs e) => OpenTab("Search", "بحث شامل");

        private void UserGuideMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("دليل المستخدم قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SupportMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("للدعم الفني:\nالبريد الإلكتروني: <EMAIL>\nالهاتف: +212 5XX-XXXXXX", 
                "الدعم الفني", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AboutMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("نظام إدارة مكتب المحاماة\nالإصدار 1.0.0\n\n© 2025 Law Firm Management Solutions", 
                "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExitMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void NavigationTreeView_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node.Tag != null)
            {
                OpenTab(e.Node.Tag.ToString(), e.Node.Text);
            }
        }

        private void OpenTab(string tabKey, string tabTitle)
        {
            // Check if tab already exists
            foreach (TabPage tab in contentTabControl.TabPages)
            {
                if (tab.Tag?.ToString() == tabKey)
                {
                    contentTabControl.SelectedTab = tab;
                    return;
                }
            }

            // Create new tab
            var newTab = new TabPage(tabTitle)
            {
                Tag = tabKey
            };

            // Add placeholder content
            var placeholderLabel = new Label
            {
                Text = $"{tabTitle}\n\nهذه الصفحة قيد التطوير",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Tahoma", 12),
                ForeColor = Color.Gray
            };

            newTab.Controls.Add(placeholderLabel);
            contentTabControl.TabPages.Add(newTab);
            contentTabControl.SelectedTab = newTab;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            _logger.LogInformation("Main form loaded for user: {Username}", UserSession.CurrentUser?.Username);
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                LocalizationHelper.GetText("ExitConfirmation", "هل تريد إغلاق التطبيق؟"),
                LocalizationHelper.GetText("Messages.Confirmation"),
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
            {
                e.Cancel = true;
            }
        }
    }
}
