using System.Collections.Generic;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة البريد الإلكتروني
    /// Email service interface
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// إرسال بريد إلكتروني
        /// Send email
        /// </summary>
        Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true);

        /// <summary>
        /// إرسال بريد إلكتروني مع مرفقات
        /// Send email with attachments
        /// </summary>
        Task<bool> SendEmailAsync(string to, string subject, string body, IEnumerable<EmailAttachment> attachments, bool isHtml = true);

        /// <summary>
        /// إرسال بريد إلكتروني لعدة مستقبلين
        /// Send email to multiple recipients
        /// </summary>
        Task<bool> SendEmailAsync(IEnumerable<string> to, string subject, string body, bool isHtml = true);

        /// <summary>
        /// إرسال بريد إلكتروني باستخدام قالب
        /// Send email using template
        /// </summary>
        Task<bool> SendTemplateEmailAsync(string to, string templateName, object model, string? language = null);

        /// <summary>
        /// إرسال تذكير موعد
        /// Send appointment reminder
        /// </summary>
        Task<bool> SendAppointmentReminderAsync(string to, DateTime appointmentDate, string clientName, string? language = null);

        /// <summary>
        /// إرسال تذكير جلسة
        /// Send hearing reminder
        /// </summary>
        Task<bool> SendHearingReminderAsync(string to, DateTime hearingDate, string caseName, string courtName, string? language = null);

        /// <summary>
        /// إرسال إشعار فاتورة
        /// Send invoice notification
        /// </summary>
        Task<bool> SendInvoiceNotificationAsync(string to, string invoiceNumber, decimal amount, DateTime dueDate, string? language = null);

        /// <summary>
        /// إرسال تأكيد دفعة
        /// Send payment confirmation
        /// </summary>
        Task<bool> SendPaymentConfirmationAsync(string to, string paymentNumber, decimal amount, DateTime paymentDate, string? language = null);

        /// <summary>
        /// التحقق من إعدادات البريد الإلكتروني
        /// Validate email settings
        /// </summary>
        Task<bool> ValidateEmailSettingsAsync();
    }

    /// <summary>
    /// مرفق البريد الإلكتروني
    /// Email attachment
    /// </summary>
    public class EmailAttachment
    {
        public string FileName { get; set; } = string.Empty;
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = "application/octet-stream";
    }
}
