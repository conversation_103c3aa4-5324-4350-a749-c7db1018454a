# دليل التثبيت / Installation Guide

## نظام إدارة مكتب المحاماة / Law Firm Management System

### متطلبات النظام / System Requirements

#### الحد الأدنى / Minimum Requirements
- **نظام التشغيل / Operating System**: Windows 10 (64-bit) أو أحدث
- **المعالج / Processor**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة / RAM**: 4 GB
- **مساحة التخزين / Storage**: 2 GB مساحة متاحة
- **الشبكة / Network**: اتصال بالإنترنت للتحديثات

#### الموصى به / Recommended Requirements
- **نظام التشغيل / Operating System**: Windows 11 (64-bit)
- **المعالج / Processor**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة / RAM**: 8 GB أو أكثر
- **مساحة التخزين / Storage**: 5 GB مساحة متاحة
- **الشبكة / Network**: اتصال سريع بالإنترنت

### المتطلبات التقنية / Technical Requirements

#### 1. .NET 8.0 Runtime
```bash
# تحميل وتثبيت .NET 8.0
# Download and install .NET 8.0
https://dotnet.microsoft.com/download/dotnet/8.0
```

#### 2. SQL Server (اختياري / Optional)
```bash
# SQL Server Express (مجاني / Free)
https://www.microsoft.com/sql-server/sql-server-downloads

# أو استخدام LocalDB المدمج
# Or use built-in LocalDB
```

### خطوات التثبيت / Installation Steps

#### الطريقة الأولى: التشغيل المباشر / Method 1: Direct Run

1. **تحميل المشروع / Download Project**
   ```bash
   # استنساخ المشروع من GitHub
   git clone https://github.com/your-repo/LawFirmManagementSystem.git
   
   # أو تحميل ملف ZIP وفك الضغط
   # Or download ZIP file and extract
   ```

2. **فتح مجلد المشروع / Open Project Folder**
   ```bash
   cd LawFirmManagementSystem
   ```

3. **تشغيل ملف التثبيت / Run Installation File**
   ```bash
   # على Windows
   run.bat
   
   # أو تشغيل مباشر
   dotnet run
   ```

#### الطريقة الثانية: التثبيت اليدوي / Method 2: Manual Installation

1. **التحقق من .NET 8.0**
   ```bash
   dotnet --version
   # يجب أن يظهر 8.0.x أو أحدث
   ```

2. **استعادة الحزم / Restore Packages**
   ```bash
   dotnet restore
   ```

3. **بناء المشروع / Build Project**
   ```bash
   dotnet build
   ```

4. **تشغيل التطبيق / Run Application**
   ```bash
   dotnet run
   ```

### إعداد قاعدة البيانات / Database Setup

#### الخيار الأول: LocalDB (افتراضي / Default)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=LawFirmManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

#### الخيار الثاني: SQL Server Express
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=LawFirmManagementDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

#### الخيار الثالث: SQL Server كامل / Full SQL Server
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=YOUR_SERVER;Database=LawFirmManagementDB;User Id=YOUR_USERNAME;Password=YOUR_PASSWORD;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

### التحقق من التثبيت / Installation Verification

#### 1. تشغيل التطبيق / Run Application
- يجب أن تظهر شاشة البداية (Splash Screen)
- ثم شاشة تسجيل الدخول

#### 2. تسجيل الدخول الأولي / Initial Login
```
اسم المستخدم / Username: admin
كلمة المرور / Password: Admin@123
```

#### 3. التحقق من قاعدة البيانات / Database Verification
- يجب إنشاء قاعدة البيانات تلقائياً
- يجب إنشاء الجداول والبيانات الأولية

### حل المشاكل الشائعة / Troubleshooting

#### مشكلة: .NET 8.0 غير مثبت
```bash
# الحل: تثبيت .NET 8.0
# Solution: Install .NET 8.0
https://dotnet.microsoft.com/download/dotnet/8.0
```

#### مشكلة: خطأ في قاعدة البيانات
```bash
# الحل 1: التحقق من سلسلة الاتصال
# Solution 1: Check connection string in appsettings.json

# الحل 2: تثبيت SQL Server Express
# Solution 2: Install SQL Server Express
https://www.microsoft.com/sql-server/sql-server-downloads
```

#### مشكلة: خطأ في الأذونات
```bash
# الحل: تشغيل كمدير
# Solution: Run as Administrator
# انقر بالزر الأيمن على run.bat واختر "تشغيل كمدير"
```

#### مشكلة: منافذ مشغولة
```bash
# الحل: تغيير المنفذ في launchSettings.json
# Solution: Change port in launchSettings.json
```

### إعدادات إضافية / Additional Configuration

#### 1. إعداد البريد الإلكتروني / Email Configuration
```json
{
  "Email": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "EnableSsl": true,
    "Username": "<EMAIL>",
    "Password": "your-app-password"
  }
}
```

#### 2. إعداد النسخ الاحتياطي / Backup Configuration
```json
{
  "Backup": {
    "Enabled": true,
    "Schedule": "0 2 * * *",
    "BackupPath": "C:\\Backups\\LawFirm",
    "RetentionDays": 30
  }
}
```

#### 3. إعداد الأمان / Security Configuration
```json
{
  "Security": {
    "PasswordPolicy": {
      "MinLength": 8,
      "RequireUppercase": true,
      "RequireLowercase": true,
      "RequireDigit": true,
      "RequireSpecialCharacter": true
    }
  }
}
```

### التحديث / Updates

#### تحديث تلقائي / Automatic Update
- التطبيق يتحقق من التحديثات عند بدء التشغيل
- يمكن تفعيل التحديث التلقائي من الإعدادات

#### تحديث يدوي / Manual Update
```bash
# سحب آخر التحديثات
git pull origin main

# إعادة بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

### النسخ الاحتياطي والاستعادة / Backup and Restore

#### إنشاء نسخة احتياطية / Create Backup
```bash
# من داخل التطبيق: أدوات > النسخ الاحتياطي
# From within app: Tools > Backup

# أو استخدام SQL Server Management Studio
# Or use SQL Server Management Studio
```

#### استعادة النسخة الاحتياطية / Restore Backup
```bash
# من داخل التطبيق: أدوات > استعادة البيانات
# From within app: Tools > Restore Data
```

### الدعم الفني / Technical Support

#### معلومات الاتصال / Contact Information
- **البريد الإلكتروني / Email**: <EMAIL>
- **الهاتف / Phone**: +212 5XX-XXXXXX
- **الموقع / Website**: www.lawfirmsolutions.ma

#### ساعات الدعم / Support Hours
- **الأحد - الخميس / Sunday - Thursday**: 9:00 - 18:00
- **الجمعة / Friday**: 9:00 - 12:00
- **السبت / Saturday**: مغلق / Closed

#### الدعم عن بُعد / Remote Support
- متوفر عبر TeamViewer أو AnyDesk
- يتطلب موافقة مسبقة

---

**ملاحظة مهمة / Important Note**: 
يُنصح بإنشاء نسخة احتياطية من البيانات قبل أي تحديث أو تغيير في النظام.

It is recommended to create a backup of your data before any update or system changes.
