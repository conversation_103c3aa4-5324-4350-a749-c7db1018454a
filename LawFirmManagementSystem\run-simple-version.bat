@echo off
echo ========================================
echo تشغيل النسخة المبسطة من النظام
echo Running Simple Version of the System
echo ========================================
echo.

echo هذه النسخة المبسطة تتضمن:
echo This simple version includes:
echo ✓ نافذة تسجيل دخول / Login window
echo ✓ النافذة الرئيسية / Main window  
echo ✓ قوائم أساسية / Basic menus
echo ✓ رسائل تفاعلية / Interactive messages
echo.

echo بيانات تسجيل الدخول:
echo Login credentials:
echo Username: admin
echo Password: Admin@123
echo.

echo تجميع وتشغيل النسخة المبسطة...
echo Compiling and running simple version...

csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleProgram.cs

if %errorlevel% equ 0 (
    echo ✓ تم التجميع بنجاح!
    echo ✓ Compilation successful!
    echo.
    echo تشغيل التطبيق...
    echo Running application...
    SimpleProgram.exe
) else (
    echo ✗ فشل التجميع
    echo ✗ Compilation failed
    echo.
    echo محاولة باستخدام dotnet...
    echo Trying with dotnet...
    
    echo إنشاء مشروع مؤقت...
    echo Creating temporary project...
    
    if not exist "temp" mkdir temp
    copy SimpleProgram.cs temp\
    cd temp
    
    echo ^<Project Sdk="Microsoft.NET.Sdk"^> > SimpleApp.csproj
    echo   ^<PropertyGroup^> >> SimpleApp.csproj
    echo     ^<OutputType^>WinExe^</OutputType^> >> SimpleApp.csproj
    echo     ^<TargetFramework^>net8.0-windows^</TargetFramework^> >> SimpleApp.csproj
    echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> SimpleApp.csproj
    echo   ^</PropertyGroup^> >> SimpleApp.csproj
    echo ^</Project^> >> SimpleApp.csproj
    
    ren SimpleProgram.cs Program.cs
    
    dotnet run
    
    cd ..
    rmdir /s /q temp
)

pause
