@echo off
echo ========================================
echo تحديث نظام إدارة مكتب المحاماة
echo Law Firm Management System Update
echo ========================================
echo.

echo التحقق من التحديثات المتاحة...
echo Checking for available updates...

:: Check if Git is available
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: Git غير متوفر، لا يمكن التحقق من التحديثات
    echo Warning: Git not available, cannot check for updates
    goto manual_update
)

:: Fetch latest changes
echo جلب آخر التحديثات...
echo Fetching latest updates...
git fetch origin

:: Check if there are updates
for /f %%i in ('git rev-list HEAD...origin/main --count') do set UPDATE_COUNT=%%i

if %UPDATE_COUNT%==0 (
    echo لا توجد تحديثات متاحة
    echo No updates available
    goto end
)

echo يوجد %UPDATE_COUNT% تحديث متاح
echo %UPDATE_COUNT% update(s) available

echo.
set /p CONFIRM="هل تريد تطبيق التحديثات؟ (y/n) / Do you want to apply updates? (y/n): "
if /i not "%CONFIRM%"=="y" goto end

echo إنشاء نسخة احتياطية...
echo Creating backup...
if not exist "Backups" mkdir Backups
set BACKUP_NAME=backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=%BACKUP_NAME: =0%
xcopy /E /I /H /Y . "Backups\%BACKUP_NAME%" >nul
echo تم إنشاء النسخة الاحتياطية: Backups\%BACKUP_NAME%
echo Backup created: Backups\%BACKUP_NAME%

echo تطبيق التحديثات...
echo Applying updates...
git pull origin main
if %errorlevel% neq 0 (
    echo خطأ في تطبيق التحديثات
    echo Error applying updates
    goto restore_backup
)

echo استعادة الحزم...
echo Restoring packages...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    echo Error restoring packages
    goto restore_backup
)

echo بناء المشروع...
echo Building project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    echo Error building project
    goto restore_backup
)

echo تطبيق تحديثات قاعدة البيانات...
echo Applying database updates...
:: Uncomment when EF migrations are set up
:: dotnet ef database update
:: if %errorlevel% neq 0 (
::     echo خطأ في تحديث قاعدة البيانات
::     echo Error updating database
::     goto restore_backup
:: )

echo ========================================
echo تم تطبيق التحديثات بنجاح!
echo Updates applied successfully!
echo ========================================
echo.

echo يُنصح بإعادة تشغيل التطبيق
echo Recommended to restart the application
goto end

:manual_update
echo ========================================
echo التحديث اليدوي
echo Manual Update
echo ========================================
echo.
echo لتحديث المشروع يدوياً:
echo To update the project manually:
echo.
echo 1. قم بتحميل آخر إصدار من GitHub
echo    Download latest version from GitHub
echo.
echo 2. انسخ الملفات الجديدة
echo    Copy new files
echo.
echo 3. قم بتشغيل:
echo    Run:
echo    dotnet restore
echo    dotnet build
echo.
goto end

:restore_backup
echo ========================================
echo خطأ في التحديث - استعادة النسخة الاحتياطية
echo Update Error - Restoring Backup
echo ========================================
echo.
echo استعادة النسخة الاحتياطية...
echo Restoring backup...
xcopy /E /I /H /Y "Backups\%BACKUP_NAME%\*" . >nul
echo تم استعادة النسخة الاحتياطية
echo Backup restored
echo.
echo يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني
echo Please try again or contact technical support
goto end

:end
echo.
pause
