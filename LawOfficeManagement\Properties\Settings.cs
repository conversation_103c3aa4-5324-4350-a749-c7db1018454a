using System.Configuration;

namespace LawOfficeManagement.Properties
{
    internal sealed partial class Settings : ApplicationSettingsBase
    {
        private static Settings defaultInstance = ((Settings)(ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default
        {
            get
            {
                return defaultInstance;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("")]
        public string RememberedUsername
        {
            get
            {
                return ((string)(this["RememberedUsername"]));
            }
            set
            {
                this["RememberedUsername"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("False")]
        public bool RememberMe
        {
            get
            {
                return ((bool)(this["RememberMe"]));
            }
            set
            {
                this["RememberMe"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("ar")]
        public string Language
        {
            get
            {
                return ((string)(this["Language"]));
            }
            set
            {
                this["Language"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("Light")]
        public string Theme
        {
            get
            {
                return ((string)(this["Theme"]));
            }
            set
            {
                this["Theme"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("")]
        public string DatabaseConnectionString
        {
            get
            {
                return ((string)(this["DatabaseConnectionString"]));
            }
            set
            {
                this["DatabaseConnectionString"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("True")]
        public bool AutoBackup
        {
            get
            {
                return ((bool)(this["AutoBackup"]));
            }
            set
            {
                this["AutoBackup"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("24")]
        public int BackupFrequencyHours
        {
            get
            {
                return ((int)(this["BackupFrequencyHours"]));
            }
            set
            {
                this["BackupFrequencyHours"] = value;
            }
        }
        
        [UserScopedSetting()]
        [DefaultSettingValue("")]
        public string BackupPath
        {
            get
            {
                return ((string)(this["BackupPath"]));
            }
            set
            {
                this["BackupPath"] = value;
            }
        }
    }
}
