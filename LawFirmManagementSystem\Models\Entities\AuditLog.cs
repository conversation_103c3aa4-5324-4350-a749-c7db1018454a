using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان سجل التدقيق
    /// Audit log entity
    /// </summary>
    [Table("AuditLogs")]
    public class AuditLog : BaseEntity
    {
        [Required]
        [Column("UserId")]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Action")]
        public string Action { get; set; } = string.Empty; // Create, Update, Delete, View, Login, Logout

        [StringLength(50)]
        [Column("ActionFr")]
        public string? ActionFr { get; set; }

        [Required]
        [StringLength(100)]
        [Column("EntityType")]
        public string EntityType { get; set; } = string.Empty; // User, Case, Client, etc.

        [StringLength(100)]
        [Column("EntityTypeFr")]
        public string? EntityTypeFr { get; set; }

        [Column("EntityId")]
        public int? EntityId { get; set; }

        [StringLength(200)]
        [Column("EntityName")]
        public string? EntityName { get; set; }

        [StringLength(200)]
        [Column("EntityNameFr")]
        public string? EntityNameFr { get; set; }

        [Required]
        [Column("Timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        [StringLength(100)]
        [Column("IpAddress")]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        [Column("UserAgent")]
        public string? UserAgent { get; set; }

        [StringLength(100)]
        [Column("SessionId")]
        public string? SessionId { get; set; }

        [StringLength(2000)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(2000)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [Column("OldValues")]
        public string? OldValues { get; set; } // JSON

        [Column("NewValues")]
        public string? NewValues { get; set; } // JSON

        [Column("ChangedFields")]
        public string? ChangedFields { get; set; } // JSON array

        [StringLength(50)]
        [Column("Severity")]
        public string Severity { get; set; } = "معلومات"; // معلومات، تحذير، خطأ، حرج

        [StringLength(50)]
        [Column("SeverityFr")]
        public string? SeverityFr { get; set; }

        [StringLength(50)]
        [Column("Category")]
        public string? Category { get; set; } // Security, Data, System, User

        [StringLength(50)]
        [Column("CategoryFr")]
        public string? CategoryFr { get; set; }

        [StringLength(100)]
        [Column("Module")]
        public string? Module { get; set; } // Cases, Clients, Users, etc.

        [StringLength(100)]
        [Column("ModuleFr")]
        public string? ModuleFr { get; set; }

        [StringLength(200)]
        [Column("Feature")]
        public string? Feature { get; set; } // Specific feature within module

        [StringLength(200)]
        [Column("FeatureFr")]
        public string? FeatureFr { get; set; }

        [Column("Duration")]
        public long? Duration { get; set; } // في الميلي ثانية

        [Column("IsSuccessful")]
        public bool IsSuccessful { get; set; } = true;

        [StringLength(2000)]
        [Column("ErrorMessage")]
        public string? ErrorMessage { get; set; }

        [Column("StackTrace")]
        public string? StackTrace { get; set; }

        [StringLength(100)]
        [Column("RequestId")]
        public string? RequestId { get; set; }

        [StringLength(200)]
        [Column("RequestUrl")]
        public string? RequestUrl { get; set; }

        [StringLength(20)]
        [Column("RequestMethod")]
        public string? RequestMethod { get; set; } // GET, POST, PUT, DELETE

        [Column("RequestSize")]
        public long? RequestSize { get; set; } // بالبايت

        [Column("ResponseSize")]
        public long? ResponseSize { get; set; } // بالبايت

        [Column("ResponseCode")]
        public int? ResponseCode { get; set; }

        [StringLength(100)]
        [Column("DeviceName")]
        public string? DeviceName { get; set; }

        [StringLength(50)]
        [Column("DeviceType")]
        public string? DeviceType { get; set; }

        [StringLength(50)]
        [Column("OperatingSystem")]
        public string? OperatingSystem { get; set; }

        [StringLength(50)]
        [Column("Browser")]
        public string? Browser { get; set; }

        [StringLength(100)]
        [Column("Location")]
        public string? Location { get; set; }

        [StringLength(100)]
        [Column("LocationFr")]
        public string? LocationFr { get; set; }

        [Column("IsAutomated")]
        public bool IsAutomated { get; set; } = false;

        [StringLength(100)]
        [Column("AutomationSource")]
        public string? AutomationSource { get; set; }

        [Column("IsSystemAction")]
        public bool IsSystemAction { get; set; } = false;

        [Column("RequiresReview")]
        public bool RequiresReview { get; set; } = false;

        [Column("IsReviewed")]
        public bool IsReviewed { get; set; } = false;

        [Column("ReviewedBy")]
        public int? ReviewedBy { get; set; }

        [Column("ReviewedDate")]
        public DateTime? ReviewedDate { get; set; }

        [StringLength(2000)]
        [Column("ReviewNotes")]
        public string? ReviewNotes { get; set; }

        [StringLength(2000)]
        [Column("ReviewNotesFr")]
        public string? ReviewNotesFr { get; set; }

        [Column("IsArchived")]
        public bool IsArchived { get; set; } = false;

        [Column("ArchiveDate")]
        public DateTime? ArchiveDate { get; set; }

        [Column("RetentionDate")]
        public DateTime? RetentionDate { get; set; }

        [StringLength(500)]
        [Column("Tags")]
        public string? Tags { get; set; }

        [StringLength(500)]
        [Column("TagsFr")]
        public string? TagsFr { get; set; }

        [Column("AdditionalData")]
        public string? AdditionalData { get; set; } // JSON

        // Navigation Properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ReviewedBy")]
        public virtual User? ReviewedByUser { get; set; }

        /// <summary>
        /// الحصول على الإجراء حسب اللغة
        /// Get action by language
        /// </summary>
        public string GetAction(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ActionFr) 
                ? ActionFr 
                : Action;
        }

        /// <summary>
        /// الحصول على نوع الكيان حسب اللغة
        /// Get entity type by language
        /// </summary>
        public string GetEntityType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(EntityTypeFr) 
                ? EntityTypeFr 
                : EntityType;
        }

        /// <summary>
        /// الحصول على الوصف حسب اللغة
        /// Get description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// الحصول على مستوى الخطورة حسب اللغة
        /// Get severity by language
        /// </summary>
        public string GetSeverity(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(SeverityFr) 
                ? SeverityFr 
                : Severity;
        }

        /// <summary>
        /// التحقق من كون السجل خطأ
        /// Check if log is an error
        /// </summary>
        public bool IsError()
        {
            return Severity == "خطأ" || Severity == "حرج" || !IsSuccessful;
        }

        /// <summary>
        /// التحقق من كون السجل تحذير
        /// Check if log is a warning
        /// </summary>
        public bool IsWarning()
        {
            return Severity == "تحذير";
        }

        /// <summary>
        /// التحقق من كون السجل معلومات
        /// Check if log is informational
        /// </summary>
        public bool IsInformation()
        {
            return Severity == "معلومات";
        }

        /// <summary>
        /// التحقق من كون السجل حرج
        /// Check if log is critical
        /// </summary>
        public bool IsCritical()
        {
            return Severity == "حرج";
        }

        /// <summary>
        /// الحصول على مدة العملية بصيغة قابلة للقراءة
        /// Get duration in readable format
        /// </summary>
        public string GetDurationFormatted()
        {
            if (!Duration.HasValue) return "غير محدد";
            
            var ms = Duration.Value;
            if (ms < 1000) return $"{ms} مللي ثانية";
            if (ms < 60000) return $"{ms / 1000.0:F2} ثانية";
            return $"{ms / 60000.0:F2} دقيقة";
        }

        /// <summary>
        /// مراجعة السجل
        /// Review log
        /// </summary>
        public void ReviewLog(int reviewedBy, string? notes = null, string? notesFr = null)
        {
            IsReviewed = true;
            ReviewedBy = reviewedBy;
            ReviewedDate = DateTime.Now;
            ReviewNotes = notes;
            ReviewNotesFr = notesFr;
        }

        /// <summary>
        /// أرشفة السجل
        /// Archive log
        /// </summary>
        public void ArchiveLog(DateTime? retentionDate = null)
        {
            IsArchived = true;
            ArchiveDate = DateTime.Now;
            RetentionDate = retentionDate ?? DateTime.Now.AddYears(7); // الاحتفاظ لمدة 7 سنوات افتراضياً
        }

        /// <summary>
        /// التحقق من انتهاء مدة الاحتفاظ
        /// Check if retention period is expired
        /// </summary>
        public bool IsRetentionExpired()
        {
            return RetentionDate.HasValue && RetentionDate.Value <= DateTime.Now;
        }

        /// <summary>
        /// إنشاء سجل تدقيق جديد
        /// Create new audit log
        /// </summary>
        public static AuditLog CreateLog(int userId, string action, string entityType, 
            int? entityId = null, string? description = null, string? oldValues = null, 
            string? newValues = null, string severity = "معلومات")
        {
            return new AuditLog
            {
                UserId = userId,
                Action = action,
                EntityType = entityType,
                EntityId = entityId,
                Description = description,
                OldValues = oldValues,
                NewValues = newValues,
                Severity = severity,
                Timestamp = DateTime.Now,
                IsSuccessful = true
            };
        }

        /// <summary>
        /// إنشاء سجل خطأ
        /// Create error log
        /// </summary>
        public static AuditLog CreateErrorLog(int userId, string action, string entityType, 
            string errorMessage, string? stackTrace = null, int? entityId = null)
        {
            return new AuditLog
            {
                UserId = userId,
                Action = action,
                EntityType = entityType,
                EntityId = entityId,
                ErrorMessage = errorMessage,
                StackTrace = stackTrace,
                Severity = "خطأ",
                SeverityFr = "Erreur",
                Timestamp = DateTime.Now,
                IsSuccessful = false,
                RequiresReview = true
            };
        }
    }
}
