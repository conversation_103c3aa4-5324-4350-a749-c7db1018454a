@echo off
echo ========================================
echo تشخيص مشاكل المشروع
echo Project Diagnostics
echo ========================================
echo.

echo 1. فحص ملفات المشروع...
echo 1. Checking project files...
if exist "LawFirmManagementSystem.csproj" (
    echo ✓ ملف المشروع موجود
    echo ✓ Project file exists
) else (
    echo ✗ ملف المشروع غير موجود
    echo ✗ Project file missing
)

echo.
echo 2. فحص .NET SDK...
echo 2. Checking .NET SDK...
dotnet --version
if %errorlevel% equ 0 (
    echo ✓ .NET SDK متاح
    echo ✓ .NET SDK available
) else (
    echo ✗ .NET SDK غير متاح
    echo ✗ .NET SDK not available
)

echo.
echo 3. فحص الحزم...
echo 3. Checking packages...
dotnet list package

echo.
echo 4. محاولة استعادة الحزم...
echo 4. Attempting package restore...
dotnet restore

echo.
echo 5. محاولة البناء مع تفاصيل الأخطاء...
echo 5. Attempting build with error details...
dotnet build --verbosity normal

echo.
echo 6. فحص الملفات المهمة...
echo 6. Checking important files...

if exist "Program.cs" (
    echo ✓ Program.cs موجود
) else (
    echo ✗ Program.cs مفقود
)

if exist "appsettings.json" (
    echo ✓ appsettings.json موجود
) else (
    echo ✗ appsettings.json مفقود
)

if exist "Forms\Authentication\LoginForm.cs" (
    echo ✓ LoginForm.cs موجود
) else (
    echo ✗ LoginForm.cs مفقود
)

if exist "Services\Implementations\SimpleClientService.cs" (
    echo ✓ SimpleClientService.cs موجود
) else (
    echo ✗ SimpleClientService.cs مفقود
)

echo.
echo ========================================
echo انتهى التشخيص
echo Diagnostics complete
echo ========================================

pause
