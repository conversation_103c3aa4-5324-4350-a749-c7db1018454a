using Microsoft.Extensions.DependencyInjection;
using LawFirmManagementSystem.Services.Interfaces;
using LawFirmManagementSystem.Services.Implementations;

namespace LawFirmManagementSystem.Services
{
    /// <summary>
    /// امتدادات تسجيل الخدمات
    /// Service registration extensions
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// إضافة خدمات التطبيق
        /// Add application services
        /// </summary>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // Core Services
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IClientService, SimpleClientService>();
            services.AddScoped<SimpleClientService>();
            services.AddScoped<ICaseService, CaseService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<IPaymentService, PaymentService>();
            services.AddScoped<IDocumentService, DocumentService>();
            services.AddScoped<IHearingService, HearingService>();
            services.AddScoped<IAppointmentService, AppointmentService>();
            services.AddScoped<IReportService, ReportService>();

            // Security Services
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IAuthorizationService, AuthorizationService>();
            services.AddScoped<IEncryptionService, EncryptionService>();
            services.AddScoped<IAuditService, AuditService>();

            // Utility Services
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<ISmsService, SmsService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<IConfigurationService, ConfigurationService>();
            services.AddScoped<ILocalizationService, LocalizationService>();

            // Business Logic Services
            services.AddScoped<ICaseManagementService, CaseManagementService>();
            services.AddScoped<IClientManagementService, ClientManagementService>();
            services.AddScoped<IFinancialService, FinancialService>();
            services.AddScoped<ICalendarService, CalendarService>();
            services.AddScoped<ITaskManagementService, TaskManagementService>();
            services.AddScoped<ITimelineService, TimelineService>();

            // Import/Export Services
            services.AddScoped<IImportService, ImportService>();
            services.AddScoped<IExportService, ExportService>();
            services.AddScoped<IPdfService, PdfService>();
            services.AddScoped<IExcelService, ExcelService>();

            // Communication Services
            services.AddScoped<IWhatsAppService, WhatsAppService>();
            services.AddScoped<ICommunicationService, CommunicationService>();

            // Validation Services
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<IBusinessRuleService, BusinessRuleService>();

            // Cache Services
            services.AddSingleton<ICacheService, MemoryCacheService>();

            // Background Services
            services.AddHostedService<ReminderBackgroundService>();
            services.AddHostedService<BackupBackgroundService>();
            services.AddHostedService<CleanupBackgroundService>();

            return services;
        }
    }
}
