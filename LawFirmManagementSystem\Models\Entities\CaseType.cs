using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان نوع القضية
    /// Case type entity
    /// </summary>
    [Table("CaseTypes")]
    public class CaseType : BaseEntity
    {
        [Required]
        [StringLength(100)]
        [Column("TypeName")]
        public string TypeName { get; set; } = string.Empty;

        [StringLength(100)]
        [Column("TypeNameFr")]
        public string? TypeNameFr { get; set; }

        [StringLength(500)]
        [Column("Description")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [StringLength(50)]
        [Column("Category")]
        public string? Category { get; set; } // مدني، جنائي، تجاري، أسري، إداري

        [StringLength(50)]
        [Column("CategoryFr")]
        public string? CategoryFr { get; set; }

        [Column("DefaultFee")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal? DefaultFee { get; set; }

        [Column("EstimatedDurationDays")]
        public int? EstimatedDurationDays { get; set; }

        [StringLength(50)]
        [Column("RequiredDocuments")]
        public string? RequiredDocuments { get; set; }

        [StringLength(50)]
        [Column("RequiredDocumentsFr")]
        public string? RequiredDocumentsFr { get; set; }

        [Column("IsUrgentByDefault")]
        public bool IsUrgentByDefault { get; set; } = false;

        [Column("RequiresCourtFiling")]
        public bool RequiresCourtFiling { get; set; } = true;

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        [StringLength(20)]
        [Column("Color")]
        public string? Color { get; set; } = "#007bff";

        [StringLength(50)]
        [Column("Icon")]
        public string? Icon { get; set; }

        // Navigation Properties
        public virtual ICollection<LegalCase> Cases { get; set; } = new List<LegalCase>();

        /// <summary>
        /// الحصول على اسم النوع حسب اللغة
        /// Get type name by language
        /// </summary>
        public string GetTypeName(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(TypeNameFr) 
                ? TypeNameFr 
                : TypeName;
        }

        /// <summary>
        /// الحصول على وصف النوع حسب اللغة
        /// Get type description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description ?? string.Empty;
        }

        /// <summary>
        /// الحصول على فئة النوع حسب اللغة
        /// Get type category by language
        /// </summary>
        public string GetCategory(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(CategoryFr) 
                ? CategoryFr 
                : Category ?? string.Empty;
        }
    }
}
