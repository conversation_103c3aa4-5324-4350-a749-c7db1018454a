using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Forms
{
    /// <summary>
    /// شاشة البداية
    /// Splash screen form
    /// </summary>
    public partial class SplashForm : Form
    {
        private PictureBox picLogo;
        private Label lblTitle;
        private Label lblSubtitle;
        private Label lblVersion;
        private Label lblCopyright;
        private Label lblStatus;
        private ProgressBar progressBar;
        private Timer timer;
        
        private int _progressValue = 0;
        private readonly string[] _loadingMessages = {
            "تهيئة النظام...",
            "تحميل قاعدة البيانات...",
            "تحميل الإعدادات...",
            "تحميل الخدمات...",
            "تحميل واجهة المستخدم...",
            "اكتمل التحميل..."
        };

        public SplashForm()
        {
            InitializeComponent();
            InitializeForm();
            StartLoading();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "نظام إدارة مكتب المحاماة";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.White;
            this.ShowInTaskbar = false;
            this.TopMost = true;
            
            // Add border
            this.Paint += SplashForm_Paint;
            
            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(120, 120),
                Location = new Point(240, 50),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.Transparent
            };
            
            // Title
            lblTitle = new Label
            {
                Text = "نظام إدارة مكتب المحاماة",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(500, 40),
                Location = new Point(50, 180),
                BackColor = Color.Transparent
            };
            
            // Subtitle
            lblSubtitle = new Label
            {
                Text = "Système de Gestion de Cabinet d'Avocat",
                Font = new Font("Tahoma", 12F, FontStyle.Regular),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(500, 25),
                Location = new Point(50, 220),
                BackColor = Color.Transparent
            };
            
            // Version
            lblVersion = new Label
            {
                Text = "الإصدار 1.0.0 / Version 1.0.0",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(128, 128, 128),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(500, 20),
                Location = new Point(50, 250),
                BackColor = Color.Transparent
            };
            
            // Progress bar
            progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Location = new Point(100, 290),
                Style = ProgressBarStyle.Continuous,
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            
            // Status label
            lblStatus = new Label
            {
                Text = "تهيئة النظام...",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(400, 20),
                Location = new Point(100, 320),
                BackColor = Color.Transparent
            };
            
            // Copyright
            lblCopyright = new Label
            {
                Text = "© 2025 Law Firm Management Solutions. جميع الحقوق محفوظة.",
                Font = new Font("Tahoma", 8F, FontStyle.Regular),
                ForeColor = Color.FromArgb(153, 153, 153),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(500, 15),
                Location = new Point(50, 360),
                BackColor = Color.Transparent
            };
            
            // Timer for progress simulation
            timer = new Timer
            {
                Interval = 500,
                Enabled = false
            };
            timer.Tick += Timer_Tick;
            
            // Add controls to form
            this.Controls.AddRange(new Control[] {
                picLogo, lblTitle, lblSubtitle, lblVersion,
                progressBar, lblStatus, lblCopyright
            });
            
            this.ResumeLayout(false);
        }

        private void InitializeForm()
        {
            // Set logo if available
            try
            {
                var logoPath = Path.Combine(Application.StartupPath, "Resources", "Images", "logo.png");
                if (File.Exists(logoPath))
                {
                    picLogo.Image = Image.FromFile(logoPath);
                }
                else
                {
                    // Create a simple logo placeholder
                    var bitmap = new Bitmap(120, 120);
                    using (var g = Graphics.FromImage(bitmap))
                    {
                        g.Clear(Color.FromArgb(0, 123, 255));
                        using (var brush = new SolidBrush(Color.White))
                        using (var font = new Font("Tahoma", 24F, FontStyle.Bold))
                        {
                            var text = "⚖";
                            var size = g.MeasureString(text, font);
                            var x = (120 - size.Width) / 2;
                            var y = (120 - size.Height) / 2;
                            g.DrawString(text, font, brush, x, y);
                        }
                    }
                    picLogo.Image = bitmap;
                }
            }
            catch
            {
                // Create a simple colored rectangle as fallback
                var bitmap = new Bitmap(120, 120);
                using (var g = Graphics.FromImage(bitmap))
                {
                    g.Clear(Color.FromArgb(0, 123, 255));
                }
                picLogo.Image = bitmap;
            }
            
            // Set form icon if available
            try
            {
                var iconPath = Path.Combine(Application.StartupPath, "Resources", "Icons", "app-icon.ico");
                if (File.Exists(iconPath))
                {
                    this.Icon = new Icon(iconPath);
                }
            }
            catch { }
        }

        private void SplashForm_Paint(object sender, PaintEventArgs e)
        {
            // Draw border
            using (var pen = new Pen(Color.FromArgb(200, 200, 200), 2))
            {
                e.Graphics.DrawRectangle(pen, 0, 0, this.Width - 1, this.Height - 1);
            }
        }

        private void StartLoading()
        {
            timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            _progressValue += 20;
            progressBar.Value = Math.Min(_progressValue, 100);
            
            // Update status message
            var messageIndex = Math.Min(_progressValue / 20, _loadingMessages.Length - 1);
            if (messageIndex < _loadingMessages.Length)
            {
                lblStatus.Text = _loadingMessages[messageIndex];
            }
            
            // Close splash screen when loading is complete
            if (_progressValue >= 100)
            {
                timer.Stop();
                
                // Add a small delay before closing
                Task.Delay(500).ContinueWith(_ =>
                {
                    this.Invoke(new Action(() =>
                    {
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }));
                });
            }
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            
            // Add fade-in effect
            this.Opacity = 0;
            var fadeTimer = new Timer { Interval = 50 };
            fadeTimer.Tick += (s, args) =>
            {
                if (this.Opacity < 1)
                {
                    this.Opacity += 0.1;
                }
                else
                {
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
            };
            fadeTimer.Start();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Add fade-out effect
            if (e.CloseReason == CloseReason.UserClosing && this.Opacity > 0)
            {
                e.Cancel = true;
                var fadeTimer = new Timer { Interval = 50 };
                fadeTimer.Tick += (s, args) =>
                {
                    if (this.Opacity > 0)
                    {
                        this.Opacity -= 0.1;
                    }
                    else
                    {
                        fadeTimer.Stop();
                        fadeTimer.Dispose();
                        base.OnFormClosing(new FormClosingEventArgs(CloseReason.ApplicationExitCall, false));
                    }
                };
                fadeTimer.Start();
            }
            else
            {
                base.OnFormClosing(e);
            }
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            // Allow ESC to close splash screen
            if (e.KeyCode == Keys.Escape)
            {
                timer.Stop();
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            base.OnKeyDown(e);
        }

        protected override void OnClick(EventArgs e)
        {
            // Allow click to close splash screen
            timer.Stop();
            this.DialogResult = DialogResult.OK;
            this.Close();
            base.OnClick(e);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                timer?.Dispose();
                picLogo?.Image?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
