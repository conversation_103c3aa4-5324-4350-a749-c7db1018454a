@echo off
echo ========================================
echo فحص متطلبات النظام
echo System Requirements Check
echo ========================================
echo.

set ERRORS=0
set WARNINGS=0

echo التحقق من نظام التشغيل...
echo Checking operating system...

:: Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo إصدار Windows: %VERSION%
echo Windows Version: %VERSION%

if "%VERSION%" LSS "10.0" (
    echo تحذير: يُنصح بـ Windows 10 أو أحدث
    echo Warning: Windows 10 or newer is recommended
    set /a WARNINGS+=1
) else (
    echo ✓ نظام التشغيل مدعوم
    echo ✓ Operating system supported
)
echo.

echo التحقق من .NET...
echo Checking .NET...

:: Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ .NET غير مثبت
    echo ✗ .NET is not installed
    echo يرجى تثبيت .NET 8.0 من: https://dotnet.microsoft.com/download/dotnet/8.0
    echo Please install .NET 8.0 from: https://dotnet.microsoft.com/download/dotnet/8.0
    set /a ERRORS+=1
) else (
    for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
    echo ✓ .NET مثبت - الإصدار: %DOTNET_VERSION%
    echo ✓ .NET installed - Version: %DOTNET_VERSION%
    
    :: Check if it's .NET 8.0 or higher
    for /f "tokens=1 delims=." %%i in ("%DOTNET_VERSION%") do set MAJOR_VERSION=%%i
    if %MAJOR_VERSION% LSS 8 (
        echo تحذير: يُنصح بـ .NET 8.0 أو أحدث
        echo Warning: .NET 8.0 or newer is recommended
        set /a WARNINGS+=1
    )
)
echo.

echo التحقق من SQL Server...
echo Checking SQL Server...

:: Check if SQL Server LocalDB is available
sqlcmd -S "(localdb)\MSSQLLocalDB" -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: SQL Server LocalDB غير متوفر
    echo Warning: SQL Server LocalDB not available
    echo يُنصح بتثبيت SQL Server Express
    echo Recommended to install SQL Server Express
    set /a WARNINGS+=1
) else (
    echo ✓ SQL Server LocalDB متوفر
    echo ✓ SQL Server LocalDB available
)
echo.

echo التحقق من الذاكرة...
echo Checking memory...

:: Get total physical memory
for /f "skip=1 tokens=2" %%i in ('wmic computersystem get TotalPhysicalMemory /value') do (
    if not "%%i"=="" (
        set /a MEMORY_GB=%%i/1024/1024/1024
        goto memory_check
    )
)

:memory_check
echo إجمالي الذاكرة: %MEMORY_GB% GB
echo Total Memory: %MEMORY_GB% GB

if %MEMORY_GB% LSS 4 (
    echo تحذير: الحد الأدنى للذاكرة 4 GB
    echo Warning: Minimum 4 GB RAM required
    set /a WARNINGS+=1
) else if %MEMORY_GB% LSS 8 (
    echo تحذير: يُنصح بـ 8 GB أو أكثر للأداء الأمثل
    echo Warning: 8 GB or more recommended for optimal performance
    set /a WARNINGS+=1
) else (
    echo ✓ الذاكرة كافية
    echo ✓ Sufficient memory
)
echo.

echo التحقق من مساحة القرص...
echo Checking disk space...

:: Get free space on C: drive
for /f "tokens=3" %%i in ('dir C:\ /-c ^| find "bytes free"') do set FREE_SPACE=%%i
set /a FREE_SPACE_GB=%FREE_SPACE:~0,-10%/1024/1024/1024

echo المساحة المتاحة: %FREE_SPACE_GB% GB
echo Available Space: %FREE_SPACE_GB% GB

if %FREE_SPACE_GB% LSS 2 (
    echo ✗ مساحة القرص غير كافية (الحد الأدنى 2 GB)
    echo ✗ Insufficient disk space (minimum 2 GB required)
    set /a ERRORS+=1
) else if %FREE_SPACE_GB% LSS 5 (
    echo تحذير: يُنصح بـ 5 GB أو أكثر
    echo Warning: 5 GB or more recommended
    set /a WARNINGS+=1
) else (
    echo ✓ مساحة القرص كافية
    echo ✓ Sufficient disk space
)
echo.

echo التحقق من الأدوات الاختيارية...
echo Checking optional tools...

:: Check Git
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: Git غير مثبت (اختياري للتطوير)
    echo Warning: Git not installed (optional for development)
    set /a WARNINGS+=1
) else (
    echo ✓ Git مثبت
    echo ✓ Git installed
)

:: Check Visual Studio Code
code --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: Visual Studio Code غير مثبت (اختياري)
    echo Warning: Visual Studio Code not installed (optional)
    set /a WARNINGS+=1
) else (
    echo ✓ Visual Studio Code مثبت
    echo ✓ Visual Studio Code installed
)

:: Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: Docker غير مثبت (اختياري)
    echo Warning: Docker not installed (optional)
    set /a WARNINGS+=1
) else (
    echo ✓ Docker مثبت
    echo ✓ Docker installed
)
echo.

echo التحقق من الشبكة...
echo Checking network...

:: Test internet connectivity
ping -n 1 google.com >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: لا يوجد اتصال بالإنترنت
    echo Warning: No internet connection
    echo قد تحتاج للإنترنت لتحميل الحزم
    echo Internet may be needed to download packages
    set /a WARNINGS+=1
) else (
    echo ✓ الاتصال بالإنترنت متوفر
    echo ✓ Internet connection available
)
echo.

echo التحقق من الأذونات...
echo Checking permissions...

:: Test write permissions
echo test > test_file.tmp 2>nul
if exist test_file.tmp (
    del test_file.tmp
    echo ✓ أذونات الكتابة متوفرة
    echo ✓ Write permissions available
) else (
    echo تحذير: قد تحتاج لتشغيل التطبيق كمدير
    echo Warning: May need to run application as administrator
    set /a WARNINGS+=1
)
echo.

echo ========================================
echo نتائج الفحص
echo Check Results
echo ========================================
echo.

if %ERRORS%==0 (
    echo ✓ جميع المتطلبات الأساسية متوفرة
    echo ✓ All basic requirements met
) else (
    echo ✗ يوجد %ERRORS% خطأ يجب إصلاحه
    echo ✗ %ERRORS% error(s) need to be fixed
)

if %WARNINGS%==0 (
    echo ✓ لا توجد تحذيرات
    echo ✓ No warnings
) else (
    echo ⚠ يوجد %WARNINGS% تحذير
    echo ⚠ %WARNINGS% warning(s)
)
echo.

if %ERRORS%==0 (
    echo النظام جاهز لتشغيل التطبيق!
    echo System ready to run the application!
    echo.
    echo للتشغيل:
    echo To run:
    echo   run.bat
    echo.
    echo للتطوير:
    echo For development:
    echo   dev-setup.bat
) else (
    echo يرجى إصلاح الأخطاء قبل المتابعة
    echo Please fix errors before proceeding
)

echo.
echo للحصول على المساعدة:
echo For help:
echo   البريد الإلكتروني / Email: <EMAIL>
echo   الموقع / Website: www.lawfirmsolutions.ma
echo.

pause
