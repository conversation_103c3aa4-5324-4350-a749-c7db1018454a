using LawFirmManagementSystem.Models.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Interfaces
{
    /// <summary>
    /// واجهة خدمة المستخدمين
    /// User service interface
    /// </summary>
    public interface IUserService
    {
        // User Management
        Task<User?> GetUserByIdAsync(int id);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<User?> GetUserByEmailAsync(string email);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<IEnumerable<User>> GetActiveUsersAsync();
        Task<User> CreateUserAsync(User user, string password);
        Task<User> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> ActivateUserAsync(int id);
        Task<bool> DeactivateUserAsync(int id);

        // Authentication
        Task<User?> AuthenticateAsync(string username, string password);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(int userId, string newPassword);
        Task<bool> ValidatePasswordAsync(string password);

        // User Sessions
        Task<UserSession> CreateSessionAsync(int userId, string ipAddress, string userAgent);
        Task<bool> EndSessionAsync(string sessionToken);
        Task<bool> EndAllUserSessionsAsync(int userId);
        Task<UserSession?> GetSessionAsync(string sessionToken);
        Task<IEnumerable<UserSession>> GetUserSessionsAsync(int userId);
        Task<bool> UpdateSessionActivityAsync(string sessionToken);

        // User Permissions
        Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId);
        Task<bool> HasPermissionAsync(int userId, string module, string action);
        Task<bool> GrantPermissionAsync(int userId, string permissionName, string module, string action);
        Task<bool> RevokePermissionAsync(int userId, string permissionName);
        Task<bool> UpdatePermissionsAsync(int userId, IEnumerable<UserPermission> permissions);

        // User Profile
        Task<bool> UpdateProfileAsync(int userId, string fullName, string? fullNameFr, string? email, string? phone);
        Task<bool> UpdatePreferencesAsync(int userId, string language, string theme, int sessionTimeout);
        Task<bool> UpdateProfilePictureAsync(int userId, string picturePath);

        // Security
        Task<bool> LockUserAsync(int userId, int lockoutMinutes, string reason);
        Task<bool> UnlockUserAsync(int userId);
        Task<bool> IsUserLockedAsync(int userId);
        Task<bool> EnableTwoFactorAsync(int userId, string secret);
        Task<bool> DisableTwoFactorAsync(int userId);
        Task<bool> VerifyTwoFactorAsync(int userId, string code);

        // User Statistics
        Task<int> GetTotalUsersCountAsync();
        Task<int> GetActiveUsersCountAsync();
        Task<int> GetOnlineUsersCountAsync();
        Task<IEnumerable<User>> GetRecentlyActiveUsersAsync(int count = 10);

        // Search and Filter
        Task<IEnumerable<User>> SearchUsersAsync(string searchTerm);
        Task<IEnumerable<User>> GetUsersByRoleAsync(string role);
        Task<IEnumerable<User>> GetUsersByDepartmentAsync(string department);

        // Validation
        Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null);
        Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null);
        Task<bool> ValidateUserDataAsync(User user);
    }
}
