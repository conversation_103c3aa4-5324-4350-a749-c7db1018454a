using LawFirmManagementSystem.Data;
using LawFirmManagementSystem.Models.Entities;
using LawFirmManagementSystem.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة التدقيق
    /// Audit service implementation
    /// </summary>
    public class AuditService : IAuditService
    {
        private readonly LawFirmDbContext _context;
        private readonly ILogger<AuditService> _logger;

        public AuditService(LawFirmDbContext context, ILogger<AuditService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task LogAsync(int userId, string action, string entityType, int? entityId = null, 
            string? description = null, string? oldValues = null, string? newValues = null, 
            string severity = "Information")
        {
            try
            {
                var auditLog = new AuditLog
                {
                    UserId = userId,
                    Action = action,
                    EntityType = entityType,
                    EntityId = entityId,
                    Description = description,
                    OldValues = oldValues,
                    NewValues = newValues,
                    Severity = severity,
                    Timestamp = DateTime.Now,
                    IsSuccessful = severity != "خطأ" && severity != "Error"
                };

                await LogAsync(auditLog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging audit event for user {UserId}, action {Action}", userId, action);
            }
        }

        public async Task LogAsync(AuditLog auditLog)
        {
            try
            {
                if (auditLog == null)
                    throw new ArgumentNullException(nameof(auditLog));

                // Set additional properties
                if (auditLog.Timestamp == default)
                    auditLog.Timestamp = DateTime.Now;

                // Get client information if available
                // This would typically come from HttpContext in a web application
                // For WinForms, we can get machine information
                auditLog.IpAddress = GetLocalIPAddress();
                auditLog.DeviceName = Environment.MachineName;
                auditLog.OperatingSystem = Environment.OSVersion.ToString();

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();

                _logger.LogDebug("Audit log created: {Action} by user {UserId} on {EntityType} {EntityId}", 
                    auditLog.Action, auditLog.UserId, auditLog.EntityType, auditLog.EntityId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving audit log to database");
                // Don't throw here to avoid breaking the main operation
            }
        }

        public async Task<IEnumerable<AuditLog>> GetUserAuditLogsAsync(int userId, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                return await _context.AuditLogs
                    .Where(a => a.UserId == userId)
                    .OrderByDescending(a => a.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Include(a => a.User)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs for user {UserId}", userId);
                return Enumerable.Empty<AuditLog>();
            }
        }

        public async Task<IEnumerable<AuditLog>> GetEntityAuditLogsAsync(string entityType, int entityId, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                return await _context.AuditLogs
                    .Where(a => a.EntityType == entityType && a.EntityId == entityId)
                    .OrderByDescending(a => a.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Include(a => a.User)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs for entity {EntityType} {EntityId}", entityType, entityId);
                return Enumerable.Empty<AuditLog>();
            }
        }

        public async Task<IEnumerable<AuditLog>> GetAuditLogsByDateRangeAsync(DateTime fromDate, DateTime toDate, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                return await _context.AuditLogs
                    .Where(a => a.Timestamp >= fromDate && a.Timestamp <= toDate)
                    .OrderByDescending(a => a.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Include(a => a.User)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs for date range {FromDate} to {ToDate}", fromDate, toDate);
                return Enumerable.Empty<AuditLog>();
            }
        }

        public async Task<IEnumerable<AuditLog>> GetAuditLogsByActionAsync(string action, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                return await _context.AuditLogs
                    .Where(a => a.Action == action)
                    .OrderByDescending(a => a.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Include(a => a.User)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs for action {Action}", action);
                return Enumerable.Empty<AuditLog>();
            }
        }

        public async Task<IEnumerable<AuditLog>> SearchAuditLogsAsync(string searchTerm, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<AuditLog>();

                return await _context.AuditLogs
                    .Where(a => a.Description!.Contains(searchTerm) ||
                               a.Action.Contains(searchTerm) ||
                               a.EntityType.Contains(searchTerm) ||
                               a.User.FullName.Contains(searchTerm))
                    .OrderByDescending(a => a.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Include(a => a.User)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching audit logs with term {SearchTerm}", searchTerm);
                return Enumerable.Empty<AuditLog>();
            }
        }

        public async Task<AuditStatistics> GetAuditStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.AuditLogs.AsQueryable();

                if (fromDate.HasValue)
                    query = query.Where(a => a.Timestamp >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(a => a.Timestamp <= toDate.Value);

                var logs = await query.ToListAsync();

                var statistics = new AuditStatistics
                {
                    TotalLogs = logs.Count,
                    TotalUsers = logs.Select(a => a.UserId).Distinct().Count(),
                    TotalActions = logs.Select(a => a.Action).Distinct().Count(),
                    ErrorCount = logs.Count(a => a.Severity == "خطأ" || a.Severity == "Error"),
                    WarningCount = logs.Count(a => a.Severity == "تحذير" || a.Severity == "Warning"),
                    InfoCount = logs.Count(a => a.Severity == "معلومات" || a.Severity == "Information"),
                    FirstLogDate = logs.Any() ? logs.Min(a => a.Timestamp) : null,
                    LastLogDate = logs.Any() ? logs.Max(a => a.Timestamp) : null
                };

                // Action counts
                statistics.ActionCounts = logs
                    .GroupBy(a => a.Action)
                    .ToDictionary(g => g.Key, g => g.Count());

                // User counts
                statistics.UserCounts = logs
                    .GroupBy(a => a.UserId)
                    .ToDictionary(g => g.Key.ToString(), g => g.Count());

                // Entity counts
                statistics.EntityCounts = logs
                    .GroupBy(a => a.EntityType)
                    .ToDictionary(g => g.Key, g => g.Count());

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating audit statistics");
                return new AuditStatistics();
            }
        }

        public async Task<int> CleanupOldLogsAsync(int retentionDays = 365)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var oldLogs = await _context.AuditLogs
                    .Where(a => a.Timestamp < cutoffDate && !a.IsArchived)
                    .ToListAsync();

                if (oldLogs.Any())
                {
                    _context.AuditLogs.RemoveRange(oldLogs);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} old audit logs older than {CutoffDate}", oldLogs.Count, cutoffDate);
                }

                return oldLogs.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old audit logs");
                return 0;
            }
        }

        public async Task<byte[]> ExportAuditLogsAsync(DateTime fromDate, DateTime toDate, string format = "Excel")
        {
            try
            {
                var logs = await _context.AuditLogs
                    .Where(a => a.Timestamp >= fromDate && a.Timestamp <= toDate)
                    .Include(a => a.User)
                    .OrderByDescending(a => a.Timestamp)
                    .ToListAsync();

                // TODO: Implement export functionality based on format
                // This would use libraries like EPPlus for Excel or iTextSharp for PDF
                
                _logger.LogInformation("Exported {Count} audit logs from {FromDate} to {ToDate} in {Format} format", 
                    logs.Count, fromDate, toDate, format);

                return Array.Empty<byte>(); // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting audit logs");
                return Array.Empty<byte>();
            }
        }

        public async Task<bool> ReviewAuditLogAsync(int auditLogId, int reviewedBy, string? notes = null)
        {
            try
            {
                var auditLog = await _context.AuditLogs.FindAsync(auditLogId);
                if (auditLog == null)
                    return false;

                auditLog.ReviewLog(reviewedBy, notes);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Audit log {AuditLogId} reviewed by user {ReviewedBy}", auditLogId, reviewedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reviewing audit log {AuditLogId}", auditLogId);
                return false;
            }
        }

        public async Task<bool> ArchiveAuditLogsAsync(DateTime beforeDate)
        {
            try
            {
                var logsToArchive = await _context.AuditLogs
                    .Where(a => a.Timestamp < beforeDate && !a.IsArchived)
                    .ToListAsync();

                foreach (var log in logsToArchive)
                {
                    log.ArchiveLog();
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Archived {Count} audit logs before {BeforeDate}", logsToArchive.Count, beforeDate);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving audit logs");
                return false;
            }
        }

        private string GetLocalIPAddress()
        {
            try
            {
                return System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
                    .AddressList
                    .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    ?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }
    }
}
