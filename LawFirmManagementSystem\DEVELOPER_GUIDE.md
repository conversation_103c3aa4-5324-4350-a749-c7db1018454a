# دليل المطور / Developer Guide

## نظام إدارة مكتب المحاماة / Law Firm Management System

### نظرة عامة / Overview

هذا دليل شامل للمطورين الذين يرغبون في المساهمة في تطوير نظام إدارة مكتب المحاماة أو فهم بنيته التقنية.

This is a comprehensive guide for developers who want to contribute to the Law Firm Management System or understand its technical architecture.

### بنية المشروع / Project Architecture

#### هيكل المجلدات / Folder Structure
```
LawFirmManagementSystem/
├── Data/                    # طبقة البيانات / Data Layer
│   ├── LawFirmDbContext.cs
│   └── Migrations/
├── Models/                  # النماذج / Models
│   ├── Entities/           # كيانات قاعدة البيانات / Database Entities
│   ├── DTOs/              # كائنات نقل البيانات / Data Transfer Objects
│   └── ViewModels/        # نماذج العرض / View Models
├── Services/               # طبقة الخدمات / Services Layer
│   ├── Interfaces/        # واجهات الخدمات / Service Interfaces
│   └── Implementations/   # تنفيذ الخدمات / Service Implementations
├── Forms/                  # نماذج WinForms / WinForms
│   ├── Main/              # النماذج الرئيسية / Main Forms
│   ├── Clients/           # نماذج العملاء / Client Forms
│   ├── Cases/             # نماذج القضايا / Case Forms
│   └── Common/            # النماذج المشتركة / Common Forms
├── Utils/                  # الأدوات المساعدة / Utilities
├── Resources/              # الموارد / Resources
│   ├── Icons/             # الأيقونات / Icons
│   ├── Images/            # الصور / Images
│   └── Localization/      # ملفات الترجمة / Localization Files
├── Templates/              # قوالب التقارير / Report Templates
└── Logs/                   # ملفات السجلات / Log Files
```

#### الطبقات المعمارية / Architectural Layers

1. **طبقة العرض / Presentation Layer**
   - WinForms للواجهة الرسومية
   - تطبيق نمط MVP (Model-View-Presenter)
   - دعم RTL/LTR للغات

2. **طبقة الخدمات / Service Layer**
   - خدمات الأعمال / Business Services
   - خدمات البنية التحتية / Infrastructure Services
   - حقن التبعيات / Dependency Injection

3. **طبقة البيانات / Data Layer**
   - Entity Framework Core
   - Repository Pattern
   - Unit of Work Pattern

4. **طبقة النماذج / Model Layer**
   - Domain Entities
   - DTOs
   - ViewModels

### متطلبات التطوير / Development Requirements

#### الأدوات المطلوبة / Required Tools
- **Visual Studio 2022** (Community أو أعلى)
- **.NET 8.0 SDK**
- **SQL Server** (Express أو أعلى)
- **Git** لإدارة الإصدارات

#### الأدوات الاختيارية / Optional Tools
- **Visual Studio Code** مع إضافة C#
- **SQL Server Management Studio**
- **Postman** لاختبار APIs
- **Docker** للحاويات

### إعداد بيئة التطوير / Development Environment Setup

#### 1. استنساخ المشروع / Clone Project
```bash
git clone https://github.com/your-repo/LawFirmManagementSystem.git
cd LawFirmManagementSystem
```

#### 2. تشغيل إعداد التطوير / Run Development Setup
```bash
# على Windows
dev-setup.bat

# أو يدوياً
dotnet restore
dotnet build
```

#### 3. إعداد قاعدة البيانات / Database Setup
```bash
# إنشاء قاعدة البيانات
dotnet ef database update

# أو إنشاء migration جديد
dotnet ef migrations add InitialCreate
```

#### 4. تشغيل التطبيق / Run Application
```bash
# بيئة التطوير
dotnet run --environment Development

# أو من Visual Studio: F5
```

### معايير الكود / Coding Standards

#### تسمية الملفات والمجلدات / File and Folder Naming
- استخدم PascalCase للفئات والواجهات
- استخدم camelCase للمتغيرات والمعاملات
- استخدم kebab-case لأسماء الملفات
- أضف تعليقات باللغتين العربية والإنجليزية

#### مثال على التعليقات / Comment Example
```csharp
/// <summary>
/// خدمة إدارة العملاء
/// Client management service
/// </summary>
public class ClientService : IClientService
{
    /// <summary>
    /// إضافة عميل جديد
    /// Add new client
    /// </summary>
    /// <param name="client">بيانات العميل / Client data</param>
    /// <returns>العميل المضاف / Added client</returns>
    public async Task<Client> AddClientAsync(Client client)
    {
        // تنفيذ الكود هنا
        // Implementation here
    }
}
```

#### معايير C# / C# Standards
- اتبع Microsoft C# Coding Conventions
- استخدم async/await للعمليات غير المتزامنة
- استخدم using statements للموارد
- تجنب الكود المكرر (DRY Principle)
- طبق مبادئ SOLID

### أنماط التصميم المستخدمة / Design Patterns Used

#### 1. Dependency Injection
```csharp
// في Program.cs
services.AddScoped<IClientService, ClientService>();
services.AddScoped<ICaseService, CaseService>();
```

#### 2. Repository Pattern
```csharp
public interface IRepository<T> where T : class
{
    Task<T> GetByIdAsync(int id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<T> AddAsync(T entity);
    Task UpdateAsync(T entity);
    Task DeleteAsync(int id);
}
```

#### 3. Unit of Work Pattern
```csharp
public interface IUnitOfWork : IDisposable
{
    IRepository<Client> Clients { get; }
    IRepository<Case> Cases { get; }
    Task<int> SaveChangesAsync();
}
```

#### 4. Factory Pattern
```csharp
public interface IFormFactory
{
    T CreateForm<T>() where T : Form;
}
```

### إدارة قاعدة البيانات / Database Management

#### Entity Framework Migrations
```bash
# إضافة migration جديد
dotnet ef migrations add MigrationName

# تطبيق migrations
dotnet ef database update

# إزالة آخر migration
dotnet ef migrations remove

# إنشاء script SQL
dotnet ef migrations script
```

#### إعداد DbContext
```csharp
public class LawFirmDbContext : DbContext
{
    public DbSet<Client> Clients { get; set; }
    public DbSet<Case> Cases { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // إعداد العلاقات والقيود
        // Configure relationships and constraints
        base.OnModelCreating(modelBuilder);
    }
}
```

### اختبار الكود / Code Testing

#### Unit Tests
```csharp
[TestClass]
public class ClientServiceTests
{
    [TestMethod]
    public async Task AddClient_ValidClient_ReturnsClient()
    {
        // Arrange
        var client = new Client { Name = "Test Client" };
        
        // Act
        var result = await _clientService.AddClientAsync(client);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Test Client", result.Name);
    }
}
```

#### Integration Tests
```csharp
[TestClass]
public class ClientControllerIntegrationTests
{
    [TestMethod]
    public async Task GetClients_ReturnsAllClients()
    {
        // اختبار التكامل مع قاعدة البيانات
        // Integration test with database
    }
}
```

### التوطين / Localization

#### إضافة ترجمات جديدة / Adding New Translations
```csharp
// في LocalizationService
private void InitializeTranslations()
{
    var arTranslations = new Dictionary<string, string>
    {
        {"NewKey", "النص العربي"},
        // ...
    };
    
    var frTranslations = new Dictionary<string, string>
    {
        {"NewKey", "Texte français"},
        // ...
    };
}
```

#### استخدام الترجمة في الكود / Using Localization in Code
```csharp
// في النماذج
var localizedText = _localizationService.GetLocalizedString("KeyName");
label.Text = localizedText;

// مع معاملات
var message = _localizationService.GetLocalizedString("WelcomeMessage", 
    new object[] { userName });
```

### إدارة الأخطاء / Error Handling

#### Logging
```csharp
public class ClientService
{
    private readonly ILogger<ClientService> _logger;
    
    public async Task<Client> AddClientAsync(Client client)
    {
        try
        {
            _logger.LogInformation("Adding new client: {ClientName}", client.Name);
            // تنفيذ الكود
            _logger.LogInformation("Client added successfully: {ClientId}", client.Id);
            return client;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding client: {ClientName}", client.Name);
            throw;
        }
    }
}
```

#### Exception Handling
```csharp
public class GlobalExceptionHandler
{
    public static void HandleException(Exception ex)
    {
        // تسجيل الخطأ
        // Log error
        
        // عرض رسالة للمستخدم
        // Show user message
        
        // إرسال تقرير الخطأ (اختياري)
        // Send error report (optional)
    }
}
```

### الأمان / Security

#### تشفير البيانات / Data Encryption
```csharp
public class EncryptionService : IEncryptionService
{
    public string Encrypt(string plainText)
    {
        // تنفيذ التشفير
        // Encryption implementation
    }
    
    public string Decrypt(string cipherText)
    {
        // تنفيذ فك التشفير
        // Decryption implementation
    }
}
```

#### التحقق من الصلاحيات / Permission Checking
```csharp
[RequirePermission("Clients", "Read")]
public async Task<IEnumerable<Client>> GetClientsAsync()
{
    // تنفيذ الكود
    // Implementation
}
```

### الأداء / Performance

#### تحسين الاستعلامات / Query Optimization
```csharp
// استخدام Include للتحميل المسبق
var clients = await _context.Clients
    .Include(c => c.Cases)
    .Where(c => c.IsActive)
    .ToListAsync();

// استخدام AsNoTracking للقراءة فقط
var readOnlyClients = await _context.Clients
    .AsNoTracking()
    .ToListAsync();
```

#### التخزين المؤقت / Caching
```csharp
public async Task<Client> GetClientAsync(int id)
{
    var cacheKey = $"client_{id}";
    var cachedClient = await _cache.GetAsync<Client>(cacheKey);
    
    if (cachedClient == null)
    {
        cachedClient = await _repository.GetByIdAsync(id);
        await _cache.SetAsync(cacheKey, cachedClient, TimeSpan.FromMinutes(30));
    }
    
    return cachedClient;
}
```

### النشر / Deployment

#### بناء للإنتاج / Production Build
```bash
# بناء للإنتاج
dotnet publish -c Release -r win-x64 --self-contained

# إنشاء installer
# Create installer using tools like Inno Setup or WiX
```

#### إعداد قاعدة البيانات للإنتاج / Production Database Setup
```bash
# تطبيق migrations على الإنتاج
dotnet ef database update --environment Production
```

### المساهمة في المشروع / Contributing

#### خطوات المساهمة / Contribution Steps
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. تطوير الميزة مع الاختبارات
4. Commit التغييرات مع رسائل واضحة
5. Push إلى الفرع
6. إنشاء Pull Request

#### معايير Pull Request
- وصف واضح للتغييرات
- اختبارات للميزات الجديدة
- توثيق للتغييرات
- اتباع معايير الكود

### الدعم الفني / Technical Support

#### للمطورين / For Developers
- **البريد الإلكتروني / Email**: <EMAIL>
- **Discord**: [رابط الخادم / Server Link]
- **GitHub Issues**: لتقارير الأخطاء والطلبات

#### الموارد المفيدة / Useful Resources
- [Microsoft .NET Documentation](https://docs.microsoft.com/dotnet/)
- [Entity Framework Core Documentation](https://docs.microsoft.com/ef/core/)
- [WinForms Documentation](https://docs.microsoft.com/dotnet/desktop/winforms/)

---

**ملاحظة / Note**: هذا الدليل في تطوير مستمر. يرجى المساهمة في تحسينه.

This guide is under continuous development. Please contribute to its improvement.
