using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LawFirmManagementSystem.Models.Entities
{
    /// <summary>
    /// كيان عنصر الفاتورة
    /// Invoice item entity
    /// </summary>
    [Table("InvoiceItems")]
    public class InvoiceItem : BaseEntity
    {
        [Required]
        [Column("InvoiceId")]
        public int InvoiceId { get; set; }

        [Required]
        [StringLength(200)]
        [Column("Description")]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        [Column("DescriptionFr")]
        public string? DescriptionFr { get; set; }

        [Required]
        [Column("Quantity")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Quantity { get; set; } = 1;

        [Required]
        [Column("UnitPrice")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Required]
        [Column("TotalPrice")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }

        [StringLength(50)]
        [Column("Unit")]
        public string? Unit { get; set; } // ساعة، يوم، خدمة

        [StringLength(50)]
        [Column("UnitFr")]
        public string? UnitFr { get; set; }

        [StringLength(50)]
        [Column("ItemType")]
        public string? ItemType { get; set; } // أتعاب، مصروف، خدمة

        [StringLength(50)]
        [Column("ItemTypeFr")]
        public string? ItemTypeFr { get; set; }

        [Column("TaxRate")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 20;

        [Column("TaxAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column("DiscountRate")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountRate { get; set; } = 0;

        [Column("DiscountAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column("NetAmount")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }

        [Column("SortOrder")]
        public int SortOrder { get; set; } = 0;

        [Column("IsTaxable")]
        public bool IsTaxable { get; set; } = true;

        [Column("IsDiscountable")]
        public bool IsDiscountable { get; set; } = true;

        // Navigation Properties
        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;

        /// <summary>
        /// الحصول على الوصف حسب اللغة
        /// Get description by language
        /// </summary>
        public string GetDescription(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(DescriptionFr) 
                ? DescriptionFr 
                : Description;
        }

        /// <summary>
        /// الحصول على الوحدة حسب اللغة
        /// Get unit by language
        /// </summary>
        public string GetUnit(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(UnitFr) 
                ? UnitFr 
                : Unit ?? string.Empty;
        }

        /// <summary>
        /// الحصول على نوع العنصر حسب اللغة
        /// Get item type by language
        /// </summary>
        public string GetItemType(string language = "ar")
        {
            return language.ToLower() == "fr" && !string.IsNullOrEmpty(ItemTypeFr) 
                ? ItemTypeFr 
                : ItemType ?? string.Empty;
        }

        /// <summary>
        /// حساب السعر الإجمالي
        /// Calculate total price
        /// </summary>
        public void CalculateTotalPrice()
        {
            TotalPrice = Quantity * UnitPrice;
        }

        /// <summary>
        /// حساب الضريبة
        /// Calculate tax
        /// </summary>
        public void CalculateTax()
        {
            if (IsTaxable)
            {
                TaxAmount = TotalPrice * (TaxRate / 100);
            }
            else
            {
                TaxAmount = 0;
            }
        }

        /// <summary>
        /// حساب الخصم
        /// Calculate discount
        /// </summary>
        public void CalculateDiscount()
        {
            if (IsDiscountable && DiscountRate > 0)
            {
                DiscountAmount = TotalPrice * (DiscountRate / 100);
            }
            else
            {
                DiscountAmount = 0;
            }
        }

        /// <summary>
        /// حساب المبلغ الصافي
        /// Calculate net amount
        /// </summary>
        public void CalculateNetAmount()
        {
            CalculateTotalPrice();
            CalculateTax();
            CalculateDiscount();
            NetAmount = TotalPrice + TaxAmount - DiscountAmount;
        }
    }
}
