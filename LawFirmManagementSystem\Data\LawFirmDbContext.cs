using Microsoft.EntityFrameworkCore;
using LawFirmManagementSystem.Models.Entities;
using System.Reflection;

namespace LawFirmManagementSystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي لنظام إدارة مكتب المحاماة
    /// Main database context for Law Firm Management System
    /// </summary>
    public class LawFirmDbContext : DbContext
    {
        public LawFirmDbContext(DbContextOptions<LawFirmDbContext> options) : base(options)
        {
        }

        // User Management
        public DbSet<User> Users { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        // Client Management
        public DbSet<Client> Clients { get; set; }
        public DbSet<ClientContact> ClientContacts { get; set; }

        // Case Management
        public DbSet<LegalCase> LegalCases { get; set; }
        public DbSet<CaseType> CaseTypes { get; set; }
        public DbSet<Court> Courts { get; set; }
        public DbSet<Opponent> Opponents { get; set; }
        public DbSet<CaseNote> CaseNotes { get; set; }
        public DbSet<CaseTask> CaseTasks { get; set; }
        public DbSet<CaseTimeline> CaseTimeline { get; set; }
        public DbSet<CaseExpense> CaseExpenses { get; set; }

        // Calendar & Scheduling
        public DbSet<Hearing> Hearings { get; set; }
        public DbSet<Appointment> Appointments { get; set; }

        // Financial Management
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Payment> Payments { get; set; }

        // Document Management
        public DbSet<Document> Documents { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply all entity configurations from the current assembly
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Configure decimal precision globally
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(decimal) || property.ClrType == typeof(decimal?))
                    {
                        property.SetColumnType("decimal(18,2)");
                    }
                }
            }

            // Configure string length defaults
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(string) && property.GetMaxLength() == null)
                    {
                        property.SetMaxLength(255);
                    }
                }
            }

            // Configure indexes for better performance
            ConfigureIndexes(modelBuilder);

            // Configure relationships
            ConfigureRelationships(modelBuilder);

            // Configure soft delete global filter
            ConfigureSoftDelete(modelBuilder);

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // User indexes
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();
            
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email);

            // Client indexes
            modelBuilder.Entity<Client>()
                .HasIndex(c => c.ClientReference)
                .IsUnique();
            
            modelBuilder.Entity<Client>()
                .HasIndex(c => c.CIN);
            
            modelBuilder.Entity<Client>()
                .HasIndex(c => c.Email);

            // Case indexes
            modelBuilder.Entity<LegalCase>()
                .HasIndex(c => c.CaseReference)
                .IsUnique();
            
            modelBuilder.Entity<LegalCase>()
                .HasIndex(c => c.OfficeReference)
                .IsUnique();
            
            modelBuilder.Entity<LegalCase>()
                .HasIndex(c => c.CourtCaseNumber);

            // Invoice indexes
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();

            // Payment indexes
            modelBuilder.Entity<Payment>()
                .HasIndex(p => p.PaymentNumber)
                .IsUnique();

            // Document indexes
            modelBuilder.Entity<Document>()
                .HasIndex(d => d.FileHash);

            // Audit log indexes
            modelBuilder.Entity<AuditLog>()
                .HasIndex(a => new { a.UserId, a.Timestamp });
            
            modelBuilder.Entity<AuditLog>()
                .HasIndex(a => new { a.EntityType, a.EntityId });

            // Session indexes
            modelBuilder.Entity<UserSession>()
                .HasIndex(s => s.SessionToken)
                .IsUnique();
            
            modelBuilder.Entity<UserSession>()
                .HasIndex(s => new { s.UserId, s.Status });

            // Timeline indexes
            modelBuilder.Entity<CaseTimeline>()
                .HasIndex(t => new { t.CaseId, t.EventDate });

            // Contact indexes
            modelBuilder.Entity<ClientContact>()
                .HasIndex(c => new { c.ClientId, c.ContactDate });

            // Hearing indexes
            modelBuilder.Entity<Hearing>()
                .HasIndex(h => new { h.HearingDate, h.HearingTime });

            // Appointment indexes
            modelBuilder.Entity<Appointment>()
                .HasIndex(a => new { a.AppointmentDate, a.StartTime });
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // User relationships
            modelBuilder.Entity<UserSession>()
                .HasOne(s => s.User)
                .WithMany(u => u.Sessions)
                .HasForeignKey(s => s.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(p => p.User)
                .WithMany(u => u.Permissions)
                .HasForeignKey(p => p.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Case relationships
            modelBuilder.Entity<LegalCase>()
                .HasOne(c => c.Client)
                .WithMany(cl => cl.Cases)
                .HasForeignKey(c => c.ClientId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<LegalCase>()
                .HasOne(c => c.AssignedLawyer)
                .WithMany(u => u.AssignedCases)
                .HasForeignKey(c => c.AssignedLawyerId)
                .OnDelete(DeleteBehavior.SetNull);

            // Invoice relationships
            modelBuilder.Entity<InvoiceItem>()
                .HasOne(i => i.Invoice)
                .WithMany(inv => inv.Items)
                .HasForeignKey(i => i.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Invoice)
                .WithMany(i => i.Payments)
                .HasForeignKey(p => p.InvoiceId)
                .OnDelete(DeleteBehavior.SetNull);

            // Document relationships - prevent cascade delete
            modelBuilder.Entity<Document>()
                .HasOne(d => d.Case)
                .WithMany(c => c.Documents)
                .HasForeignKey(d => d.CaseId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Document>()
                .HasOne(d => d.Client)
                .WithMany(c => c.Documents)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureSoftDelete(ModelBuilder modelBuilder)
        {
            // Configure global query filter for soft delete
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var method = typeof(LawFirmDbContext)
                        .GetMethod(nameof(GetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)
                        ?.MakeGenericMethod(entityType.ClrType);
                    
                    var filter = method?.Invoke(null, Array.Empty<object>());
                    if (filter != null)
                    {
                        entityType.SetQueryFilter((LambdaExpression)filter);
                    }
                }
            }
        }

        private static LambdaExpression GetSoftDeleteFilter<TEntity>() where TEntity : BaseEntity
        {
            Expression<Func<TEntity, bool>> filter = x => !x.IsDeleted;
            return filter;
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = User.HashPassword("Admin@123"),
                    FullName = "مدير النظام",
                    FullNameFr = "Administrateur Système",
                    Email = "<EMAIL>",
                    Role = "Admin",
                    PreferredLanguage = "ar",
                    CreatedDate = DateTime.Now,
                    IsActive = true
                }
            );

            // Seed default case types
            var caseTypes = new[]
            {
                new CaseType { Id = 1, TypeName = "قضايا مدنية", TypeNameFr = "Affaires Civiles", Category = "مدني", CategoryFr = "Civil", CreatedDate = DateTime.Now, IsActive = true },
                new CaseType { Id = 2, TypeName = "قضايا جنائية", TypeNameFr = "Affaires Pénales", Category = "جنائي", CategoryFr = "Pénal", CreatedDate = DateTime.Now, IsActive = true },
                new CaseType { Id = 3, TypeName = "قضايا تجارية", TypeNameFr = "Affaires Commerciales", Category = "تجاري", CategoryFr = "Commercial", CreatedDate = DateTime.Now, IsActive = true },
                new CaseType { Id = 4, TypeName = "قضايا أسرية", TypeNameFr = "Affaires Familiales", Category = "أسري", CategoryFr = "Familial", CreatedDate = DateTime.Now, IsActive = true },
                new CaseType { Id = 5, TypeName = "قضايا إدارية", TypeNameFr = "Affaires Administratives", Category = "إداري", CategoryFr = "Administratif", CreatedDate = DateTime.Now, IsActive = true },
                new CaseType { Id = 6, TypeName = "قضايا عقارية", TypeNameFr = "Affaires Immobilières", Category = "عقاري", CategoryFr = "Immobilier", CreatedDate = DateTime.Now, IsActive = true },
                new CaseType { Id = 7, TypeName = "قضايا عمالية", TypeNameFr = "Affaires du Travail", Category = "عمالي", CategoryFr = "Travail", CreatedDate = DateTime.Now, IsActive = true }
            };
            modelBuilder.Entity<CaseType>().HasData(caseTypes);

            // Seed default courts
            var courts = new[]
            {
                new Court { Id = 1, CourtName = "المحكمة الابتدائية بالرباط", CourtNameFr = "Tribunal de Première Instance de Rabat", CourtType = "ابتدائية", CourtTypeFr = "Première Instance", City = "الرباط", CityFr = "Rabat", Jurisdiction = "الرباط سلا القنيطرة", JurisdictionFr = "Rabat-Salé-Kénitra", CreatedDate = DateTime.Now, IsActive = true },
                new Court { Id = 2, CourtName = "محكمة الاستئناف بالرباط", CourtNameFr = "Cour d'Appel de Rabat", CourtType = "استئناف", CourtTypeFr = "Appel", City = "الرباط", CityFr = "Rabat", Jurisdiction = "الرباط سلا القنيطرة", JurisdictionFr = "Rabat-Salé-Kénitra", CreatedDate = DateTime.Now, IsActive = true },
                new Court { Id = 3, CourtName = "المحكمة التجارية بالرباط", CourtNameFr = "Tribunal de Commerce de Rabat", CourtType = "تجارية", CourtTypeFr = "Commerce", City = "الرباط", CityFr = "Rabat", Jurisdiction = "الرباط سلا القنيطرة", JurisdictionFr = "Rabat-Salé-Kénitra", CreatedDate = DateTime.Now, IsActive = true }
            };
            modelBuilder.Entity<Court>().HasData(courts);
        }

        public override int SaveChanges()
        {
            UpdateAuditFields();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateAuditFields();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateAuditFields()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedDate = DateTime.Now;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdateModificationInfo();
                        break;
                }
            }
        }
    }
}
