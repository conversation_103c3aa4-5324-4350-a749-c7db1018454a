using System;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Services
{
    public class ErrorLogService
    {
        private readonly DatabaseService _databaseService;
        
        public ErrorLogService()
        {
            _databaseService = DatabaseService.Instance;
        }
        
        public async Task LogErrorAsync(string source, Exception exception, string? userName = null, int? userId = null)
        {
            try
            {
                var query = @"
                    INSERT INTO ErrorLogs (LogLevel, Message, Exception, StackTrace, Source, UserId, UserName, MachineName, LogDate)
                    VALUES (@LogLevel, @Message, @Exception, @StackTrace, @Source, @UserId, @UserName, @MachineName, @LogDate)";
                
                var parameters = new[]
                {
                    new SqlParameter("@LogLevel", "Error"),
                    new SqlParameter("@Message", exception.Message),
                    new SqlParameter("@Exception", exception.ToString()),
                    new SqlParameter("@StackTrace", exception.StackTrace ?? string.Empty),
                    new SqlParameter("@Source", source),
                    new SqlParameter("@UserId", (object?)userId ?? DBNull.Value),
                    new SqlParameter("@UserName", (object?)userName ?? DBNull.Value),
                    new SqlParameter("@MachineName", Environment.MachineName),
                    new SqlParameter("@LogDate", DateTime.Now)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch
            {
                // في حالة فشل تسجيل الخطأ في قاعدة البيانات، نحفظه في ملف
                await LogToFileAsync(source, exception, userName);
            }
        }
        
        public async Task LogWarningAsync(string source, string message, string? userName = null, int? userId = null)
        {
            try
            {
                var query = @"
                    INSERT INTO ErrorLogs (LogLevel, Message, Source, UserId, UserName, MachineName, LogDate)
                    VALUES (@LogLevel, @Message, @Source, @UserId, @UserName, @MachineName, @LogDate)";
                
                var parameters = new[]
                {
                    new SqlParameter("@LogLevel", "Warning"),
                    new SqlParameter("@Message", message),
                    new SqlParameter("@Source", source),
                    new SqlParameter("@UserId", (object?)userId ?? DBNull.Value),
                    new SqlParameter("@UserName", (object?)userName ?? DBNull.Value),
                    new SqlParameter("@MachineName", Environment.MachineName),
                    new SqlParameter("@LogDate", DateTime.Now)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await LogToFileAsync(source, ex, userName);
            }
        }
        
        public async Task LogInfoAsync(string source, string message, string? userName = null, int? userId = null)
        {
            try
            {
                var query = @"
                    INSERT INTO ErrorLogs (LogLevel, Message, Source, UserId, UserName, MachineName, LogDate)
                    VALUES (@LogLevel, @Message, @Source, @UserId, @UserName, @MachineName, @LogDate)";
                
                var parameters = new[]
                {
                    new SqlParameter("@LogLevel", "Info"),
                    new SqlParameter("@Message", message),
                    new SqlParameter("@Source", source),
                    new SqlParameter("@UserId", (object?)userId ?? DBNull.Value),
                    new SqlParameter("@UserName", (object?)userName ?? DBNull.Value),
                    new SqlParameter("@MachineName", Environment.MachineName),
                    new SqlParameter("@LogDate", DateTime.Now)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await LogToFileAsync(source, ex, userName);
            }
        }
        
        private async Task LogToFileAsync(string source, Exception exception, string? userName)
        {
            try
            {
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                Directory.CreateDirectory(logDirectory);
                
                var logFile = Path.Combine(logDirectory, $"error_log_{DateTime.Now:yyyyMMdd}.txt");
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{source}] [{userName ?? "Unknown"}] {exception.Message}{Environment.NewLine}{exception.StackTrace}{Environment.NewLine}{Environment.NewLine}";
                
                await File.AppendAllTextAsync(logFile, logEntry);
            }
            catch
            {
                // لا يمكن فعل شيء إذا فشل حتى تسجيل الملف
            }
        }
    }
}
