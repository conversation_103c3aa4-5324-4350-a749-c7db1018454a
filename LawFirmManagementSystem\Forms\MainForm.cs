using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using LawFirmManagementSystem.Services.Interfaces;

namespace LawFirmManagementSystem.Forms
{
    /// <summary>
    /// الشاشة الرئيسية للتطبيق
    /// Main application form
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserService _userService;
        private readonly ILocalizationService _localizationService;
        
        private MenuStrip mainMenuStrip;
        private ToolStrip mainToolStrip;
        private StatusStrip statusStrip;
        private Panel leftPanel;
        private Panel rightPanel;
        private Splitter splitter;
        private TabControl mainTabControl;
        
        // Menu items
        private ToolStripMenuItem mnuFile;
        private ToolStripMenuItem mnuClients;
        private ToolStripMenuItem mnuCases;
        private ToolStripMenuItem mnuCalendar;
        private ToolStripMenuItem mnuFinancial;
        private ToolStripMenuItem mnuDocuments;
        private ToolStripMenuItem mnuReports;
        private ToolStripMenuItem mnuTools;
        private ToolStripMenuItem mnuHelp;
        
        // Toolbar buttons
        private ToolStripButton btnNewClient;
        private ToolStripButton btnNewCase;
        private ToolStripButton btnCalendar;
        private ToolStripButton btnReports;
        
        // Status bar labels
        private ToolStripStatusLabel lblUser;
        private ToolStripStatusLabel lblDateTime;
        private ToolStripStatusLabel lblStatus;
        
        // Dashboard controls
        private Panel dashboardPanel;
        private TreeView navigationTree;

        public MainForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();
            
            InitializeComponent();
            InitializeForm();
            LoadDashboard();
            ApplyLocalization();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "نظام إدارة مكتب المحاماة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Font = new Font("Tahoma", 9F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Initialize menu strip
            InitializeMenuStrip();
            
            // Initialize toolbar
            InitializeToolStrip();
            
            // Initialize status strip
            InitializeStatusStrip();
            
            // Initialize main layout
            InitializeMainLayout();
            
            // Add controls to form
            this.Controls.Add(mainTabControl);
            this.Controls.Add(splitter);
            this.Controls.Add(leftPanel);
            this.Controls.Add(mainToolStrip);
            this.Controls.Add(statusStrip);
            this.Controls.Add(mainMenuStrip);
            
            this.MainMenuStrip = mainMenuStrip;
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeMenuStrip()
        {
            mainMenuStrip = new MenuStrip
            {
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes
            };
            
            // File menu
            mnuFile = new ToolStripMenuItem("ملف");
            mnuFile.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("جديد", null, null, Keys.Control | Keys.N),
                new ToolStripSeparator(),
                new ToolStripMenuItem("إعدادات النظام"),
                new ToolStripMenuItem("إعدادات المستخدم"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تسجيل الخروج", null, Logout_Click),
                new ToolStripMenuItem("خروج", null, Exit_Click, Keys.Alt | Keys.F4)
            });
            
            // Clients menu
            mnuClients = new ToolStripMenuItem("العملاء");
            mnuClients.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("عميل جديد", null, NewClient_Click, Keys.Control | Keys.Shift | Keys.C),
                new ToolStripMenuItem("قائمة العملاء", null, ClientsList_Click),
                new ToolStripMenuItem("البحث عن عميل", null, SearchClient_Click, Keys.Control | Keys.F),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تقارير العملاء")
            });
            
            // Cases menu
            mnuCases = new ToolStripMenuItem("القضايا");
            mnuCases.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("قضية جديدة", null, NewCase_Click, Keys.Control | Keys.Shift | Keys.L),
                new ToolStripMenuItem("قائمة القضايا", null, CasesList_Click),
                new ToolStripMenuItem("القضايا النشطة"),
                new ToolStripMenuItem("القضايا المغلقة"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("أنواع القضايا"),
                new ToolStripMenuItem("المحاكم")
            });
            
            // Calendar menu
            mnuCalendar = new ToolStripMenuItem("التقويم");
            mnuCalendar.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("عرض التقويم", null, Calendar_Click),
                new ToolStripMenuItem("موعد جديد", null, NewAppointment_Click),
                new ToolStripMenuItem("جلسة جديدة", null, NewHearing_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("المواعيد القادمة"),
                new ToolStripMenuItem("الجلسات القادمة")
            });
            
            // Financial menu
            mnuFinancial = new ToolStripMenuItem("المالية");
            mnuFinancial.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("فاتورة جديدة", null, NewInvoice_Click),
                new ToolStripMenuItem("دفعة جديدة", null, NewPayment_Click),
                new ToolStripMenuItem("قائمة الفواتير"),
                new ToolStripMenuItem("قائمة الدفعات"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("التقارير المالية")
            });
            
            // Documents menu
            mnuDocuments = new ToolStripMenuItem("الوثائق");
            mnuDocuments.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("رفع وثيقة", null, UploadDocument_Click),
                new ToolStripMenuItem("مكتبة الوثائق", null, DocumentLibrary_Click),
                new ToolStripMenuItem("البحث في الوثائق"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("قوالب الوثائق")
            });
            
            // Reports menu
            mnuReports = new ToolStripMenuItem("التقارير");
            mnuReports.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("تقارير العملاء"),
                new ToolStripMenuItem("تقارير القضايا"),
                new ToolStripMenuItem("التقارير المالية"),
                new ToolStripMenuItem("تقارير الأداء"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("منشئ التقارير")
            });
            
            // Tools menu
            mnuTools = new ToolStripMenuItem("أدوات");
            mnuTools.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("النسخ الاحتياطي"),
                new ToolStripMenuItem("استعادة البيانات"),
                new ToolStripMenuItem("تنظيف قاعدة البيانات"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("إدارة المستخدمين"),
                new ToolStripMenuItem("سجل التدقيق")
            });
            
            // Help menu
            mnuHelp = new ToolStripMenuItem("مساعدة");
            mnuHelp.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("دليل المستخدم"),
                new ToolStripMenuItem("الأسئلة الشائعة"),
                new ToolStripMenuItem("الدعم الفني"),
                new ToolStripSeparator(),
                new ToolStripMenuItem("حول البرنامج", null, About_Click)
            });
            
            mainMenuStrip.Items.AddRange(new ToolStripItem[] {
                mnuFile, mnuClients, mnuCases, mnuCalendar,
                mnuFinancial, mnuDocuments, mnuReports, mnuTools, mnuHelp
            });
        }

        private void InitializeToolStrip()
        {
            mainToolStrip = new ToolStrip
            {
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes,
                ImageScalingSize = new Size(24, 24)
            };
            
            btnNewClient = new ToolStripButton
            {
                Text = "عميل جديد",
                ToolTipText = "إضافة عميل جديد",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnNewClient.Click += NewClient_Click;
            
            btnNewCase = new ToolStripButton
            {
                Text = "قضية جديدة",
                ToolTipText = "إضافة قضية جديدة",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnNewCase.Click += NewCase_Click;
            
            btnCalendar = new ToolStripButton
            {
                Text = "التقويم",
                ToolTipText = "عرض التقويم",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnCalendar.Click += Calendar_Click;
            
            btnReports = new ToolStripButton
            {
                Text = "التقارير",
                ToolTipText = "عرض التقارير",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnReports.Click += Reports_Click;
            
            mainToolStrip.Items.AddRange(new ToolStripItem[] {
                btnNewClient,
                new ToolStripSeparator(),
                btnNewCase,
                new ToolStripSeparator(),
                btnCalendar,
                new ToolStripSeparator(),
                btnReports
            });
        }

        private void InitializeStatusStrip()
        {
            statusStrip = new StatusStrip
            {
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes
            };
            
            lblUser = new ToolStripStatusLabel
            {
                Text = $"المستخدم: {SessionManager.CurrentUser?.FullName ?? "غير محدد"}",
                Spring = false
            };
            
            lblDateTime = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                Spring = false
            };
            
            lblStatus = new ToolStripStatusLabel
            {
                Text = "جاهز",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            statusStrip.Items.AddRange(new ToolStripItem[] {
                lblStatus,
                new ToolStripStatusLabel("|"),
                lblDateTime,
                new ToolStripStatusLabel("|"),
                lblUser
            });
            
            // Update time every minute
            var timer = new Timer { Interval = 60000 };
            timer.Tick += (s, e) => lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();
        }

        private void InitializeMainLayout()
        {
            // Left panel for navigation
            leftPanel = new Panel
            {
                Width = 250,
                Dock = DockStyle.Right,
                BackColor = Color.FromArgb(248, 249, 250)
            };
            
            // Navigation tree
            navigationTree = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true
            };
            
            LoadNavigationTree();
            leftPanel.Controls.Add(navigationTree);
            
            // Splitter
            splitter = new Splitter
            {
                Dock = DockStyle.Right,
                Width = 3,
                BackColor = Color.FromArgb(200, 200, 200)
            };
            
            // Main tab control
            mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 9F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };
            
            // Add dashboard tab
            var dashboardTab = new TabPage("لوحة التحكم");
            dashboardPanel = new Panel { Dock = DockStyle.Fill };
            dashboardTab.Controls.Add(dashboardPanel);
            mainTabControl.TabPages.Add(dashboardTab);
        }

        private void LoadNavigationTree()
        {
            navigationTree.Nodes.Clear();
            
            var clientsNode = new TreeNode("العملاء");
            clientsNode.Nodes.AddRange(new TreeNode[] {
                new TreeNode("عميل جديد"),
                new TreeNode("قائمة العملاء"),
                new TreeNode("البحث عن عميل")
            });
            
            var casesNode = new TreeNode("القضايا");
            casesNode.Nodes.AddRange(new TreeNode[] {
                new TreeNode("قضية جديدة"),
                new TreeNode("قائمة القضايا"),
                new TreeNode("القضايا النشطة"),
                new TreeNode("القضايا المغلقة")
            });
            
            var calendarNode = new TreeNode("التقويم");
            calendarNode.Nodes.AddRange(new TreeNode[] {
                new TreeNode("عرض التقويم"),
                new TreeNode("المواعيد القادمة"),
                new TreeNode("الجلسات القادمة")
            });
            
            var financialNode = new TreeNode("المالية");
            financialNode.Nodes.AddRange(new TreeNode[] {
                new TreeNode("الفواتير"),
                new TreeNode("الدفعات"),
                new TreeNode("التقارير المالية")
            });
            
            var documentsNode = new TreeNode("الوثائق");
            documentsNode.Nodes.AddRange(new TreeNode[] {
                new TreeNode("مكتبة الوثائق"),
                new TreeNode("رفع وثيقة"),
                new TreeNode("البحث في الوثائق")
            });
            
            var reportsNode = new TreeNode("التقارير");
            reportsNode.Nodes.AddRange(new TreeNode[] {
                new TreeNode("تقارير العملاء"),
                new TreeNode("تقارير القضايا"),
                new TreeNode("التقارير المالية")
            });
            
            navigationTree.Nodes.AddRange(new TreeNode[] {
                clientsNode, casesNode, calendarNode,
                financialNode, documentsNode, reportsNode
            });
            
            navigationTree.ExpandAll();
        }

        private void InitializeForm()
        {
            // Set form icon if available
            try
            {
                var iconPath = Path.Combine(Application.StartupPath, "Resources", "Icons", "app-icon.ico");
                if (File.Exists(iconPath))
                {
                    this.Icon = new Icon(iconPath);
                }
            }
            catch { }
        }

        private void LoadDashboard()
        {
            // Create dashboard layout
            var dashboardLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(10)
            };
            
            // Set column and row styles
            dashboardLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            dashboardLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            
            dashboardLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            dashboardLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            dashboardLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            
            // Add dashboard widgets
            AddDashboardWidget(dashboardLayout, "العملاء", "120", "إجمالي العملاء", 0, 0);
            AddDashboardWidget(dashboardLayout, "القضايا النشطة", "45", "قضية جارية", 1, 0);
            AddDashboardWidget(dashboardLayout, "الجلسات القادمة", "8", "جلسة هذا الأسبوع", 2, 0);
            AddDashboardWidget(dashboardLayout, "الفواتير المعلقة", "15", "فاتورة غير مدفوعة", 0, 1);
            AddDashboardWidget(dashboardLayout, "المواعيد اليوم", "3", "موعد اليوم", 1, 1);
            AddDashboardWidget(dashboardLayout, "الوثائق الجديدة", "12", "وثيقة هذا الشهر", 2, 1);
            
            dashboardPanel.Controls.Add(dashboardLayout);
        }

        private void AddDashboardWidget(TableLayoutPanel layout, string title, string value, string subtitle, int col, int row)
        {
            var widget = new Panel
            {
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(5)
            };
            
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.TopCenter,
                Dock = DockStyle.Top,
                Height = 30
            };
            
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Tahoma", 24F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 123, 255),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            
            var subtitleLabel = new Label
            {
                Text = subtitle,
                Font = new Font("Tahoma", 8F),
                ForeColor = Color.FromArgb(102, 102, 102),
                TextAlign = ContentAlignment.BottomCenter,
                Dock = DockStyle.Bottom,
                Height = 20
            };
            
            widget.Controls.AddRange(new Control[] { titleLabel, valueLabel, subtitleLabel });
            layout.Controls.Add(widget, col, row);
        }

        private void ApplyLocalization()
        {
            // Apply current language settings
            if (SessionManager.CurrentLanguage == "fr")
            {
                // Switch to French
                this.RightToLeft = RightToLeft.No;
                this.RightToLeftLayout = false;
                // Update menu and control texts to French
                // This would be implemented with a proper localization service
            }
        }

        // Event handlers
        private void NewClient_Click(object sender, EventArgs e)
        {
            // Open new client form
            ShowMessage("فتح نموذج عميل جديد");
        }

        private void NewCase_Click(object sender, EventArgs e)
        {
            // Open new case form
            ShowMessage("فتح نموذج قضية جديدة");
        }

        private void Calendar_Click(object sender, EventArgs e)
        {
            // Open calendar view
            ShowMessage("فتح التقويم");
        }

        private void Reports_Click(object sender, EventArgs e)
        {
            // Open reports
            ShowMessage("فتح التقارير");
        }

        private void ClientsList_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح قائمة العملاء");
        }

        private void SearchClient_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح البحث عن عميل");
        }

        private void CasesList_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح قائمة القضايا");
        }

        private void NewAppointment_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح نموذج موعد جديد");
        }

        private void NewHearing_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح نموذج جلسة جديدة");
        }

        private void NewInvoice_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح نموذج فاتورة جديدة");
        }

        private void NewPayment_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح نموذج دفعة جديدة");
        }

        private void UploadDocument_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح رفع وثيقة");
        }

        private void DocumentLibrary_Click(object sender, EventArgs e)
        {
            ShowMessage("فتح مكتبة الوثائق");
        }

        private void About_Click(object sender, EventArgs e)
        {
            var aboutForm = new AboutForm();
            aboutForm.ShowDialog(this);
        }

        private void Logout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد تسجيل الخروج من النظام؟",
                "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // End current session
                if (SessionManager.CurrentSession != null)
                {
                    _userService.EndSessionAsync(SessionManager.CurrentSession.SessionToken);
                }

                // Clear session data
                SessionManager.CurrentUser = null;
                SessionManager.CurrentSession = null;

                // Show login form
                this.Hide();
                var loginForm = new LoginForm(_serviceProvider);
                loginForm.FormClosed += (s, e) => Application.Exit();
                loginForm.Show();
            }
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void ShowMessage(string message)
        {
            lblStatus.Text = message;
            MessageBox.Show(message, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق التطبيق؟",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                // End current session
                if (SessionManager.CurrentSession != null)
                {
                    _userService.EndSessionAsync(SessionManager.CurrentSession.SessionToken);
                }
            }

            base.OnFormClosing(e);
        }
    }
}
