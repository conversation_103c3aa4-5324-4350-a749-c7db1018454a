version: '3.8'

services:
  # SQL Server for development
  sqlserver-dev:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: lawfirm-sqlserver-dev
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=DevPassword123!
      - MSSQL_PID=Express
    ports:
      - "1434:1433"  # Different port to avoid conflicts
    volumes:
      - sqlserver_dev_data:/var/opt/mssql
      - ./Database/Scripts:/docker-entrypoint-initdb.d
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    container_name: lawfirm-redis-dev
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_dev_data:/data
    networks:
      - lawfirm-dev-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: lawfirm-mailhog
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # Elasticsearch for development
  elasticsearch-dev:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: lawfirm-elasticsearch-dev
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms256m -Xmx256m"  # Lower memory for dev
    ports:
      - "9201:9200"  # Different port to avoid conflicts
    volumes:
      - elasticsearch_dev_data:/usr/share/elasticsearch/data
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # Kibana for development
  kibana-dev:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: lawfirm-kibana-dev
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch-dev:9200
    ports:
      - "5602:5601"  # Different port to avoid conflicts
    networks:
      - lawfirm-dev-network
    depends_on:
      - elasticsearch-dev
    restart: unless-stopped

  # Seq for structured logging (development)
  seq:
    image: datalust/seq:latest
    container_name: lawfirm-seq
    environment:
      - ACCEPT_EULA=Y
    ports:
      - "5341:80"  # Seq web UI
      - "5342:5341"  # Seq ingestion
    volumes:
      - seq_data:/data
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # Jaeger for distributed tracing (development)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: lawfirm-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # Prometheus for metrics (development)
  prometheus:
    image: prom/prometheus:latest
    container_name: lawfirm-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./Monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - lawfirm-dev-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for monitoring dashboards (development)
  grafana:
    image: grafana/grafana:latest
    container_name: lawfirm-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./Monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./Monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # MinIO for S3-compatible object storage (development)
  minio:
    image: minio/minio:latest
    container_name: lawfirm-minio
    ports:
      - "9000:9000"  # MinIO API
      - "9001:9001"  # MinIO Console
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - lawfirm-dev-network
    restart: unless-stopped
    command: server /data --console-address ":9001"

  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: lawfirm-adminer
    ports:
      - "8080:8080"
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

  # Portainer for container management
  portainer:
    image: portainer/portainer-ce:latest
    container_name: lawfirm-portainer
    ports:
      - "9443:9443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - lawfirm-dev-network
    restart: unless-stopped

networks:
  lawfirm-dev-network:
    driver: bridge
    name: lawfirm-dev-network

volumes:
  sqlserver_dev_data:
    name: lawfirm-sqlserver-dev-data
  redis_dev_data:
    name: lawfirm-redis-dev-data
  elasticsearch_dev_data:
    name: lawfirm-elasticsearch-dev-data
  seq_data:
    name: lawfirm-seq-data
  prometheus_data:
    name: lawfirm-prometheus-data
  grafana_data:
    name: lawfirm-grafana-data
  minio_data:
    name: lawfirm-minio-data
  portainer_data:
    name: lawfirm-portainer-data
