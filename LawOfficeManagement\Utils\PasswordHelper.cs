using System;
using System.Security.Cryptography;
using System.Text;

namespace LawOfficeManagement.Utils
{
    public static class PasswordHelper
    {
        public static string GenerateSalt()
        {
            using var rng = RandomNumberGenerator.Create();
            var saltBytes = new byte[32];
            rng.GetBytes(saltBytes);
            return Convert.ToBase64String(saltBytes);
        }
        
        public static string HashPassword(string password, string salt)
        {
            using var sha256 = SHA256.Create();
            var saltedPassword = password + salt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashedBytes);
        }
        
        public static bool VerifyPassword(string password, string hash, string salt)
        {
            var computedHash = HashPassword(password, salt);
            return computedHash == hash;
        }
        
        public static (string hash, string salt) CreatePasswordHash(string password)
        {
            var salt = GenerateSalt();
            var hash = HashPassword(password, salt);
            return (hash, salt);
        }
        
        public static int CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return 0;
            
            int score = 0;
            
            // طول كلمة المرور
            if (password.Length >= 8) score += 25;
            if (password.Length >= 12) score += 25;
            
            // أحرف كبيرة
            if (password.Any(char.IsUpper)) score += 15;
            
            // أحرف صغيرة
            if (password.Any(char.IsLower)) score += 15;
            
            // أرقام
            if (password.Any(char.IsDigit)) score += 10;
            
            // رموز خاصة
            if (password.Any(c => !char.IsLetterOrDigit(c))) score += 10;
            
            return Math.Min(score, 100);
        }
        
        public static string GetPasswordStrengthText(int strength)
        {
            return strength switch
            {
                < 30 => "ضعيفة",
                < 60 => "متوسطة",
                < 80 => "قوية",
                _ => "قوية جداً"
            };
        }
        
        public static Color GetPasswordStrengthColor(int strength)
        {
            return strength switch
            {
                < 30 => Color.Red,
                < 60 => Color.Orange,
                < 80 => Color.YellowGreen,
                _ => Color.Green
            };
        }
    }
}
