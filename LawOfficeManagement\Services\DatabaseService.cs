using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Configuration;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Services
{
    public class DatabaseService
    {
        private readonly string _connectionString;
        private static DatabaseService? _instance;
        
        public static DatabaseService Instance => _instance ??= new DatabaseService();
        
        private DatabaseService()
        {
            _connectionString = GetConnectionString();
        }
        
        private string GetConnectionString()
        {
            // يمكن تخصيص سلسلة الاتصال حسب الحاجة
            return @"Server=.\SQLEXPRESS;Database=LawOfficeDB;Integrated Security=true;TrustServerCertificate=true;";
        }
        
        public SqlConnection GetConnection()
        {
            return new SqlConnection(_connectionString);
        }
        
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = GetConnection();
                await connection.OpenAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                using var connection = GetConnection();
                await connection.OpenAsync();
                
                // التحقق من وجود قاعدة البيانات
                var checkDbCommand = new SqlCommand(
                    "SELECT COUNT(*) FROM sys.databases WHERE name = 'LawOfficeDB'", 
                    connection);
                
                var dbExists = (int)await checkDbCommand.ExecuteScalarAsync() > 0;
                
                if (!dbExists)
                {
                    // إنشاء قاعدة البيانات إذا لم تكن موجودة
                    await CreateDatabaseAsync();
                }
                
                return true;
            }
            catch (Exception ex)
            {
                LogError("Database initialization failed", ex);
                return false;
            }
        }
        
        private async Task CreateDatabaseAsync()
        {
            try
            {
                // قراءة سكريبت إنشاء قاعدة البيانات
                var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "DatabaseSchema.sql");
                
                if (File.Exists(scriptPath))
                {
                    var script = await File.ReadAllTextAsync(scriptPath);
                    await ExecuteScriptAsync(script);
                }
            }
            catch (Exception ex)
            {
                LogError("Database creation failed", ex);
                throw;
            }
        }
        
        private async Task ExecuteScriptAsync(string script)
        {
            using var connection = GetConnection();
            await connection.OpenAsync();
            
            // تقسيم السكريبت إلى أوامر منفصلة
            var commands = script.Split(new[] { "GO" }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var commandText in commands)
            {
                if (string.IsNullOrWhiteSpace(commandText)) continue;
                
                using var command = new SqlCommand(commandText.Trim(), connection);
                command.CommandTimeout = 300; // 5 دقائق
                await command.ExecuteNonQueryAsync();
            }
        }
        
        public async Task<DataTable> ExecuteQueryAsync(string query, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                await connection.OpenAsync();
                
                using var command = new SqlCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);
                
                return dataTable;
            }
            catch (Exception ex)
            {
                LogError($"Query execution failed: {query}", ex);
                throw;
            }
        }
        
        public async Task<object?> ExecuteScalarAsync(string query, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                await connection.OpenAsync();
                
                using var command = new SqlCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                return await command.ExecuteScalarAsync();
            }
            catch (Exception ex)
            {
                LogError($"Scalar execution failed: {query}", ex);
                throw;
            }
        }
        
        public async Task<int> ExecuteNonQueryAsync(string query, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                await connection.OpenAsync();
                
                using var command = new SqlCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                LogError($"Non-query execution failed: {query}", ex);
                throw;
            }
        }
        
        public async Task<T?> ExecuteTransactionAsync<T>(Func<SqlConnection, SqlTransaction, Task<T>> operation)
        {
            using var connection = GetConnection();
            await connection.OpenAsync();
            
            using var transaction = connection.BeginTransaction();
            
            try
            {
                var result = await operation(connection, transaction);
                await transaction.CommitAsync();
                return result;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        
        public async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var query = $@"
                    BACKUP DATABASE [LawOfficeDB] 
                    TO DISK = @BackupPath 
                    WITH FORMAT, INIT, 
                    NAME = 'LawOfficeDB-Full Database Backup', 
                    SKIP, NOREWIND, NOUNLOAD, STATS = 10";
                
                var parameters = new[]
                {
                    new SqlParameter("@BackupPath", backupPath)
                };
                
                await ExecuteNonQueryAsync(query, parameters);
                
                // تسجيل النسخة الاحتياطية في قاعدة البيانات
                await LogBackupAsync(backupPath, "Full", true);
                
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Database backup failed: {backupPath}", ex);
                await LogBackupAsync(backupPath, "Full", false, ex.Message);
                return false;
            }
        }
        
        public async Task<bool> RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                // إغلاق جميع الاتصالات النشطة
                var killConnectionsQuery = @"
                    ALTER DATABASE [LawOfficeDB] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                    RESTORE DATABASE [LawOfficeDB] FROM DISK = @BackupPath WITH REPLACE;
                    ALTER DATABASE [LawOfficeDB] SET MULTI_USER;";
                
                var parameters = new[]
                {
                    new SqlParameter("@BackupPath", backupPath)
                };
                
                await ExecuteNonQueryAsync(killConnectionsQuery, parameters);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Database restore failed: {backupPath}", ex);
                return false;
            }
        }
        
        private async Task LogBackupAsync(string backupPath, string backupType, bool isSuccessful, string? errorMessage = null)
        {
            try
            {
                var query = @"
                    INSERT INTO BackupHistory (BackupName, BackupPath, BackupType, IsAutomatic, Status, ErrorMessage, BackupSize)
                    VALUES (@BackupName, @BackupPath, @BackupType, 0, @Status, @ErrorMessage, @BackupSize)";
                
                var backupName = $"Backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                var status = isSuccessful ? "مكتملة" : "فاشلة";
                long? backupSize = null;
                
                if (isSuccessful && File.Exists(backupPath))
                {
                    backupSize = new FileInfo(backupPath).Length;
                }
                
                var parameters = new[]
                {
                    new SqlParameter("@BackupName", backupName),
                    new SqlParameter("@BackupPath", backupPath),
                    new SqlParameter("@BackupType", backupType),
                    new SqlParameter("@Status", status),
                    new SqlParameter("@ErrorMessage", (object?)errorMessage ?? DBNull.Value),
                    new SqlParameter("@BackupSize", (object?)backupSize ?? DBNull.Value)
                };
                
                await ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                LogError("Failed to log backup history", ex);
            }
        }
        
        private void LogError(string message, Exception ex)
        {
            try
            {
                // يمكن تحسين هذا لاحقاً لحفظ الأخطاء في قاعدة البيانات
                var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}: {ex.Message}";
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "database_errors.log");
                
                Directory.CreateDirectory(Path.GetDirectoryName(logPath)!);
                File.AppendAllText(logPath, logMessage + Environment.NewLine);
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }
        }
    }
}
