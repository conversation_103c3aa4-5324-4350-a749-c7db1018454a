-- قاعدة بيانات نظام إدارة مكتب المحاماة
-- Law Office Management System Database Schema

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'LawOfficeDB')
BEGIN
    CREATE DATABASE LawOfficeDB
    COLLATE Arabic_CI_AS;
END
GO

USE LawOfficeDB;
GO

-- جدول المستخدمين
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    Salt NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100),
    Phone NVARCHAR(20),
    Role NVARCHAR(20) NOT NULL CHECK (Role IN ('Admin', 'Lawyer', 'Secretary', 'Assistant')),
    IsActive BIT DEFAULT 1,
    LastLogin DATETIME2,
    FailedLoginAttempts INT DEFAULT 0,
    IsLocked BIT DEFAULT 0,
    LockoutEnd DATETIME2,
    TwoFactorEnabled BIT DEFAULT 0,
    TwoFactorSecret NVARCHAR(255),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT
);

-- جدول إعدادات المكتب
CREATE TABLE OfficeSettings (
    SettingId INT IDENTITY(1,1) PRIMARY KEY,
    OfficeName NVARCHAR(200) NOT NULL,
    ICE NVARCHAR(50),
    CommercialRegister NVARCHAR(50),
    Address NVARCHAR(500),
    City NVARCHAR(100),
    Country NVARCHAR(100) DEFAULT N'المغرب',
    Phone NVARCHAR(20),
    Fax NVARCHAR(20),
    Email NVARCHAR(100),
    Website NVARCHAR(200),
    Logo VARBINARY(MAX),
    DefaultLanguage NVARCHAR(10) DEFAULT 'ar',
    Theme NVARCHAR(20) DEFAULT 'Light',
    BackupPath NVARCHAR(500),
    AutoBackupEnabled BIT DEFAULT 1,
    BackupFrequency INT DEFAULT 24, -- ساعات
    EmailSettings NVARCHAR(MAX), -- JSON
    WhatsAppSettings NVARCHAR(MAX), -- JSON
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    ModifiedDate DATETIME2 DEFAULT GETDATE()
);

-- جدول أنواع القضايا
CREATE TABLE CaseTypes (
    CaseTypeId INT IDENTITY(1,1) PRIMARY KEY,
    TypeName NVARCHAR(100) NOT NULL,
    TypeNameFr NVARCHAR(100),
    Description NVARCHAR(500),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE()
);

-- جدول المحاكم
CREATE TABLE Courts (
    CourtId INT IDENTITY(1,1) PRIMARY KEY,
    CourtName NVARCHAR(200) NOT NULL,
    CourtNameFr NVARCHAR(200),
    CourtType NVARCHAR(50), -- ابتدائية، استئناف، نقض
    Jurisdiction NVARCHAR(100), -- الاختصاص
    City NVARCHAR(100),
    Address NVARCHAR(500),
    Phone NVARCHAR(20),
    Fax NVARCHAR(20),
    Email NVARCHAR(100),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE()
);

-- جدول العملاء
CREATE TABLE Clients (
    ClientId INT IDENTITY(1,1) PRIMARY KEY,
    ClientType NVARCHAR(20) NOT NULL CHECK (ClientType IN ('Individual', 'Company', 'Government')),
    FullName NVARCHAR(200) NOT NULL,
    FullNameFr NVARCHAR(200),
    CIN NVARCHAR(20),
    PassportNumber NVARCHAR(20),
    CompanyRegister NVARCHAR(50),
    ICE NVARCHAR(50),
    DateOfBirth DATE,
    Gender NVARCHAR(10),
    Nationality NVARCHAR(50) DEFAULT N'مغربية',
    Address NVARCHAR(500),
    City NVARCHAR(100),
    Phone NVARCHAR(20),
    Mobile NVARCHAR(20),
    Email NVARCHAR(100),
    Fax NVARCHAR(20),
    Profession NVARCHAR(100),
    Notes NVARCHAR(MAX),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- جدول الملفات القانونية
CREATE TABLE LegalCases (
    CaseId INT IDENTITY(1,1) PRIMARY KEY,
    CaseReference NVARCHAR(50) UNIQUE NOT NULL, -- مرجع الملف
    OfficeReference NVARCHAR(50) UNIQUE NOT NULL, -- مرجع المكتب
    CaseTitle NVARCHAR(500) NOT NULL,
    CaseTitleFr NVARCHAR(500),
    CaseTypeId INT NOT NULL,
    ClientId INT NOT NULL,
    CourtId INT,
    CourtCaseNumber NVARCHAR(100),
    CaseStatus NVARCHAR(50) DEFAULT N'جاري',
    Priority NVARCHAR(20) DEFAULT N'عادية',
    OpenDate DATE NOT NULL,
    CloseDate DATE,
    Description NVARCHAR(MAX),
    InternalNotes NVARCHAR(MAX),
    AssignedLawyer INT,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    FOREIGN KEY (CaseTypeId) REFERENCES CaseTypes(CaseTypeId),
    FOREIGN KEY (ClientId) REFERENCES Clients(ClientId),
    FOREIGN KEY (CourtId) REFERENCES Courts(CourtId),
    FOREIGN KEY (AssignedLawyer) REFERENCES Users(UserId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- جدول الخصوم
CREATE TABLE Opponents (
    OpponentId INT IDENTITY(1,1) PRIMARY KEY,
    CaseId INT NOT NULL,
    OpponentType NVARCHAR(20) NOT NULL CHECK (OpponentType IN ('Individual', 'Company', 'Government')),
    FullName NVARCHAR(200) NOT NULL,
    FullNameFr NVARCHAR(200),
    CIN NVARCHAR(20),
    CompanyRegister NVARCHAR(50),
    Address NVARCHAR(500),
    Phone NVARCHAR(20),
    Email NVARCHAR(100),
    LawyerName NVARCHAR(200),
    LawyerPhone NVARCHAR(20),
    LawyerEmail NVARCHAR(100),
    Notes NVARCHAR(MAX),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    FOREIGN KEY (CaseId) REFERENCES LegalCases(CaseId) ON DELETE CASCADE,
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
);

-- جدول الجلسات
CREATE TABLE Hearings (
    HearingId INT IDENTITY(1,1) PRIMARY KEY,
    CaseId INT NOT NULL,
    HearingDate DATETIME2 NOT NULL,
    HearingType NVARCHAR(100),
    CourtRoom NVARCHAR(50),
    Judge NVARCHAR(200),
    Status NVARCHAR(50) DEFAULT N'مجدولة',
    Outcome NVARCHAR(MAX),
    NextHearingDate DATETIME2,
    Notes NVARCHAR(MAX),
    ReminderSent BIT DEFAULT 0,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    FOREIGN KEY (CaseId) REFERENCES LegalCases(CaseId) ON DELETE CASCADE,
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- جدول المواعيد
CREATE TABLE Appointments (
    AppointmentId INT IDENTITY(1,1) PRIMARY KEY,
    CaseId INT,
    ClientId INT,
    Title NVARCHAR(200) NOT NULL,
    Description NVARCHAR(MAX),
    AppointmentDate DATETIME2 NOT NULL,
    Duration INT DEFAULT 60, -- بالدقائق
    Location NVARCHAR(200),
    Status NVARCHAR(50) DEFAULT N'مجدول',
    ReminderSent BIT DEFAULT 0,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    FOREIGN KEY (CaseId) REFERENCES LegalCases(CaseId),
    FOREIGN KEY (ClientId) REFERENCES Clients(ClientId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- جدول أنواع المصاريف
CREATE TABLE ExpenseTypes (
    ExpenseTypeId INT IDENTITY(1,1) PRIMARY KEY,
    TypeName NVARCHAR(100) NOT NULL,
    TypeNameFr NVARCHAR(100),
    Description NVARCHAR(500),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE()
);

-- جدول المصاريف والأتعاب
CREATE TABLE CaseExpenses (
    ExpenseId INT IDENTITY(1,1) PRIMARY KEY,
    CaseId INT NOT NULL,
    ExpenseTypeId INT NOT NULL,
    Description NVARCHAR(500),
    Amount DECIMAL(18,2) NOT NULL,
    ExpenseDate DATE NOT NULL,
    IsPaid BIT DEFAULT 0,
    PaymentDate DATE,
    PaymentMethod NVARCHAR(50),
    Receipt VARBINARY(MAX),
    Notes NVARCHAR(MAX),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    FOREIGN KEY (CaseId) REFERENCES LegalCases(CaseId) ON DELETE CASCADE,
    FOREIGN KEY (ExpenseTypeId) REFERENCES ExpenseTypes(ExpenseTypeId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- جدول الفواتير
CREATE TABLE Invoices (
    InvoiceId INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceNumber NVARCHAR(50) UNIQUE NOT NULL,
    CaseId INT NOT NULL,
    ClientId INT NOT NULL,
    InvoiceDate DATE NOT NULL,
    DueDate DATE,
    TotalAmount DECIMAL(18,2) NOT NULL,
    PaidAmount DECIMAL(18,2) DEFAULT 0,
    Status NVARCHAR(50) DEFAULT N'غير مدفوعة',
    Notes NVARCHAR(MAX),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    FOREIGN KEY (CaseId) REFERENCES LegalCases(CaseId),
    FOREIGN KEY (ClientId) REFERENCES Clients(ClientId),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- جدول تفاصيل الفواتير
CREATE TABLE InvoiceDetails (
    InvoiceDetailId INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceId INT NOT NULL,
    Description NVARCHAR(500) NOT NULL,
    Quantity DECIMAL(10,2) DEFAULT 1,
    UnitPrice DECIMAL(18,2) NOT NULL,
    TotalPrice DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (InvoiceId) REFERENCES Invoices(InvoiceId) ON DELETE CASCADE
);

-- جدول المستندات
CREATE TABLE Documents (
    DocumentId INT IDENTITY(1,1) PRIMARY KEY,
    CaseId INT NOT NULL,
    DocumentName NVARCHAR(500) NOT NULL,
    DocumentType NVARCHAR(100),
    FilePath NVARCHAR(1000),
    FileSize BIGINT,
    MimeType NVARCHAR(100),
    UploadDate DATETIME2 DEFAULT GETDATE(),
    UploadedBy INT,
    Description NVARCHAR(MAX),
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (CaseId) REFERENCES LegalCases(CaseId) ON DELETE CASCADE,
    FOREIGN KEY (UploadedBy) REFERENCES Users(UserId)
);

-- جدول سجل الأخطاء
CREATE TABLE ErrorLogs (
    LogId INT IDENTITY(1,1) PRIMARY KEY,
    LogLevel NVARCHAR(20) NOT NULL, -- Error, Warning, Info
    Message NVARCHAR(MAX) NOT NULL,
    Exception NVARCHAR(MAX),
    StackTrace NVARCHAR(MAX),
    Source NVARCHAR(200),
    UserId INT,
    UserName NVARCHAR(100),
    MachineName NVARCHAR(100),
    LogDate DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (UserId) REFERENCES Users(UserId)
);

-- جدول الإشعارات
CREATE TABLE Notifications (
    NotificationId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    Title NVARCHAR(200) NOT NULL,
    Message NVARCHAR(MAX) NOT NULL,
    NotificationType NVARCHAR(50), -- Hearing, Appointment, Payment, System
    RelatedId INT, -- معرف العنصر المرتبط
    RelatedType NVARCHAR(50), -- نوع العنصر المرتبط
    IsRead BIT DEFAULT 0,
    IsSent BIT DEFAULT 0,
    SendMethod NVARCHAR(20), -- Email, WhatsApp, Internal
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    ReadDate DATETIME2,
    FOREIGN KEY (UserId) REFERENCES Users(UserId)
);

-- جدول سجل تسجيل الدخول
CREATE TABLE LoginHistory (
    LoginId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    LoginDate DATETIME2 DEFAULT GETDATE(),
    LogoutDate DATETIME2,
    IPAddress NVARCHAR(50),
    UserAgent NVARCHAR(500),
    IsSuccessful BIT DEFAULT 1,
    FailureReason NVARCHAR(200),
    FOREIGN KEY (UserId) REFERENCES Users(UserId)
);

-- جدول النسخ الاحتياطية
CREATE TABLE BackupHistory (
    BackupId INT IDENTITY(1,1) PRIMARY KEY,
    BackupName NVARCHAR(200) NOT NULL,
    BackupPath NVARCHAR(1000) NOT NULL,
    BackupSize BIGINT,
    BackupType NVARCHAR(20), -- Full, Incremental
    IsAutomatic BIT DEFAULT 0,
    BackupDate DATETIME2 DEFAULT GETDATE(),
    CreatedBy INT,
    Status NVARCHAR(50) DEFAULT N'مكتملة',
    ErrorMessage NVARCHAR(MAX),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserId)
);

-- إدراج البيانات الأساسية
INSERT INTO CaseTypes (TypeName, TypeNameFr) VALUES
(N'قضايا مدنية', 'Affaires civiles'),
(N'قضايا جنائية', 'Affaires pénales'),
(N'قضايا تجارية', 'Affaires commerciales'),
(N'قضايا أسرية', 'Affaires familiales'),
(N'قضايا إدارية', 'Affaires administratives'),
(N'قضايا عقارية', 'Affaires immobilières'),
(N'قضايا عمالية', 'Affaires du travail'),
(N'قضايا ضريبية', 'Affaires fiscales');

INSERT INTO ExpenseTypes (TypeName, TypeNameFr) VALUES
(N'أتعاب المحاماة', 'Honoraires d''avocat'),
(N'رسوم المحكمة', 'Frais de tribunal'),
(N'رسوم التحرير', 'Frais de rédaction'),
(N'مصاريف التنقل', 'Frais de déplacement'),
(N'رسوم الخبرة', 'Frais d''expertise'),
(N'رسوم الترجمة', 'Frais de traduction'),
(N'مصاريف أخرى', 'Autres frais');

-- إنشاء المستخدم الافتراضي (admin/admin123)
INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, Role, IsActive) VALUES
('admin', 'hashed_password_here', 'salt_here', N'مدير النظام', '<EMAIL>', 'Admin', 1);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IX_LegalCases_CaseReference ON LegalCases(CaseReference);
CREATE INDEX IX_LegalCases_OfficeReference ON LegalCases(OfficeReference);
CREATE INDEX IX_LegalCases_ClientId ON LegalCases(ClientId);
CREATE INDEX IX_LegalCases_CaseTypeId ON LegalCases(CaseTypeId);
CREATE INDEX IX_Clients_CIN ON Clients(CIN);
CREATE INDEX IX_Hearings_HearingDate ON Hearings(HearingDate);
CREATE INDEX IX_Appointments_AppointmentDate ON Appointments(AppointmentDate);
CREATE INDEX IX_Notifications_UserId_IsRead ON Notifications(UserId, IsRead);
CREATE INDEX IX_ErrorLogs_LogDate ON ErrorLogs(LogDate);

GO
