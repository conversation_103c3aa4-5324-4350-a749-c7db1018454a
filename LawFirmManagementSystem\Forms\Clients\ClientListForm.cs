using LawFirmManagementSystem.Models.Entities;
using LawFirmManagementSystem.Services.Interfaces;
using LawFirmManagementSystem.Utils;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Forms.Clients
{
    /// <summary>
    /// نموذج قائمة العملاء
    /// Client list form
    /// </summary>
    public partial class ClientListForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly SimpleClientService _clientService;
        private readonly ILocalizationService _localizationService;
        private readonly ILogger<ClientListForm> _logger;

        // Controls
        private ToolStrip toolStrip;
        private DataGridView clientsDataGridView;
        private Panel searchPanel;
        private TextBox searchTextBox;
        private Button searchButton;
        private Button clearSearchButton;
        private ComboBox clientTypeComboBox;
        private Label searchLabel;
        private Label typeLabel;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel recordCountLabel;
        private ToolStripProgressBar progressBar;

        // Toolbar buttons
        private ToolStripButton addButton;
        private ToolStripButton editButton;
        private ToolStripButton deleteButton;
        private ToolStripButton refreshButton;
        private ToolStripButton exportButton;

        // Data
        private List<Client> _clients = new();
        private List<Client> _filteredClients = new();

        public ClientListForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _clientService = _serviceProvider.GetRequiredService<SimpleClientService>();
            _localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();
            _logger = _serviceProvider.GetRequiredService<ILogger<ClientListForm>>();

            InitializeComponent();
            InitializeLocalization();
            SetupEventHandlers();
            LoadClientsAsync();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "قائمة العملاء";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimumSize = new Size(800, 500);

            // Create toolbar
            CreateToolbar();

            // Create search panel
            CreateSearchPanel();

            // Create data grid
            CreateDataGrid();

            // Create status bar
            CreateStatusBar();

            // Layout controls
            this.Controls.AddRange(new Control[]
            {
                clientsDataGridView,
                searchPanel,
                toolStrip,
                statusStrip
            });
        }

        private void CreateToolbar()
        {
            toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24),
                Font = new Font("Tahoma", 9)
            };

            addButton = new ToolStripButton
            {
                Text = "إضافة عميل",
                Tag = "ClientManagement.NewClient",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            editButton = new ToolStripButton
            {
                Text = "تعديل",
                Tag = "Common.Edit",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            deleteButton = new ToolStripButton
            {
                Text = "حذف",
                Tag = "Common.Delete",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                Enabled = false
            };

            refreshButton = new ToolStripButton
            {
                Text = "تحديث",
                Tag = "Common.Refresh",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            exportButton = new ToolStripButton
            {
                Text = "تصدير",
                Tag = "Common.Export",
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                addButton,
                editButton,
                deleteButton,
                new ToolStripSeparator(),
                refreshButton,
                exportButton
            });
        }

        private void CreateSearchPanel()
        {
            searchPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 15),
                Size = new Size(50, 23),
                Font = new Font("Tahoma", 9),
                Tag = "Common.Search"
            };

            searchTextBox = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 23),
                Font = new Font("Tahoma", 9),
                PlaceholderText = "ابحث عن عميل..."
            };

            searchButton = new Button
            {
                Text = "بحث",
                Location = new Point(280, 11),
                Size = new Size(60, 25),
                Font = new Font("Tahoma", 9),
                Tag = "Common.Search"
            };

            clearSearchButton = new Button
            {
                Text = "مسح",
                Location = new Point(350, 11),
                Size = new Size(60, 25),
                Font = new Font("Tahoma", 9),
                Tag = "Common.Clear"
            };

            typeLabel = new Label
            {
                Text = "النوع:",
                Location = new Point(430, 15),
                Size = new Size(40, 23),
                Font = new Font("Tahoma", 9),
                Tag = "ClientManagement.ClientType"
            };

            clientTypeComboBox = new ComboBox
            {
                Location = new Point(480, 12),
                Size = new Size(120, 23),
                Font = new Font("Tahoma", 9),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            clientTypeComboBox.Items.AddRange(new[]
            {
                "الكل",
                "فرد",
                "شركة",
                "مؤسسة",
                "حكومي"
            });
            clientTypeComboBox.SelectedIndex = 0;

            searchPanel.Controls.AddRange(new Control[]
            {
                searchLabel,
                searchTextBox,
                searchButton,
                clearSearchButton,
                typeLabel,
                clientTypeComboBox
            });
        }

        private void CreateDataGrid()
        {
            clientsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 9),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Tahoma", 9, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.Black,
                    SelectionBackColor = Color.FromArgb(41, 128, 185),
                    SelectionForeColor = Color.White,
                    Alignment = DataGridViewContentAlignment.MiddleRight
                },
                AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(248, 249, 250)
                }
            };

            // Add columns
            clientsDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Id",
                    HeaderText = "الرقم",
                    DataPropertyName = "Id",
                    Width = 80,
                    Tag = "Common.ID"
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Name",
                    HeaderText = "الاسم",
                    DataPropertyName = "Name",
                    Width = 200,
                    Tag = "ClientManagement.ClientName"
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Type",
                    HeaderText = "النوع",
                    DataPropertyName = "Type",
                    Width = 100,
                    Tag = "ClientManagement.ClientType"
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Email",
                    HeaderText = "البريد الإلكتروني",
                    DataPropertyName = "Email",
                    Width = 180,
                    Tag = "Common.Email"
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Phone",
                    HeaderText = "الهاتف",
                    DataPropertyName = "Phone",
                    Width = 120,
                    Tag = "Common.Phone"
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreatedDate",
                    HeaderText = "تاريخ الإضافة",
                    DataPropertyName = "CreatedDate",
                    Width = 120,
                    Tag = "Common.CreatedDate"
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "IsActive",
                    HeaderText = "نشط",
                    DataPropertyName = "IsActive",
                    Width = 60,
                    Tag = "Common.Active"
                }
            });
        }

        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip
            {
                Font = new Font("Tahoma", 9)
            };

            recordCountLabel = new ToolStripStatusLabel
            {
                Text = "0 عميل",
                Spring = false
            };

            progressBar = new ToolStripProgressBar
            {
                Visible = false,
                Size = new Size(100, 16)
            };

            statusStrip.Items.AddRange(new ToolStripItem[]
            {
                recordCountLabel,
                new ToolStripStatusLabel { Spring = true },
                progressBar
            });
        }

        private void InitializeLocalization()
        {
            LocalizationHelper.Initialize(_localizationService);
            LocalizationHelper.LocalizeForm(this);
        }

        private void SetupEventHandlers()
        {
            // Toolbar events
            addButton.Click += AddButton_Click;
            editButton.Click += EditButton_Click;
            deleteButton.Click += DeleteButton_Click;
            refreshButton.Click += RefreshButton_Click;
            exportButton.Click += ExportButton_Click;

            // Search events
            searchButton.Click += SearchButton_Click;
            clearSearchButton.Click += ClearSearchButton_Click;
            searchTextBox.KeyDown += SearchTextBox_KeyDown;
            clientTypeComboBox.SelectedIndexChanged += ClientTypeComboBox_SelectedIndexChanged;

            // DataGridView events
            clientsDataGridView.SelectionChanged += ClientsDataGridView_SelectionChanged;
            clientsDataGridView.CellDoubleClick += ClientsDataGridView_CellDoubleClick;

            // Form events
            this.Load += ClientListForm_Load;
        }

        private async void LoadClientsAsync()
        {
            try
            {
                SetLoading(true);
                
                _clients = (await _clientService.GetAllClientsAsync()).ToList();
                _filteredClients = new List<Client>(_clients);
                
                UpdateDataGrid();
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading clients");
                MessageBox.Show(
                    LocalizationHelper.GetText("Messages.Error") + ": " + ex.Message,
                    LocalizationHelper.GetText("Messages.Error"),
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                SetLoading(false);
            }
        }

        private void UpdateDataGrid()
        {
            clientsDataGridView.DataSource = null;
            clientsDataGridView.DataSource = _filteredClients;
            
            // Format date columns
            if (clientsDataGridView.Columns["CreatedDate"] != null)
            {
                clientsDataGridView.Columns["CreatedDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
            }
        }

        private void UpdateRecordCount()
        {
            var count = _filteredClients.Count;
            var total = _clients.Count;
            
            recordCountLabel.Text = count == total 
                ? $"{count} عميل"
                : $"{count} من {total} عميل";
        }

        private void SetLoading(bool loading)
        {
            progressBar.Visible = loading;
            toolStrip.Enabled = !loading;
            searchPanel.Enabled = !loading;
            clientsDataGridView.Enabled = !loading;
        }

        private void FilterClients()
        {
            var searchText = searchTextBox.Text.Trim().ToLower();
            var selectedType = clientTypeComboBox.SelectedIndex;

            _filteredClients = _clients.Where(client =>
            {
                // Text search
                var matchesText = string.IsNullOrEmpty(searchText) ||
                    client.Name.ToLower().Contains(searchText) ||
                    (client.Email?.ToLower().Contains(searchText) ?? false) ||
                    (client.Phone?.Contains(searchText) ?? false);

                // Type filter
                var matchesType = selectedType == 0 || // "الكل"
                    (selectedType == 1 && client.Type == "فرد") ||
                    (selectedType == 2 && client.Type == "شركة") ||
                    (selectedType == 3 && client.Type == "مؤسسة") ||
                    (selectedType == 4 && client.Type == "حكومي");

                return matchesText && matchesType;
            }).ToList();

            UpdateDataGrid();
            UpdateRecordCount();
        }

        // Event handlers
        private void AddButton_Click(object sender, EventArgs e)
        {
            // TODO: Open new client form
            MessageBox.Show("نموذج إضافة عميل جديد قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (clientsDataGridView.SelectedRows.Count > 0)
            {
                var selectedClient = (Client)clientsDataGridView.SelectedRows[0].DataBoundItem;
                // TODO: Open edit client form
                MessageBox.Show($"تعديل العميل: {selectedClient.Name}", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            if (clientsDataGridView.SelectedRows.Count > 0)
            {
                var selectedClient = (Client)clientsDataGridView.SelectedRows[0].DataBoundItem;
                
                var result = MessageBox.Show(
                    $"هل تريد حذف العميل: {selectedClient.Name}؟",
                    LocalizationHelper.GetText("Messages.Confirmation"),
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        SetLoading(true);
                        await _clientService.DeleteClientAsync(selectedClient.Id);
                        await LoadClientsAsync();
                        
                        MessageBox.Show(
                            LocalizationHelper.GetText("Messages.DeleteSuccess"),
                            LocalizationHelper.GetText("Messages.Success"),
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting client {ClientId}", selectedClient.Id);
                        MessageBox.Show(
                            LocalizationHelper.GetText("Messages.DeleteError") + ": " + ex.Message,
                            LocalizationHelper.GetText("Messages.Error"),
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);
                    }
                    finally
                    {
                        SetLoading(false);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await LoadClientsAsync();
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("وظيفة التصدير قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            FilterClients();
        }

        private void ClearSearchButton_Click(object sender, EventArgs e)
        {
            searchTextBox.Clear();
            clientTypeComboBox.SelectedIndex = 0;
            FilterClients();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                FilterClients();
            }
        }

        private void ClientTypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterClients();
        }

        private void ClientsDataGridView_SelectionChanged(object sender, EventArgs e)
        {
            var hasSelection = clientsDataGridView.SelectedRows.Count > 0;
            editButton.Enabled = hasSelection;
            deleteButton.Enabled = hasSelection;
        }

        private void ClientsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditButton_Click(sender, e);
            }
        }

        private void ClientListForm_Load(object sender, EventArgs e)
        {
            _logger.LogInformation("Client list form loaded");
        }
    }
}
