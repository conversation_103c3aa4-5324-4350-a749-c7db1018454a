@echo off
echo ========================================
echo فحص صحة النظام
echo System Health Check
echo ========================================
echo.

set ERRORS=0
set WARNINGS=0
set INFO_COUNT=0

echo [%time%] بدء فحص صحة النظام...
echo [%time%] Starting system health check...
echo.

echo ========================================
echo 1. فحص التطبيق / Application Check
echo ========================================

echo التحقق من ملفات التطبيق...
echo Checking application files...

if not exist "LawFirmManagementSystem.exe" (
    if not exist "bin\Debug\net8.0-windows\LawFirmManagementSystem.exe" (
        echo ✗ ملف التطبيق غير موجود
        echo ✗ Application file not found
        set /a ERRORS+=1
    ) else (
        echo ✓ ملف التطبيق موجود في مجلد Debug
        echo ✓ Application file found in Debug folder
        set /a INFO_COUNT+=1
    )
) else (
    echo ✓ ملف التطبيق موجود
    echo ✓ Application file found
    set /a INFO_COUNT+=1
)

echo التحقق من ملفات الإعدادات...
echo Checking configuration files...

if not exist "appsettings.json" (
    echo ✗ ملف الإعدادات الرئيسي غير موجود
    echo ✗ Main configuration file not found
    set /a ERRORS+=1
) else (
    echo ✓ ملف الإعدادات الرئيسي موجود
    echo ✓ Main configuration file found
    set /a INFO_COUNT+=1
)

if exist "appsettings.Development.json" (
    echo ✓ ملف إعدادات التطوير موجود
    echo ✓ Development configuration file found
    set /a INFO_COUNT+=1
)

if exist "appsettings.Production.json" (
    echo ✓ ملف إعدادات الإنتاج موجود
    echo ✓ Production configuration file found
    set /a INFO_COUNT+=1
)

echo.

echo ========================================
echo 2. فحص قاعدة البيانات / Database Check
echo ========================================

echo التحقق من اتصال قاعدة البيانات...
echo Checking database connection...

sqlcmd -S "(localdb)\MSSQLLocalDB" -Q "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠ لا يمكن الاتصال بـ LocalDB
    echo ⚠ Cannot connect to LocalDB
    set /a WARNINGS+=1
    
    echo التحقق من SQL Server Express...
    echo Checking SQL Server Express...
    sqlcmd -S ".\SQLEXPRESS" -Q "SELECT 1" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ✗ لا يمكن الاتصال بقاعدة البيانات
        echo ✗ Cannot connect to database
        set /a ERRORS+=1
    ) else (
        echo ✓ متصل بـ SQL Server Express
        echo ✓ Connected to SQL Server Express
        set /a INFO_COUNT+=1
    )
) else (
    echo ✓ متصل بـ LocalDB
    echo ✓ Connected to LocalDB
    set /a INFO_COUNT+=1
)

echo.

echo ========================================
echo 3. فحص المجلدات / Folders Check
echo ========================================

echo التحقق من المجلدات المطلوبة...
echo Checking required folders...

set REQUIRED_FOLDERS=Logs Documents Backups Reports Templates

for %%f in (%REQUIRED_FOLDERS%) do (
    if not exist "%%f" (
        echo ⚠ مجلد %%f غير موجود - سيتم إنشاؤه
        echo ⚠ Folder %%f not found - will be created
        mkdir "%%f" 2>nul
        set /a WARNINGS+=1
    ) else (
        echo ✓ مجلد %%f موجود
        echo ✓ Folder %%f exists
        set /a INFO_COUNT+=1
    )
)

echo.

echo ========================================
echo 4. فحص الأذونات / Permissions Check
echo ========================================

echo التحقق من أذونات الكتابة...
echo Checking write permissions...

echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    del test_write.tmp
    echo ✓ أذونات الكتابة متوفرة
    echo ✓ Write permissions available
    set /a INFO_COUNT+=1
) else (
    echo ✗ أذونات الكتابة غير متوفرة
    echo ✗ Write permissions not available
    set /a ERRORS+=1
)

echo التحقق من أذونات إنشاء المجلدات...
echo Checking folder creation permissions...

mkdir test_folder 2>nul
if exist test_folder (
    rmdir test_folder
    echo ✓ أذونات إنشاء المجلدات متوفرة
    echo ✓ Folder creation permissions available
    set /a INFO_COUNT+=1
) else (
    echo ✗ أذونات إنشاء المجلدات غير متوفرة
    echo ✗ Folder creation permissions not available
    set /a ERRORS+=1
)

echo.

echo ========================================
echo 5. فحص الموارد / Resources Check
echo ========================================

echo التحقق من استخدام الذاكرة...
echo Checking memory usage...

for /f "skip=1 tokens=2" %%i in ('wmic OS get TotalVisibleMemorySize /value') do (
    if not "%%i"=="" (
        set /a TOTAL_MEMORY=%%i/1024
        goto memory_available
    )
)

:memory_available
for /f "skip=1 tokens=2" %%i in ('wmic OS get FreePhysicalMemory /value') do (
    if not "%%i"=="" (
        set /a FREE_MEMORY=%%i/1024
        goto memory_check
    )
)

:memory_check
set /a USED_MEMORY=%TOTAL_MEMORY%-%FREE_MEMORY%
set /a MEMORY_PERCENT=%USED_MEMORY%*100/%TOTAL_MEMORY%

echo إجمالي الذاكرة: %TOTAL_MEMORY% MB
echo Total Memory: %TOTAL_MEMORY% MB
echo الذاكرة المستخدمة: %USED_MEMORY% MB (%MEMORY_PERCENT%%%)
echo Used Memory: %USED_MEMORY% MB (%MEMORY_PERCENT%%%)

if %MEMORY_PERCENT% GTR 90 (
    echo ✗ استخدام الذاكرة مرتفع جداً
    echo ✗ Memory usage very high
    set /a ERRORS+=1
) else if %MEMORY_PERCENT% GTR 75 (
    echo ⚠ استخدام الذاكرة مرتفع
    echo ⚠ Memory usage high
    set /a WARNINGS+=1
) else (
    echo ✓ استخدام الذاكرة طبيعي
    echo ✓ Memory usage normal
    set /a INFO_COUNT+=1
)

echo التحقق من مساحة القرص...
echo Checking disk space...

for /f "tokens=3" %%i in ('dir C:\ /-c ^| find "bytes free"') do set FREE_SPACE=%%i
set /a FREE_SPACE_GB=%FREE_SPACE:~0,-10%/1024/1024/1024

echo المساحة المتاحة: %FREE_SPACE_GB% GB
echo Available Space: %FREE_SPACE_GB% GB

if %FREE_SPACE_GB% LSS 1 (
    echo ✗ مساحة القرص منخفضة جداً
    echo ✗ Disk space very low
    set /a ERRORS+=1
) else if %FREE_SPACE_GB% LSS 5 (
    echo ⚠ مساحة القرص منخفضة
    echo ⚠ Disk space low
    set /a WARNINGS+=1
) else (
    echo ✓ مساحة القرص كافية
    echo ✓ Disk space sufficient
    set /a INFO_COUNT+=1
)

echo.

echo ========================================
echo 6. فحص العمليات / Processes Check
echo ========================================

echo التحقق من العمليات المتعلقة بالتطبيق...
echo Checking application-related processes...

tasklist /FI "IMAGENAME eq LawFirmManagementSystem.exe" 2>nul | find /i "LawFirmManagementSystem.exe" >nul
if %errorlevel% equ 0 (
    echo ✓ التطبيق قيد التشغيل
    echo ✓ Application is running
    set /a INFO_COUNT+=1
) else (
    echo ℹ التطبيق غير قيد التشغيل
    echo ℹ Application is not running
)

tasklist /FI "IMAGENAME eq sqlservr.exe" 2>nul | find /i "sqlservr.exe" >nul
if %errorlevel% equ 0 (
    echo ✓ SQL Server قيد التشغيل
    echo ✓ SQL Server is running
    set /a INFO_COUNT+=1
) else (
    echo ℹ SQL Server غير قيد التشغيل (قد يكون LocalDB)
    echo ℹ SQL Server not running (might be LocalDB)
)

echo.

echo ========================================
echo 7. فحص السجلات / Logs Check
echo ========================================

echo التحقق من ملفات السجلات...
echo Checking log files...

if exist "Logs\*.log" (
    echo ملفات السجلات الموجودة:
    echo Existing log files:
    dir "Logs\*.log" /b 2>nul
    
    echo التحقق من أحدث سجل...
    echo Checking latest log...
    for /f %%i in ('dir "Logs\*.log" /b /o-d 2^>nul') do (
        set LATEST_LOG=%%i
        goto check_latest_log
    )
    
    :check_latest_log
    if defined LATEST_LOG (
        echo أحدث سجل: %LATEST_LOG%
        echo Latest log: %LATEST_LOG%
        
        echo آخر 5 أسطر من السجل:
        echo Last 5 lines from log:
        powershell -command "Get-Content 'Logs\%LATEST_LOG%' -Tail 5" 2>nul
        set /a INFO_COUNT+=1
    )
) else (
    echo ℹ لا توجد ملفات سجلات
    echo ℹ No log files found
)

echo.

echo ========================================
echo ملخص النتائج / Results Summary
echo ========================================

echo إجمالي الفحوصات: %INFO_COUNT%
echo Total Checks: %INFO_COUNT%
echo الأخطاء: %ERRORS%
echo Errors: %ERRORS%
echo التحذيرات: %WARNINGS%
echo Warnings: %WARNINGS%
echo.

if %ERRORS% equ 0 (
    if %WARNINGS% equ 0 (
        echo ✅ النظام يعمل بشكل مثالي
        echo ✅ System is running perfectly
        set HEALTH_STATUS=EXCELLENT
    ) else (
        echo ⚠️ النظام يعمل مع بعض التحذيرات
        echo ⚠️ System is running with some warnings
        set HEALTH_STATUS=GOOD
    )
) else (
    echo ❌ النظام يحتاج إلى إصلاحات
    echo ❌ System needs repairs
    set HEALTH_STATUS=POOR
)

echo حالة النظام: %HEALTH_STATUS%
echo System Status: %HEALTH_STATUS%
echo.

echo تم إنشاء تقرير الصحة في: %date% %time%
echo Health report generated at: %date% %time%

echo.
echo للحصول على المساعدة:
echo For help:
echo <EMAIL>
echo.

pause
