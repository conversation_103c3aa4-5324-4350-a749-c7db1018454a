using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LawFirmManagementSystem.Forms
{
    /// <summary>
    /// شاشة البداية
    /// Splash screen form
    /// </summary>
    public partial class SplashForm : Form
    {
        private Panel mainPanel;
        private PictureBox logoBox;
        private Label titleLabel;
        private Label subtitleLabel;
        private Label versionLabel;
        private Label copyrightLabel;
        private ProgressBar progressBar;
        private Label statusLabel;
        private Timer progressTimer;
        private int progressValue = 0;

        public SplashForm()
        {
            InitializeComponent();
            SetupProgressTimer();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.FromArgb(41, 128, 185);
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Main panel with rounded corners effect
            mainPanel = new Panel
            {
                Size = new Size(480, 330),
                Location = new Point(10, 10),
                BackColor = Color.White
            };

            // Logo placeholder
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(210, 30),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.FromArgb(41, 128, 185)
            };

            // Create a simple logo using graphics
            var logoBitmap = new Bitmap(80, 80);
            using (var g = Graphics.FromImage(logoBitmap))
            {
                g.Clear(Color.FromArgb(41, 128, 185));
                g.FillEllipse(Brushes.White, 10, 10, 60, 60);
                using (var font = new Font("Arial", 20, FontStyle.Bold))
                {
                    var textSize = g.MeasureString("⚖", font);
                    g.DrawString("⚖", font, Brushes.DarkBlue, 
                        (80 - textSize.Width) / 2, (80 - textSize.Height) / 2);
                }
            }
            logoBox.Image = logoBitmap;

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة مكتب المحاماة",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(50, 130),
                Size = new Size(380, 35),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Subtitle
            subtitleLabel = new Label
            {
                Text = "Law Firm Management System",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(50, 165),
                Size = new Size(380, 25),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Version
            versionLabel = new Label
            {
                Text = "الإصدار 1.0.0 / Version 1.0.0",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(149, 165, 166),
                Location = new Point(50, 200),
                Size = new Size(380, 20),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Progress bar
            progressBar = new ProgressBar
            {
                Location = new Point(50, 240),
                Size = new Size(380, 15),
                Style = ProgressBarStyle.Continuous,
                ForeColor = Color.FromArgb(41, 128, 185),
                BackColor = Color.FromArgb(236, 240, 241)
            };

            // Status label
            statusLabel = new Label
            {
                Text = "جاري التحميل... / Loading...",
                Font = new Font("Tahoma", 9),
                ForeColor = Color.FromArgb(127, 140, 141),
                Location = new Point(50, 260),
                Size = new Size(380, 20),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Copyright
            copyrightLabel = new Label
            {
                Text = "© 2025 Law Firm Management Solutions",
                Font = new Font("Tahoma", 8),
                ForeColor = Color.FromArgb(149, 165, 166),
                Location = new Point(50, 290),
                Size = new Size(380, 15),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Add controls to main panel
            mainPanel.Controls.AddRange(new Control[]
            {
                logoBox,
                titleLabel,
                subtitleLabel,
                versionLabel,
                progressBar,
                statusLabel,
                copyrightLabel
            });

            // Add main panel to form
            this.Controls.Add(mainPanel);

            // Add border effect
            this.Paint += SplashForm_Paint;
        }

        private void SplashForm_Paint(object sender, PaintEventArgs e)
        {
            // Draw border around the form
            var rect = this.ClientRectangle;
            rect.Inflate(-1, -1);
            ControlPaint.DrawBorder(e.Graphics, rect, Color.FromArgb(189, 195, 199), ButtonBorderStyle.Solid);
        }

        private void SetupProgressTimer()
        {
            progressTimer = new Timer
            {
                Interval = 50 // Update every 50ms
            };

            progressTimer.Tick += ProgressTimer_Tick;
        }

        private void ProgressTimer_Tick(object sender, EventArgs e)
        {
            progressValue += 2;
            progressBar.Value = Math.Min(progressValue, 100);

            // Update status text based on progress
            UpdateStatusText();

            if (progressValue >= 100)
            {
                progressTimer.Stop();
                
                // Close splash screen after a short delay
                Task.Delay(500).ContinueWith(_ =>
                {
                    if (this.InvokeRequired)
                    {
                        this.Invoke(new Action(() => this.Close()));
                    }
                    else
                    {
                        this.Close();
                    }
                });
            }
        }

        private void UpdateStatusText()
        {
            string arabicText, englishText;

            if (progressValue < 20)
            {
                arabicText = "تهيئة النظام...";
                englishText = "Initializing system...";
            }
            else if (progressValue < 40)
            {
                arabicText = "تحميل الإعدادات...";
                englishText = "Loading configuration...";
            }
            else if (progressValue < 60)
            {
                arabicText = "الاتصال بقاعدة البيانات...";
                englishText = "Connecting to database...";
            }
            else if (progressValue < 80)
            {
                arabicText = "تحميل الخدمات...";
                englishText = "Loading services...";
            }
            else if (progressValue < 95)
            {
                arabicText = "تحضير الواجهة...";
                englishText = "Preparing interface...";
            }
            else
            {
                arabicText = "اكتمل التحميل";
                englishText = "Loading complete";
            }

            statusLabel.Text = $"{arabicText} / {englishText}";
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            
            // Start progress animation
            progressTimer.Start();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            // Clean up resources
            progressTimer?.Stop();
            progressTimer?.Dispose();
            logoBox?.Image?.Dispose();
            
            base.OnFormClosed(e);
        }

        // Prevent form from being closed by Alt+F4 or clicking X
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing && progressValue < 100)
            {
                e.Cancel = true;
            }
            base.OnFormClosing(e);
        }

        // Add some animation effects
        protected override void SetVisibleCore(bool value)
        {
            base.SetVisibleCore(value);
            
            if (value)
            {
                // Fade in effect
                this.Opacity = 0;
                var fadeTimer = new Timer { Interval = 20 };
                fadeTimer.Tick += (s, e) =>
                {
                    if (this.Opacity < 1)
                    {
                        this.Opacity += 0.05;
                    }
                    else
                    {
                        fadeTimer.Stop();
                        fadeTimer.Dispose();
                    }
                };
                fadeTimer.Start();
            }
        }
    }
}
