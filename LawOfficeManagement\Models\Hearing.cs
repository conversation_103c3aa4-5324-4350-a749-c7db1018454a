using System;
using System.ComponentModel.DataAnnotations;

namespace LawOfficeManagement.Models
{
    public class Hearing
    {
        public int HearingId { get; set; }
        
        [Required]
        public int CaseId { get; set; }
        
        [Required]
        public DateTime HearingDate { get; set; }
        
        [StringLength(100)]
        public string? HearingType { get; set; }
        
        [StringLength(50)]
        public string? CourtRoom { get; set; }
        
        [StringLength(200)]
        public string? Judge { get; set; }
        
        [StringLength(50)]
        public string Status { get; set; } = "مجدولة";
        
        public string? Outcome { get; set; }
        
        public DateTime? NextHearingDate { get; set; }
        
        public string? Notes { get; set; }
        
        public bool ReminderSent { get; set; } = false;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual LegalCase? LegalCase { get; set; }
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
    }
    
    public class Appointment
    {
        public int AppointmentId { get; set; }
        
        public int? CaseId { get; set; }
        
        public int? ClientId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        [Required]
        public DateTime AppointmentDate { get; set; }
        
        public int Duration { get; set; } = 60; // بالدقائق
        
        [StringLength(200)]
        public string? Location { get; set; }
        
        [StringLength(50)]
        public string Status { get; set; } = "مجدول";
        
        public bool ReminderSent { get; set; } = false;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        // Navigation properties
        public virtual LegalCase? LegalCase { get; set; }
        public virtual Client? Client { get; set; }
        public virtual User? Creator { get; set; }
        public virtual User? Modifier { get; set; }
    }
    
    public class Opponent
    {
        public int OpponentId { get; set; }
        
        [Required]
        public int CaseId { get; set; }
        
        [Required]
        public ClientType OpponentType { get; set; }
        
        [Required]
        [StringLength(200)]
        public string FullName { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? FullNameFr { get; set; }
        
        [StringLength(20)]
        public string? CIN { get; set; }
        
        [StringLength(50)]
        public string? CompanyRegister { get; set; }
        
        [StringLength(500)]
        public string? Address { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [StringLength(200)]
        public string? LawyerName { get; set; }
        
        [StringLength(20)]
        public string? LawyerPhone { get; set; }
        
        [StringLength(100)]
        public string? LawyerEmail { get; set; }
        
        public string? Notes { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public int? CreatedBy { get; set; }
        
        // Navigation properties
        public virtual LegalCase? LegalCase { get; set; }
        public virtual User? Creator { get; set; }
    }
}
