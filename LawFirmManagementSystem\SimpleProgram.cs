using System;
using System.Windows.Forms;
using System.Drawing;

namespace LawFirmManagementSystem
{
    /// <summary>
    /// برنامج مبسط لاختبار النظام
    /// Simple program to test the system
    /// </summary>
    public static class SimpleProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // Show simple login form
                using var loginForm = new SimpleLoginForm();
                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    // Show simple main form
                    Application.Run(new SimpleMainForm());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ في النظام", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// نافذة تسجيل دخول مبسطة
    /// Simple login form
    /// </summary>
    public class SimpleLoginForm : Form
    {
        private TextBox usernameTextBox;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button cancelButton;
        private Label titleLabel;
        private Label usernameLabel;
        private Label passwordLabel;

        public SimpleLoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - نظام إدارة مكتب المحاماة";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة مكتب المحاماة",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                Location = new Point(50, 30),
                Size = new Size(300, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Username
            usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Location = new Point(50, 80),
                Size = new Size(100, 23),
                Font = new Font("Tahoma", 9)
            };

            usernameTextBox = new TextBox
            {
                Location = new Point(50, 105),
                Size = new Size(280, 25),
                Font = new Font("Tahoma", 10),
                Text = "admin"
            };

            // Password
            passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Location = new Point(50, 140),
                Size = new Size(100, 23),
                Font = new Font("Tahoma", 9)
            };

            passwordTextBox = new TextBox
            {
                Location = new Point(50, 165),
                Size = new Size(280, 25),
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true,
                Text = "Admin@123"
            };

            // Login button
            loginButton = new Button
            {
                Text = "تسجيل الدخول",
                Location = new Point(50, 210),
                Size = new Size(120, 35),
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                BackColor = Color.DarkBlue,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            loginButton.Click += LoginButton_Click;

            // Cancel button
            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(210, 210),
                Size = new Size(120, 35),
                Font = new Font("Tahoma", 10),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.AddRange(new Control[]
            {
                titleLabel, usernameLabel, usernameTextBox,
                passwordLabel, passwordTextBox, loginButton, cancelButton
            });
        }

        private void LoginButton_Click(object sender, EventArgs e)
        {
            if (usernameTextBox.Text == "admin" && passwordTextBox.Text == "Admin@123")
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// النافذة الرئيسية المبسطة
    /// Simple main form
    /// </summary>
    public class SimpleMainForm : Form
    {
        private MenuStrip menuStrip;
        private Label welcomeLabel;

        public SimpleMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام إدارة مكتب المحاماة - النافذة الرئيسية";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            // Create menu
            menuStrip = new MenuStrip();
            
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add("خروج", null, (s, e) => this.Close());

            var clientsMenu = new ToolStripMenuItem("العملاء");
            clientsMenu.DropDownItems.Add("قائمة العملاء", null, ShowClientsMessage);
            clientsMenu.DropDownItems.Add("عميل جديد", null, ShowNewClientMessage);

            var casesMenu = new ToolStripMenuItem("القضايا");
            casesMenu.DropDownItems.Add("قائمة القضايا", null, ShowCasesMessage);
            casesMenu.DropDownItems.Add("قضية جديدة", null, ShowNewCaseMessage);

            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, ShowAbout);

            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, clientsMenu, casesMenu, helpMenu });

            // Welcome label
            welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام إدارة مكتب المحاماة\n\n" +
                       "النظام يعمل بنجاح!\n\n" +
                       "يمكنك الآن:\n" +
                       "• تصفح القوائم\n" +
                       "• اختبار الوظائف المختلفة\n" +
                       "• تطوير المزيد من الميزات",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Tahoma", 14),
                ForeColor = Color.DarkBlue
            };

            this.Controls.AddRange(new Control[] { welcomeLabel, menuStrip });
            this.MainMenuStrip = menuStrip;
        }

        private void ShowClientsMessage(object sender, EventArgs e)
        {
            MessageBox.Show("قائمة العملاء\n\nهذه الوظيفة قيد التطوير.\nسيتم إضافة قائمة العملاء الكاملة قريباً.", 
                "العملاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNewClientMessage(object sender, EventArgs e)
        {
            MessageBox.Show("إضافة عميل جديد\n\nهذه الوظيفة قيد التطوير.\nسيتم إضافة نموذج العميل الجديد قريباً.", 
                "عميل جديد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCasesMessage(object sender, EventArgs e)
        {
            MessageBox.Show("قائمة القضايا\n\nهذه الوظيفة قيد التطوير.\nسيتم إضافة إدارة القضايا قريباً.", 
                "القضايا", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNewCaseMessage(object sender, EventArgs e)
        {
            MessageBox.Show("إضافة قضية جديدة\n\nهذه الوظيفة قيد التطوير.\nسيتم إضافة نموذج القضية الجديدة قريباً.", 
                "قضية جديدة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAbout(object sender, EventArgs e)
        {
            MessageBox.Show("نظام إدارة مكتب المحاماة\nالإصدار 1.0.0\n\n" +
                           "تم تطويره بواسطة Augment Agent\n" +
                           "© 2025 Law Firm Management Solutions", 
                "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
