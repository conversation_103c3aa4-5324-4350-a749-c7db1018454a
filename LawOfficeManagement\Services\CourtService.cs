using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using LawOfficeManagement.Models;

namespace LawOfficeManagement.Services
{
    public class CourtService
    {
        private readonly DatabaseService _databaseService;
        private readonly ErrorLogService _errorLogService;
        
        public CourtService()
        {
            _databaseService = DatabaseService.Instance;
            _errorLogService = new ErrorLogService();
        }
        
        public async Task<List<Court>> GetAllCourtsAsync()
        {
            try
            {
                var query = @"
                    SELECT CourtId, CourtName, CourtNameFr, CourtType, Jurisdiction, 
                           City, Address, Phone, Fax, Email, IsActive, CreatedDate
                    FROM Courts
                    WHERE IsActive = 1
                    ORDER BY CourtName";
                
                var dataTable = await _databaseService.ExecuteQueryAsync(query);
                var courts = new List<Court>();
                
                foreach (DataRow row in dataTable.Rows)
                {
                    var court = new Court
                    {
                        CourtId = (int)row["CourtId"],
                        CourtName = row["CourtName"].ToString()!,
                        CourtNameFr = row["CourtNameFr"] as string,
                        CourtType = row["CourtType"] as string,
                        Jurisdiction = row["Jurisdiction"] as string,
                        City = row["City"] as string,
                        Address = row["Address"] as string,
                        Phone = row["Phone"] as string,
                        Fax = row["Fax"] as string,
                        Email = row["Email"] as string,
                        IsActive = (bool)row["IsActive"],
                        CreatedDate = (DateTime)row["CreatedDate"]
                    };
                    courts.Add(court);
                }
                
                return courts;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("CourtService.GetAllCourts", ex);
                throw;
            }
        }
    }
}
