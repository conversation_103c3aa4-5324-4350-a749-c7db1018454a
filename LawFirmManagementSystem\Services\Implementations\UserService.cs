using LawFirmManagementSystem.Data;
using LawFirmManagementSystem.Models.Entities;
using LawFirmManagementSystem.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LawFirmManagementSystem.Services.Implementations
{
    /// <summary>
    /// تنفيذ خدمة المستخدمين
    /// User service implementation
    /// </summary>
    public class UserService : IUserService
    {
        private readonly LawFirmDbContext _context;
        private readonly ILogger<UserService> _logger;
        private readonly IAuditService _auditService;
        private readonly IAuthenticationService _authenticationService;

        public UserService(
            LawFirmDbContext context,
            ILogger<UserService> logger,
            IAuditService auditService,
            IAuthenticationService authenticationService)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
        }

        public async Task<User?> GetUserByIdAsync(int id)
        {
            try
            {
                return await _context.Users
                    .Include(u => u.Sessions)
                    .Include(u => u.Permissions)
                    .FirstOrDefaultAsync(u => u.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by ID {UserId}", id);
                return null;
            }
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            try
            {
                return await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by username {Username}", username);
                return null;
            }
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            try
            {
                return await _context.Users
                    .FirstOrDefaultAsync(u => u.Email == email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by email {Email}", email);
                return null;
            }
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            try
            {
                return await _context.Users
                    .OrderBy(u => u.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all users");
                return Enumerable.Empty<User>();
            }
        }

        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            try
            {
                return await _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active users");
                return Enumerable.Empty<User>();
            }
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            try
            {
                if (user == null)
                    throw new ArgumentNullException(nameof(user));

                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("Password cannot be null or empty", nameof(password));

                // Validate username uniqueness
                if (!await IsUsernameAvailableAsync(user.Username))
                    throw new InvalidOperationException($"Username '{user.Username}' is already taken");

                // Validate email uniqueness
                if (!string.IsNullOrEmpty(user.Email) && !await IsEmailAvailableAsync(user.Email))
                    throw new InvalidOperationException($"Email '{user.Email}' is already taken");

                // Hash password
                user.PasswordHash = _authenticationService.HashPassword(password);
                user.CreatedDate = DateTime.Now;
                user.IsActive = true;

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("User created successfully: {Username}", user.Username);
                await _auditService.LogAsync(user.Id, "Create", "User", user.Id, $"User created: {user.Username}");

                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user {Username}", user?.Username);
                throw;
            }
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            try
            {
                if (user == null)
                    throw new ArgumentNullException(nameof(user));

                var existingUser = await _context.Users.FindAsync(user.Id);
                if (existingUser == null)
                    throw new InvalidOperationException($"User with ID {user.Id} not found");

                // Validate username uniqueness (excluding current user)
                if (!await IsUsernameAvailableAsync(user.Username, user.Id))
                    throw new InvalidOperationException($"Username '{user.Username}' is already taken");

                // Validate email uniqueness (excluding current user)
                if (!string.IsNullOrEmpty(user.Email) && !await IsEmailAvailableAsync(user.Email, user.Id))
                    throw new InvalidOperationException($"Email '{user.Email}' is already taken");

                // Update properties
                existingUser.Username = user.Username;
                existingUser.FullName = user.FullName;
                existingUser.FullNameFr = user.FullNameFr;
                existingUser.Email = user.Email;
                existingUser.Phone = user.Phone;
                existingUser.Role = user.Role;
                existingUser.Department = user.Department;
                existingUser.Position = user.Position;
                existingUser.PreferredLanguage = user.PreferredLanguage;
                existingUser.Theme = user.Theme;
                existingUser.SessionTimeout = user.SessionTimeout;
                existingUser.IsActive = user.IsActive;

                await _context.SaveChangesAsync();

                _logger.LogInformation("User updated successfully: {Username}", user.Username);
                await _auditService.LogAsync(user.Id, "Update", "User", user.Id, $"User updated: {user.Username}");

                return existingUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", user?.Id);
                throw;
            }
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                    return false;

                // Soft delete
                user.SoftDelete();
                await _context.SaveChangesAsync();

                _logger.LogInformation("User deleted successfully: {UserId}", id);
                await _auditService.LogAsync(id, "Delete", "User", id, $"User deleted: {user.Username}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                return false;
            }
        }

        public async Task<bool> ActivateUserAsync(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                    return false;

                user.IsActive = true;
                await _context.SaveChangesAsync();

                _logger.LogInformation("User activated: {UserId}", id);
                await _auditService.LogAsync(id, "Activate", "User", id, $"User activated: {user.Username}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating user {UserId}", id);
                return false;
            }
        }

        public async Task<bool> DeactivateUserAsync(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                    return false;

                user.IsActive = false;
                await _context.SaveChangesAsync();

                _logger.LogInformation("User deactivated: {UserId}", id);
                await _auditService.LogAsync(id, "Deactivate", "User", id, $"User deactivated: {user.Username}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating user {UserId}", id);
                return false;
            }
        }

        public async Task<User?> AuthenticateAsync(string username, string password)
        {
            return await _authenticationService.AuthenticateAsync(username, password);
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            return await _authenticationService.ChangePasswordAsync(userId, currentPassword, newPassword);
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.PasswordHash = _authenticationService.HashPassword(newPassword);
                user.MustChangePassword = true;
                user.PasswordChanged = DateTime.Now;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Password reset for user {UserId}", userId);
                await _auditService.LogAsync(userId, "ResetPassword", "User", userId, "Password reset by administrator");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> ValidatePasswordAsync(string password)
        {
            return await Task.FromResult(_authenticationService.ValidatePasswordStrength(password));
        }

        public async Task<UserSession> CreateSessionAsync(int userId, string ipAddress, string userAgent)
        {
            try
            {
                var session = new UserSession
                {
                    UserId = userId,
                    SessionToken = Guid.NewGuid().ToString(),
                    LoginTime = DateTime.Now,
                    LastActivityTime = DateTime.Now,
                    ExpiryTime = DateTime.Now.AddMinutes(30),
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    Status = "نشط",
                    StatusFr = "Active"
                };

                _context.UserSessions.Add(session);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Session created for user {UserId}", userId);
                await _auditService.LogAsync(userId, "CreateSession", "UserSession", session.Id, "User session created");

                return session;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating session for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> EndSessionAsync(string sessionToken)
        {
            return await _authenticationService.LogoutAsync(sessionToken);
        }

        public async Task<bool> EndAllUserSessionsAsync(int userId)
        {
            try
            {
                var sessions = await _context.UserSessions
                    .Where(s => s.UserId == userId && s.Status == "نشط")
                    .ToListAsync();

                foreach (var session in sessions)
                {
                    session.EndSession();
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("All sessions ended for user {UserId}", userId);
                await _auditService.LogAsync(userId, "EndAllSessions", "User", userId, "All user sessions ended");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ending all sessions for user {UserId}", userId);
                return false;
            }
        }

        public async Task<UserSession?> GetSessionAsync(string sessionToken)
        {
            try
            {
                return await _context.UserSessions
                    .Include(s => s.User)
                    .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving session {SessionToken}", sessionToken);
                return null;
            }
        }

        public async Task<IEnumerable<UserSession>> GetUserSessionsAsync(int userId)
        {
            try
            {
                return await _context.UserSessions
                    .Where(s => s.UserId == userId)
                    .OrderByDescending(s => s.LoginTime)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sessions for user {UserId}", userId);
                return Enumerable.Empty<UserSession>();
            }
        }

        public async Task<bool> UpdateSessionActivityAsync(string sessionToken)
        {
            return await _authenticationService.RefreshSessionAsync(sessionToken);
        }

        public async Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                return await _context.UserPermissions
                    .Where(p => p.UserId == userId && p.IsGranted && p.IsActive)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving permissions for user {UserId}", userId);
                return Enumerable.Empty<UserPermission>();
            }
        }

        public async Task<bool> HasPermissionAsync(int userId, string module, string action)
        {
            try
            {
                return await _context.UserPermissions
                    .AnyAsync(p => p.UserId == userId && 
                                  p.Module == module && 
                                  p.Action == action && 
                                  p.IsGranted && 
                                  p.IsActive &&
                                  p.IsValid());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> GrantPermissionAsync(int userId, string permissionName, string module, string action)
        {
            try
            {
                var existingPermission = await _context.UserPermissions
                    .FirstOrDefaultAsync(p => p.UserId == userId && 
                                             p.Module == module && 
                                             p.Action == action);

                if (existingPermission != null)
                {
                    existingPermission.IsGranted = true;
                    existingPermission.GrantedDate = DateTime.Now;
                }
                else
                {
                    var permission = new UserPermission
                    {
                        UserId = userId,
                        PermissionName = permissionName,
                        Module = module,
                        Action = action,
                        IsGranted = true,
                        GrantedDate = DateTime.Now
                    };

                    _context.UserPermissions.Add(permission);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Permission granted to user {UserId}: {Module}.{Action}", userId, module, action);
                await _auditService.LogAsync(userId, "GrantPermission", "UserPermission", null, 
                    $"Permission granted: {module}.{action}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error granting permission to user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> RevokePermissionAsync(int userId, string permissionName)
        {
            try
            {
                var permission = await _context.UserPermissions
                    .FirstOrDefaultAsync(p => p.UserId == userId && p.PermissionName == permissionName);

                if (permission != null)
                {
                    permission.RevokePermission();
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Permission revoked from user {UserId}: {PermissionName}", userId, permissionName);
                    await _auditService.LogAsync(userId, "RevokePermission", "UserPermission", permission.Id, 
                        $"Permission revoked: {permissionName}");

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error revoking permission from user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> UpdatePermissionsAsync(int userId, IEnumerable<UserPermission> permissions)
        {
            try
            {
                // Remove existing permissions
                var existingPermissions = await _context.UserPermissions
                    .Where(p => p.UserId == userId)
                    .ToListAsync();

                _context.UserPermissions.RemoveRange(existingPermissions);

                // Add new permissions
                foreach (var permission in permissions)
                {
                    permission.UserId = userId;
                    _context.UserPermissions.Add(permission);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Permissions updated for user {UserId}", userId);
                await _auditService.LogAsync(userId, "UpdatePermissions", "User", userId, "User permissions updated");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating permissions for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> UpdateProfileAsync(int userId, string fullName, string? fullNameFr, string? email, string? phone)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.FullName = fullName;
                user.FullNameFr = fullNameFr;
                user.Email = email;
                user.Phone = phone;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Profile updated for user {UserId}", userId);
                await _auditService.LogAsync(userId, "UpdateProfile", "User", userId, "User profile updated");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> UpdatePreferencesAsync(int userId, string language, string theme, int sessionTimeout)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.PreferredLanguage = language;
                user.Theme = theme;
                user.SessionTimeout = sessionTimeout;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Preferences updated for user {UserId}", userId);
                await _auditService.LogAsync(userId, "UpdatePreferences", "User", userId, "User preferences updated");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating preferences for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> UpdateProfilePictureAsync(int userId, string picturePath)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.ProfilePicture = picturePath;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Profile picture updated for user {UserId}", userId);
                await _auditService.LogAsync(userId, "UpdateProfilePicture", "User", userId, "Profile picture updated");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile picture for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> LockUserAsync(int userId, int lockoutMinutes, string reason)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.IsLocked = true;
                user.LockoutEnd = DateTime.Now.AddMinutes(lockoutMinutes);
                user.LockoutReason = reason;

                await _context.SaveChangesAsync();

                _logger.LogInformation("User locked: {UserId} for {LockoutMinutes} minutes", userId, lockoutMinutes);
                await _auditService.LogAsync(userId, "LockUser", "User", userId, $"User locked: {reason}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error locking user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> UnlockUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.IsLocked = false;
                user.LockoutEnd = null;
                user.LockoutReason = null;
                user.FailedLoginAttempts = 0;

                await _context.SaveChangesAsync();

                _logger.LogInformation("User unlocked: {UserId}", userId);
                await _auditService.LogAsync(userId, "UnlockUser", "User", userId, "User unlocked");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unlocking user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> IsUserLockedAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                return user?.IsLocked == true && user.LockoutEnd > DateTime.Now;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user is locked {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> EnableTwoFactorAsync(int userId, string secret)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.TwoFactorEnabled = true;
                user.TwoFactorSecret = secret;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Two-factor authentication enabled for user {UserId}", userId);
                await _auditService.LogAsync(userId, "EnableTwoFactor", "User", userId, "Two-factor authentication enabled");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enabling two-factor authentication for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> DisableTwoFactorAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return false;

                user.TwoFactorEnabled = false;
                user.TwoFactorSecret = null;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Two-factor authentication disabled for user {UserId}", userId);
                await _auditService.LogAsync(userId, "DisableTwoFactor", "User", userId, "Two-factor authentication disabled");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disabling two-factor authentication for user {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> VerifyTwoFactorAsync(int userId, string code)
        {
            try
            {
                // TODO: Implement TOTP verification
                // This would use a library like OtpNet to verify the TOTP code
                await Task.CompletedTask;
                return true; // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying two-factor code for user {UserId}", userId);
                return false;
            }
        }

        public async Task<int> GetTotalUsersCountAsync()
        {
            try
            {
                return await _context.Users.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total users count");
                return 0;
            }
        }

        public async Task<int> GetActiveUsersCountAsync()
        {
            try
            {
                return await _context.Users.CountAsync(u => u.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active users count");
                return 0;
            }
        }

        public async Task<int> GetOnlineUsersCountAsync()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddMinutes(-30);
                return await _context.UserSessions
                    .CountAsync(s => s.LastActivityTime > cutoffTime && s.Status == "نشط");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting online users count");
                return 0;
            }
        }

        public async Task<IEnumerable<User>> GetRecentlyActiveUsersAsync(int count = 10)
        {
            try
            {
                var recentUserIds = await _context.UserSessions
                    .Where(s => s.Status == "نشط")
                    .OrderByDescending(s => s.LastActivityTime)
                    .Select(s => s.UserId)
                    .Distinct()
                    .Take(count)
                    .ToListAsync();

                return await _context.Users
                    .Where(u => recentUserIds.Contains(u.Id))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recently active users");
                return Enumerable.Empty<User>();
            }
        }

        public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return Enumerable.Empty<User>();

                return await _context.Users
                    .Where(u => u.FullName.Contains(searchTerm) ||
                               u.Username.Contains(searchTerm) ||
                               u.Email!.Contains(searchTerm))
                    .OrderBy(u => u.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching users with term {SearchTerm}", searchTerm);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<IEnumerable<User>> GetUsersByRoleAsync(string role)
        {
            try
            {
                return await _context.Users
                    .Where(u => u.Role == role && u.IsActive)
                    .OrderBy(u => u.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users by role {Role}", role);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<IEnumerable<User>> GetUsersByDepartmentAsync(string department)
        {
            try
            {
                return await _context.Users
                    .Where(u => u.Department == department && u.IsActive)
                    .OrderBy(u => u.FullName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users by department {Department}", department);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<bool> IsUsernameAvailableAsync(string username, int? excludeUserId = null)
        {
            try
            {
                var query = _context.Users.Where(u => u.Username == username);
                
                if (excludeUserId.HasValue)
                    query = query.Where(u => u.Id != excludeUserId.Value);

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking username availability {Username}", username);
                return false;
            }
        }

        public async Task<bool> IsEmailAvailableAsync(string email, int? excludeUserId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return true;

                var query = _context.Users.Where(u => u.Email == email);
                
                if (excludeUserId.HasValue)
                    query = query.Where(u => u.Id != excludeUserId.Value);

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking email availability {Email}", email);
                return false;
            }
        }

        public async Task<bool> ValidateUserDataAsync(User user)
        {
            try
            {
                if (user == null)
                    return false;

                if (string.IsNullOrWhiteSpace(user.Username))
                    return false;

                if (string.IsNullOrWhiteSpace(user.FullName))
                    return false;

                if (!await IsUsernameAvailableAsync(user.Username, user.Id))
                    return false;

                if (!string.IsNullOrEmpty(user.Email) && !await IsEmailAvailableAsync(user.Email, user.Id))
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating user data");
                return false;
            }
        }
    }
}
