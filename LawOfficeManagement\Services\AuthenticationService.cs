using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using LawOfficeManagement.Models;
using LawOfficeManagement.Utils;

namespace LawOfficeManagement.Services
{
    public class AuthenticationService
    {
        private readonly DatabaseService _databaseService;
        private readonly ErrorLogService _errorLogService;
        
        public AuthenticationService()
        {
            _databaseService = DatabaseService.Instance;
            _errorLogService = new ErrorLogService();
        }
        
        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new LoginResult { IsSuccess = false, ErrorMessage = "اسم المستخدم وكلمة المرور مطلوبان" };
                }
                
                // البحث عن المستخدم في قاعدة البيانات
                var query = @"
                    SELECT UserId, Username, PasswordHash, Salt, FullName, Email, Phone, Role, 
                           IsActive, FailedLoginAttempts, IsLocked, LockoutEnd, TwoFactorEnabled
                    FROM Users 
                    WHERE Username = @Username";
                
                var parameters = new[] { new SqlParameter("@Username", username) };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                
                if (dataTable.Rows.Count == 0)
                {
                    await LogFailedLoginAsync(username, "المستخدم غير موجود");
                    return new LoginResult { IsSuccess = false, ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }
                
                var row = dataTable.Rows[0];
                var user = new User
                {
                    UserId = (int)row["UserId"],
                    Username = row["Username"].ToString()!,
                    PasswordHash = row["PasswordHash"].ToString()!,
                    Salt = row["Salt"].ToString()!,
                    FullName = row["FullName"].ToString()!,
                    Email = row["Email"] as string,
                    Phone = row["Phone"] as string,
                    Role = Enum.Parse<UserRole>(row["Role"].ToString()!),
                    IsActive = (bool)row["IsActive"],
                    FailedLoginAttempts = (int)row["FailedLoginAttempts"],
                    IsLocked = (bool)row["IsLocked"],
                    LockoutEnd = row["LockoutEnd"] as DateTime?,
                    TwoFactorEnabled = (bool)row["TwoFactorEnabled"]
                };
                
                // التحقق من حالة المستخدم
                if (!user.IsActive)
                {
                    await LogFailedLoginAsync(username, "الحساب غير نشط");
                    return new LoginResult { IsSuccess = false, ErrorMessage = "الحساب غير نشط" };
                }
                
                if (user.IsLocked && user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.Now)
                {
                    await LogFailedLoginAsync(username, "الحساب مقفل");
                    return new LoginResult { IsSuccess = false, ErrorMessage = "الحساب مقفل مؤقتاً" };
                }
                
                // التحقق من كلمة المرور
                if (!VerifyPassword(password, user.PasswordHash, user.Salt))
                {
                    await IncrementFailedLoginAttemptsAsync(user.UserId);
                    await LogFailedLoginAsync(username, "كلمة مرور خاطئة");
                    return new LoginResult { IsSuccess = false, ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }
                
                // إعادة تعيين محاولات الدخول الفاشلة
                await ResetFailedLoginAttemptsAsync(user.UserId);
                
                // تحديث آخر تسجيل دخول
                await UpdateLastLoginAsync(user.UserId);
                
                // تسجيل نجاح تسجيل الدخول
                await LogSuccessfulLoginAsync(user.UserId);
                
                return new LoginResult { IsSuccess = true, User = user };
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("Authentication", ex, username);
                return new LoginResult { IsSuccess = false, ErrorMessage = "خطأ في النظام" };
            }
        }
        
        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                // الحصول على بيانات المستخدم الحالية
                var query = "SELECT PasswordHash, Salt FROM Users WHERE UserId = @UserId";
                var parameters = new[] { new SqlParameter("@UserId", userId) };
                var dataTable = await _databaseService.ExecuteQueryAsync(query, parameters);
                
                if (dataTable.Rows.Count == 0)
                {
                    return false;
                }
                
                var row = dataTable.Rows[0];
                var currentHash = row["PasswordHash"].ToString()!;
                var currentSalt = row["Salt"].ToString()!;
                
                // التحقق من كلمة المرور الحالية
                if (!VerifyPassword(currentPassword, currentHash, currentSalt))
                {
                    return false;
                }
                
                // إنشاء كلمة مرور جديدة
                var newSalt = GenerateSalt();
                var newHash = HashPassword(newPassword, newSalt);
                
                // تحديث كلمة المرور في قاعدة البيانات
                var updateQuery = @"
                    UPDATE Users 
                    SET PasswordHash = @PasswordHash, Salt = @Salt, ModifiedDate = @ModifiedDate 
                    WHERE UserId = @UserId";
                
                var updateParameters = new[]
                {
                    new SqlParameter("@PasswordHash", newHash),
                    new SqlParameter("@Salt", newSalt),
                    new SqlParameter("@ModifiedDate", DateTime.Now),
                    new SqlParameter("@UserId", userId)
                };
                
                var result = await _databaseService.ExecuteNonQueryAsync(updateQuery, updateParameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ChangePassword", ex, userId.ToString());
                return false;
            }
        }
        
        public async Task<bool> ResetPasswordAsync(string username, string newPassword)
        {
            try
            {
                // إنشاء كلمة مرور جديدة
                var salt = GenerateSalt();
                var hash = HashPassword(newPassword, salt);
                
                // تحديث كلمة المرور في قاعدة البيانات
                var query = @"
                    UPDATE Users 
                    SET PasswordHash = @PasswordHash, Salt = @Salt, ModifiedDate = @ModifiedDate,
                        FailedLoginAttempts = 0, IsLocked = 0, LockoutEnd = NULL
                    WHERE Username = @Username";
                
                var parameters = new[]
                {
                    new SqlParameter("@PasswordHash", hash),
                    new SqlParameter("@Salt", salt),
                    new SqlParameter("@ModifiedDate", DateTime.Now),
                    new SqlParameter("@Username", username)
                };
                
                var result = await _databaseService.ExecuteNonQueryAsync(query, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ResetPassword", ex, username);
                return false;
            }
        }
        
        private string HashPassword(string password, string salt)
        {
            using var sha256 = SHA256.Create();
            var saltedPassword = password + salt;
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashedBytes);
        }
        
        private bool VerifyPassword(string password, string hash, string salt)
        {
            var computedHash = HashPassword(password, salt);
            return computedHash == hash;
        }
        
        private string GenerateSalt()
        {
            using var rng = RandomNumberGenerator.Create();
            var saltBytes = new byte[32];
            rng.GetBytes(saltBytes);
            return Convert.ToBase64String(saltBytes);
        }
        
        private async Task IncrementFailedLoginAttemptsAsync(int userId)
        {
            try
            {
                var query = @"
                    UPDATE Users 
                    SET FailedLoginAttempts = FailedLoginAttempts + 1,
                        IsLocked = CASE WHEN FailedLoginAttempts >= 4 THEN 1 ELSE 0 END,
                        LockoutEnd = CASE WHEN FailedLoginAttempts >= 4 THEN DATEADD(MINUTE, 30, GETDATE()) ELSE NULL END
                    WHERE UserId = @UserId";
                
                var parameters = new[] { new SqlParameter("@UserId", userId) };
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("IncrementFailedLoginAttempts", ex, userId.ToString());
            }
        }
        
        private async Task ResetFailedLoginAttemptsAsync(int userId)
        {
            try
            {
                var query = @"
                    UPDATE Users 
                    SET FailedLoginAttempts = 0, IsLocked = 0, LockoutEnd = NULL 
                    WHERE UserId = @UserId";
                
                var parameters = new[] { new SqlParameter("@UserId", userId) };
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("ResetFailedLoginAttempts", ex, userId.ToString());
            }
        }
        
        private async Task UpdateLastLoginAsync(int userId)
        {
            try
            {
                var query = "UPDATE Users SET LastLogin = @LastLogin WHERE UserId = @UserId";
                var parameters = new[]
                {
                    new SqlParameter("@LastLogin", DateTime.Now),
                    new SqlParameter("@UserId", userId)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("UpdateLastLogin", ex, userId.ToString());
            }
        }
        
        private async Task LogSuccessfulLoginAsync(int userId)
        {
            try
            {
                var query = @"
                    INSERT INTO LoginHistory (UserId, LoginDate, IPAddress, UserAgent, IsSuccessful)
                    VALUES (@UserId, @LoginDate, @IPAddress, @UserAgent, 1)";
                
                var parameters = new[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@LoginDate", DateTime.Now),
                    new SqlParameter("@IPAddress", NetworkHelper.GetLocalIPAddress()),
                    new SqlParameter("@UserAgent", Environment.MachineName)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("LogSuccessfulLogin", ex, userId.ToString());
            }
        }
        
        private async Task LogFailedLoginAsync(string username, string reason)
        {
            try
            {
                var query = @"
                    INSERT INTO LoginHistory (UserId, LoginDate, IPAddress, UserAgent, IsSuccessful, FailureReason)
                    VALUES (NULL, @LoginDate, @IPAddress, @UserAgent, 0, @FailureReason)";
                
                var parameters = new[]
                {
                    new SqlParameter("@LoginDate", DateTime.Now),
                    new SqlParameter("@IPAddress", NetworkHelper.GetLocalIPAddress()),
                    new SqlParameter("@UserAgent", $"{Environment.MachineName} - {username}"),
                    new SqlParameter("@FailureReason", reason)
                };
                
                await _databaseService.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                await _errorLogService.LogErrorAsync("LogFailedLogin", ex, username);
            }
        }
    }
    
    public class LoginResult
    {
        public bool IsSuccess { get; set; }
        public User? User { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
